import datetime
import json

import requests
from loguru import logger

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("meta")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = "https://www.metacareers.com/graphql"
    headers = {
        "accept": "*/*",
        "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
        "content-type": "application/x-www-form-urlencoded",
        "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-asbd-id": "129477",
        "x-fb-friendly-name": "CareersJobSearchResultsQuery",
        "x-fb-lsd": "SvIXT9qqw4FDW7PsqqHQVp",
    }

    for keyword in search.keyword_list:
        payload = {
            "av": "228384200353986",
            "__user": "0",
            "__a": "1",
            "__req": "2",
            "__hs": "19740.BP%3ADEFAULT.2.0..0.0",
            "dpr": "1",
            "__ccg": "UNKNOWN",
            "__rev": "1010879600",
            "__s": "pyjy2e:pcdf0k:9e55en",
            "__hsi": "7325321931548236218",
            "__dyn": "7xeUmwkHgmwn8K2Wmhwn84a2i5U4e1Fx-ewSwMxW4E5S2WdwJw5ux60Vo1upE4W0OE2WxO2O1Vwooa85ufw5Zx61vw4iwBgao881FU2IzXw9S5ryE3bwkE5G0zE5W0HUvw4Jwp8oxa0YU2ZwrU6C0L836w8i6E3ew",
            "__csr": "",
            "fb_dtsg": "NAcMMcK5s4l4S8DfJ8swdFW7vkLZiQIG2xJaCgqF_8XjNoHn5Nr_exg:14:1705554989",
            "jazoest": "25480",
            "lsd": "SvIXT9qqw4FDW7PsqqHQVp",
            "__spin_r": "1010879600",
            "__spin_b": "trunk",
            "__spin_t": "1705559419",
            "__jssesw": "1",
            "fb_api_caller_class": "RelayModern",
            "fb_api_req_friendly_name": "CareersJobSearchResultsQuery",
            "variables": json.dumps({
                "search_input": {
                    "q": keyword,
                    "divisions": [],
                    "offices": ["North America"],
                    "roles": [],
                    "leadership_levels": ["Individual Contributor"],
                    "saved_jobs": [],
                    "saved_searches": [],
                    "sub_teams": [],
                    "teams": ["Software Engineering", "Data & Analytics", "Advertising Technology",
                              "Artificial Intelligence", "Business Development & Partnerships", "AR/VR",
                              "Infrastructure"],
                    "is_leadership": False,
                    "is_remote_only": False,
                    "sort_by_new": True,
                    "page": 1,
                    "results_per_page": None
                }
            }),
            "server_timestamps": "true",
            "doc_id": "9114524511922157",
        }
        referrer = (f"https://www.metacareers.com/jobs?leadership_levels[0]=Individual%20Contributor&offices["
                    f"0]=North%20America&q={keyword}&sort_by_new=true&teams[0]=Software%20Engineering&teams["
                    f"1]=Data%20%26%20Analytics&teams[2]=Advertising%20Technology&teams["
                    f"3]=Artificial%20Intelligence&teams[4]=Business%20Development%20%26%20Partnerships&teams["
                    f"5]=AR%2FVR&teams[6]=Infrastructure")
        referrer_policy = "origin-when-cross-origin"

        response = requests.post(
            url,
            headers=headers,
            data=payload,
            params={"referrer": referrer, "referrerPolicy": referrer_policy},
            cookies={"credentials": "include"},
        )
        base_url = 'https://www.metacareers.com/jobs/'
        if response.status_code == 200:
            json_response = response.json()
            jobs = json_response['data']['job_search']
            for job in jobs:
                job_title = job['title']
                try:
                    next(filter(lambda x: "Toronto" in x or "Dublin" in x or "Norway" in x, job["locations"]))
                    continue
                except:
                    if "," in job_title:
                        job_title = job_title.split(",")[0]
                    elif "(" in job_title:
                        job_title = job_title.split("(")[0]
                    job_id = job['id']
                    job_link = base_url + job_id
                    try:
                        job_loc = job["locations"][0]
                    except:
                        job_loc = "Remote"
                    if experience.whatIWant(job_title) and len(
                            db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                        job_state = search.get_job_location_by_job_link_location(job_loc)
                        if not job_state:
                            job_state = search.get_job_location_by_requests(job_loc)
                        job_loc = job_state
                        db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                                   "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                                   "location": f"{job_loc}"})

        else:
            logger.error(f"Request failed with status code: {response.status_code}")
