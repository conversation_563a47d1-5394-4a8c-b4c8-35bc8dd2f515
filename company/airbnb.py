import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen: bool):
    search = Base("airbnb")
    experience = search.experience
    db = search.db
    hide = search.hide
    company_name = search.name
    url = "https://careers.airbnb.com/positions/?_offices=united-states"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[class="facetwp-page next"]')))
    hide.hideHeadAndFoot(wd)
    hide.hideElement(wd, 'section')
    target = int(wd.find_element(By.CSS_SELECTOR, '[class="facetwp-page last"]').text)
    for _ in range(target):
        job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="text-size-4"]')
        for job in job_lists:
            job = job.find_element(By.CSS_SELECTOR, 'a')
            job_link = job.get_attribute("href")
            job_title = job.text
            if "," in job_title:
                job_title = job_title.split(",")[0]
            job_id = job_link.split("/")[-2]
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"Remote"})
        try:
            search.click_ele(wd, '[class="facetwp-page next"]')
            search.wait_x_sec(5)
        except:
            pass
