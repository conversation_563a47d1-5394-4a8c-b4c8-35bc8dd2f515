import datetime
import time

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("cisco")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f'https://jobs.cisco.com/jobs/SearchJobs/{keyword}?21178=%5B169482%5D&21178_format=6020&listFilterMode=1&projectOffset=0'
        wd.get(url)
        search.numbers_present_in_element(wd, '[class="pagination_legend"]')
        try:
            search.click_ele(wd, '[id="js-calculateTotal"]')
        except:
            pass
        target = 1
        try:
            target = (int(
                wd.find_element(By.CSS_SELECTOR, '[class="pagination_legend"]').text.split(" ")[-1])) // 25
        except:
            pass
        for _ in range(target):
            job_lists = wd.find_element(By.CSS_SELECTOR, 'tbody').find_elements(By.CSS_SELECTOR,
                                                                                '[data-th="Job Title"]')
            for job in job_lists:
                job = job.find_element(By.CSS_SELECTOR, 'a')
                job_title = job.text
                job_link = job.get_attribute("href")
                job_id = job_link.split("/")[-1]
                job_loc = job.find_element(By.XPATH, "../..//td[@data-th='Location']").text
                if "US" not in job_loc: continue
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})

            try:
                wd.find_elements(By.CSS_SELECTOR, '[class="pagination_item"]')[-1].click()
                time.sleep(5)
            except:
                pass
