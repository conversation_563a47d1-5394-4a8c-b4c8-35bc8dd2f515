import datetime
import time

from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("chase")
    experience = search.experience
    db = search.db
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    company_name = search.name
    for keyword in search.keyword_list:
        url = f"https://jpmc.fa.oraclecloud.com/hcmUI/CandidateExperience/en/sites/CX_1001/requisitions?keyword={keyword}&lastSelectedFacet=POSTING_DATES&location=United+States&locationId=300000000289738&locationLevel=country&mode=location&selectedPostingDatesFacet=7"
        wd.get(url)
        WebDriverWait(wd, 10).until(
            lambda driver: search.numbers_present_in_element(driver, '[class="search-filters__counter"]'))
        idx = int(wd.find_element(By.CSS_SELECTOR, '[class="search-filters__counter"]').text.split()[0]) // 25
        body = wd.find_element(By.TAG_NAME, "body")
        while idx:
            body.send_keys(Keys.END)
            idx -= 1
            time.sleep(2)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[class="job-grid-item__link"]')))
        job_lists = wd.find_elements(By.CSS_SELECTOR, '[data-qa="searchResultItem"]')
        for job in job_lists:
            job_title = job.find_element(By.CSS_SELECTOR, '[class="job-tile__title"]').text
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href").split("?keyword=")[0]
            job_id = job_link.split("/")[9]
            job_loc = job.find_element(By.CSS_SELECTOR, '[data-bind="html: primaryLocation"]').text
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
