from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://intel.wd1.myworkdayjobs.com/en-US/External/?redirect=/External/job/&workerSubType=dc8bf79476611087dfde9c6b065bae76&locations=1e4a4eb3adf101cc4e292078bf8199d0&locations=1e4a4eb3adf101770f350977bf8193cf&locations=1e4a4eb3adf1011246675c76bf81f8ce&locations=9225dd5a24931001586f2d984f1e0000&locations=1e4a4eb3adf1016541777876bf8111cf&locations=1e4a4eb3adf10146fd5c5276bf81eece&locations=719d3d221adf0101611f7e0dfefb0000&locations=1e4a4eb3adf10155d1cc0778bf8180d0&locations=1e4a4eb3adf101d4e5a61779bf8159d1&locations=1e4a4eb3adf10118b1dfe877bf8162d0&locations=1e4a4eb3adf10129d05fe377bf815dd0&locations=1e4a4eb3adf1019ec42cde77bf8158d0&locations=1e4a4eb3adf1013ddb7bd877bf8153d0&locations=1e4a4eb3adf1018c4bf78f77bf8112d0&locations=1e4a4eb3adf101d3aea90377bf818ecf&locations=1e4a4eb3adf101b8aec18a77bf810dd0&jobFamilyGroup=ace7a3d23b7e01a0544279031a0ec85c&jobFamilyGroup=dc8bf79476611087d67b2cccdde47034&jobFamilyGroup=6fbee4bf2e511000c6fd6a78c26e0000&jobFamilyGroup=a55ea4dd831d1000c6fce5a0c4d30000&jobFamilyGroup=f31a130ea31c1000c6fd81ebbafd0000&jobFamilyGroup=dc8bf79476611087d67b67d653eb703e"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
