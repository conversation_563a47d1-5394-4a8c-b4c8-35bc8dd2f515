import datetime

from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("microsoft")
    experience = search.experience
    db = search.db
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    company_name = search.name
    for keyword in search.keyword_list:
        url = f"https://jobs.careers.microsoft.com/global/en/search?q={keyword}&lc=United%20States&rt=Individual%20Contributor&et=Full-Time&l=en_us&pg=1&pgSz=20&o=Recent&flt=true"
        wd.get(url)
        WebDriverWait(wd, 10).until(EC.visibility_of_element_located(
            (By.XPATH, '//*[@id="job-search-app"]/div/div[2]/div/div[1]/div/div[1]/h1')))
        try:
            search.hide.hideElement(wd, '[id="headerArea"]')
            search.hide.hideElement(wd, '[aria-label="Feedback"]')
            search.hide.hideElement(wd, '[class="ms-MessageBar ms-MessageBar-multiline ms-gcs-banner root-93"]')
        except:
            pass
        target = min(int(wd.find_element(By.XPATH, '//*[@id="job-search-app"]/div/div[2]/div/div[1]/div/div[1]/h1')
                         .text.split()[-2]) // 20 - 1, 10)
        for _ in range(target // 2 + 1):
            try:
                WebDriverWait(wd, 10).until(EC.visibility_of_element_located((By.CLASS_NAME, "ms-List-cell")))
            except:
                continue
            for job in wd.find_elements(By.CSS_SELECTOR, '[class="ms-List-cell"]'):
                ActionChains(wd).move_to_element(job).click(job).perform()
                search.wait_x_sec(4)
                job_link = wd.current_url
                job_title = job_link.split("/")[-1].replace("-", " ").replace("%20", " ")
                job_id = job_link.split("/")[-2]
                if job_id == 'en':
                    continue
                job_loc = wd.find_element(By.XPATH,
                                          '//*[@id="job-search-app"]/div/div[2]/div/div/div/div[2]/div/p').text
                if "Multiple Locations" in job_loc:
                    job_loc = 'Remote'
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[aria-label="Go to next page"]')
                search.wait_x_sec(4)
            except:
                pass
