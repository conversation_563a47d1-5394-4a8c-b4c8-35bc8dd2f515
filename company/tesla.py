import datetime
import os
import signal

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("tesla")
    wd = search.wd.driver_chrome_with_profile(search.name)
    browser_pid = wd.browser_pid
    experience = search.experience
    db = search.db
    company_name = search.name
    try:
        for keyword in search.keyword_list:
            url = f"https://www.tesla.com/careers/search/?query={keyword}&type=1&region=5&site=US"
            wd.get(url)
            search.wait_x_sec(4)
            idx = 5
            while idx > 0:
                idx -= 1
                wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                search.wait_x_sec(2)
            job_lists = wd.find_element(By.CSS_SELECTOR, 'ol[class="tds-list"]').find_elements(By.CSS_SELECTOR, 'li')
            for job in job_lists:
                try:
                    job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
                except:
                    continue
                job_id = job_link.split("/")[-1].split('-')[-1]
                job_title = job.find_element(By.CSS_SELECTOR, 'a').text
                job_loc = job.text.split("\n")[-2]
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
    finally:
        wd.quit()
        os.kill(int(browser_pid), signal.SIGTERM)
