import datetime

from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("snowflake")
    experience = search.experience
    db = search.db
    company_name = search.name
    hide = search.hide
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    url = "https://careers.snowflake.com/us/en/search-results"
    wd.get(url)
    WebDriverWait(wd, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, '[data-ph-at-text="Region"]')))
    hide.hideElement(wd, '[class="ph-header"]')
    try:
        hide.hideElement(wd, '[id="chatbotWidget"]')
    except:
        pass
    search.click_ele(wd, '[data-ph-at-text="Region"]')
    next(filter(lambda x: x.text.startswith("Americas"),
                wd.find_elements(By.CSS_SELECTOR, '[class="result-text"]'))).click()
    for keyword in search.keyword_list:
        search_ele = wd.find_element(By.CSS_SELECTOR, '[placeholder="Search from below list"]')
        search_ele.clear()
        search_ele.send_keys(keyword)
        search_ele.send_keys(Keys.ENTER)
        search.wait_x_sec(2)
        search.numbers_present_in_element(wd, '[class="result-count"]')
        result = min(int(wd.find_element(By.CSS_SELECTOR, '[class="result-count"]').text) // 10, 15)
        for _ in range(result - 1):
            for job in wd.find_element(By.CSS_SELECTOR, '[class="phs-jobs-list"]').find_elements(By.CSS_SELECTOR,
                                                                                                 '[class="jobs-list-item"]'):
                job = job.find_element(By.CSS_SELECTOR, 'a')
                job_loc = job.get_attribute('data-ph-at-job-location-text')
                if "United States" not in job_loc: continue
                job_link = job.get_attribute('href')
                job_title = job.get_attribute('data-ph-at-job-title-text')
                job_id = job.get_attribute('data-ph-at-job-id-text')
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[data-ph-tevent-attr-trait214="Next"]')
                search.wait_x_sec(2)
            except:
                pass
