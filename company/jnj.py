import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("jnj")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://jobs.jnj.com/en/jobs/?search={keyword}&country=United+States&pagesize=50#results"
        wd.get(url)
        try:
            search.numbers_present_in_element(wd, '[class="job-count"]')
            target = max((int(wd.find_element(By.CSS_SELECTOR, '[class="job-count"]').text.split(" ")[-3])) // 50, 1)
        except:
            search.expect_shown_ele(wd, '[class="lead"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="lead"]').text.startswith("Sorry"):
                continue
        for _ in range(target):
            try:
                search.hide.hideHeadAndFoot(wd)
                search.hide.hideElement(wd, '[class="cookieBar"]')
            except:
                pass
            job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="card-body"]')
            for job in job_lists:
                job_title = job.find_element(By.CSS_SELECTOR,
                                             '[class="card-job-actions js-job"]').get_attribute("data-jobtitle")
                job_link = job.find_element(By.CSS_SELECTOR, '[class="card-title"]') \
                    .find_element(By.CSS_SELECTOR, 'a').get_attribute("href")
                job_id = job_link.split("/")[-3]
                job_loc = \
                    job.find_element(By.CSS_SELECTOR, '[class="list-inline job-meta"]').find_elements(
                        By.CSS_SELECTOR, '[class="list-inline-item"]')[-1].text
                if "United States" not in job_loc or "co-op" in job_title:
                    continue
                if ";" in job_loc:
                    job_loc = job_loc.split(";")[0]
                job_loc = job_loc.split(",")[0].replace(" ", "-")
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid",
                                where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})

            try:
                search.click_ele(wd, '[aria-label="Next page"]')
                search.wait_x_sec(4)
            except:
                if target:
                    search.logger.error(f"{company_name}, next page click error")
