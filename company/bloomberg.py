import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("bloomberg")
    experience = search.experience
    db = search.db
    company_name = search.name
    idx = 2
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://bloomberg.avature.net/careers/SearchJobs/{keyword}"
        wd.get(url)
        WebDriverWait(wd, 15).until(
            lambda driver: search.numbers_present_in_element(driver, '[class="list-controls__text__legend"]'))
        target = int(wd.find_element(By.CSS_SELECTOR, '[class="list-controls__text__legend"]').get_attribute(
            'aria-label').split()[0]) // 12 + 1
        for _ in range(target):
            job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="article__header__text"]')
            for job in job_lists:
                job_title = job.find_element(By.CSS_SELECTOR, 'a').text
                job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href")
                job_id = job_link.split("/")[-1]
                job_loc = job.find_element(By.CSS_SELECTOR, '[class="list-item-location"]').text
                if "United States" not in job_loc: continue
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid",
                                where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})

            try:
                search.click_ele(wd, f'[aria-label="Go to Next Page, Number {idx}"]')
                idx += 1
                search.wait_x_sec(4)
            except:
                pass
