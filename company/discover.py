from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://discover.wd5.myworkdayjobs.com/Discover?locations=dd81500376e3100d308a693ac9334f50&locations=dd81500376e3100d308a8c0d13cb4f9b&locations=dd81500376e3100d308a6b7c6c334f55&locations=dd81500376e3100d308a95535c334faf&locations=dd81500376e3100d308aac627f534fe1&locations=dd81500376e3100d308aa55cc4934fd2&locations=dd81500376e3100d308a8e6d79cb4fa0&locations=dd81500376e3100d308aa2fa011b4fcd&locations=dd81500376e3100d308a9c1de2b34fbe&locations=dd81500376e3100d308a6dcac9034f5a&locations=dd81500376e3100d308a725565eb4f64&locations=febd73641484109bf1faaa7e997a66c3&locations=dd81500376e3100d308a99d97cab4fb9&locations=dd81500376e3100d308aa0a8d1a34fc8&locations=59de7feb9e0e01f2129d6909e82a0eb0&locations=ae914e0d596801c955d0a23a7a0bb72b&locations=96bf16fa269701eb4279010ddf93b1e3"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
