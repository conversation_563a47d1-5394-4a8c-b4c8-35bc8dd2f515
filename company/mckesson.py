from module.companyModule import companyModule as Base


def findIt(seen):
    url = "https://mckesson.wd3.myworkdayjobs.com/en-US/External_Careers?redirect=/External_Careers/job/&Location_Country=bc33aa3152ec42d4995f4791a106ed09"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
