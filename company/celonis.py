import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("celonis")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = f"https://job-boards.greenhouse.io/embed/job_board?for={company_name}"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    search.wait_x_sec(4)
    try:
        target = int(wd.find_element(By.CSS_SELECTOR, '[aria-label="Pagination"]')
                     .find_element(By.CSS_SELECTOR, 'ul').find_elements(By.CSS_SELECTOR, 'li')[-1].text)
    except:
        target = 1
    for _ in range(target):
        search.expect_shown_ele(wd, '[class="cell"]')
        job_list = wd.find_elements(By.CSS_SELECTOR, '[class="cell"]')
        for job in job_list:
            job_title, job_loc = [x.text for x in job.find_elements(By.CSS_SELECTOR, 'p')]
            try:
                tag_text = job.find_element(By.CSS_SELECTOR, '[class="ellipse"]').text
                job_title = job_title.replace(tag_text, '')
            except:
                pass
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
            job_id = job_link.split('/')[-1].split("=")[1].split("&")[0]
            job_link = f'https://job-boards.greenhouse.io/embed/job_app?for={company_name.lower()}&token={job_id}'
            if any(x in job_loc for x in ["India", "United Kingdom", "Spain", "Germany", "Kosovo", "France"]):
                continue
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
        try:
            search.click_ele(wd, '[aria-label="Next page"]')
            search.wait_x_sec(4)
        except:
            pass
