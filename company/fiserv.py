from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://fiserv.wd5.myworkdayjobs.com/en-US/EXT?workerSubType=9845595a26eb1060bff54587bc2d8f8e&timeType=f1fcabeda1b8105f92d4b58bb84d3c44&locations=9845595a26eb10611629c7c489c53e82&locations=9845595a26eb1061162a197029553efa&locations=9845595a26eb1061162a1c4369ad3eff&locations=9845595a26eb1061162a1fa8c0753f04&locations=9845595a26eb1061162a2247d5553f09&locations=9845595a26eb1061162a25bd77fd3f10&locations=9845595a26eb1061162a2907f3c53f17&locations=9845595a26eb1061162adc8c2cbd3f1d&locations=9845595a26eb10611629cb2d28653e87&locations=9845595a26eb1061162adffaedad3f22&locations=9845595a26eb1061162ae280ecad3f27&locations=9845595a26eb1061162ae5cdf4cd3f2c&locations=9845595a26eb10611629a73491f53e4b&locations=9845595a26eb10611629ce1fef0d3e8c&locations=9845595a26eb1061162ae852b37d3f31&locations=9845595a26eb1061162aeb0f18453f36&locations=9845595a26eb1061162aee6f5e2d3f3b&locations=9845595a26eb1061162af16b67ed3f40&locations=298bcd268d03014c7e8954b27c2aa3fe&locations=9845595a26eb1061162af4f02d953f45&locations=2acf2f683f7d0144af34716838548aeb&locations=9845595a26eb1061162e37bed35d42e4&locations=9845595a26eb10611629d68695753e9b&locations=9845595a26eb10611629adac64953e55&locations=9845595a26eb10611629d3ea0ced3e96&locations=9845595a26eb10611629aa5a3fdd3e50&locations=9845595a26eb1061162a0d834e153ef5&locations=a1ed7448ce5b01105d5395d956490000&locations=87f2390e89b001b53c37208f1f1b6f68&locations=2acf2f683f7d013032557e67385430eb&locations=2acf2f683f7d013b47326d6738542aeb&locations=2acf2f683f7d01a14f594e663854c4ea&locations=2acf2f683f7d0141bb4a2c693854d1eb&locations=9845595a26eb1061162e2e7e66f542cd&locations=3c28161b7c4a01d6d5732a663f5b14c4&locations=59c5e1b17068101816e904b4ff38da9d&locations=63a84f4adfba01a869aee7f25f24e90a&locations=2acf2f683f7d01656ec53568385472eb&locations=2acf2f683f7d01e135d6b06b385487ec&locations=2acf2f683f7d01134494b8663854e8ea&jobFamilyGroup=21bcd77223041071dff3e2dd2d6cfbe0&jobFamilyGroup=c6b68e57f5a3108c3d6d14a3b2fc364e&jobFamilyGroup=c6b68e57f5a3108c3d89bc494834372e&jobFamilyGroup=c6b68e57f5a3108c3da4397d1eec3825&jobFamilyGroup=c6b68e57f5a3108c44f805c8e51cad33&jobFamilyGroup=c6b68e57f5a3108c45719c1a50046d3a&jobFamilyGroup=c6b68e57f5a3108c4614399740ec75a9"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
