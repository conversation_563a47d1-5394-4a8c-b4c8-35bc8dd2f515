from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://zillow.wd5.myworkdayjobs.com/Zillow_Group_External/?locations=76d84517207210c0ae0423975342e5e5&locations=bf3166a9227a01f8b514f0b00b147bc9"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
