import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base('gusto')
    experience = search.experience
    db = search.db
    company_name = search.name
    url = "https://job-boards.greenhouse.io/gusto?offices%5B%5D=4645&offices%5B%5D=523&offices%5B%5D=81641&offices%5B%5D=63926&offices%5B%5D=59937"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    search.expect_shown_ele(wd, '[class="cell"]')
    job_list = wd.find_elements(By.CSS_SELECTOR, '[class="cell"]')
    for job in job_list:
        job_title, job_loc = job.text.split("\n")
        try:
            tag_text = job.find_element(By.CSS_SELECTOR, '[class="ellipse"]').text
            job_title = job_title.replace(tag_text, '')
        except:
            pass
        job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
        job_id = job_link.split('/')[-1].split("?")[0]
        if ";" in job_loc:
            job_loc = job_loc.split(";")[0]
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
