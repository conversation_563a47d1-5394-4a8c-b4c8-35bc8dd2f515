import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("oracle")
    experience = search.experience
    db = search.db
    hide = search.hide
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://eeho.fa.us2.oraclecloud.com/hcmUI/CandidateExperience/en/sites/jobsearch/requisitions?keyword={keyword}&lastSelectedFacet=AttributeChar6&location=United+States&locationId=300000000149325&locationLevel=country&mode=location&selectedFlexFieldsFacets=%22AttributeChar6%7C3+to+5%2B+years%3B0+to+2%2B+years%22&selectedLocationsFacet=300000000149325&selectedPostingDatesFacet=31%3B7"
        wd.get(url)
        WebDriverWait(wd, 20).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[class="search-pagination"]')))
        hide.hideElement(wd, '[class="talent-community-tile text-color-secondary talent-community-tile--tile"]')
        hide.hideElement(wd, '[class="cc-section__content"]')
        hide.hideElement(wd, '[class="cc-section__content"]')
        try:
            flag = wd.find_element(By.CSS_SELECTOR, '[class="search-pagination"]')
        except:
            wd.refresh()
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[class="search-pagination"]')))
            flag = wd.find_element(By.CSS_SELECTOR, '[class="search-pagination"]')
        try:
            while flag:
                search.click_ele(wd, '[class="search-pagination"]')
                search.wait_x_sec(2)
                flag = wd.find_element(By.CSS_SELECTOR, '[class="search-pagination"]')
        except:
            pass
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[class="job-grid-item__link"]')))
        job_lists = wd.find_elements(By.CSS_SELECTOR, '[data-qa="searchResultItem"]')
        for job in job_lists:
            job_title = job.find_element(By.CSS_SELECTOR, '[class="job-tile__title"]').text
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href").split("?keyword=")[0]
            job_id = job_link.split("/")[9]
            job_loc = job.find_element(By.CSS_SELECTOR, '[data-bind="html: primaryLocation"]').text
            if job_loc == 'United States': job_loc = 'Remote'
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
