import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("datadog")
    experience = search.experience
    db = search.db
    hide = search.hide
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://careers.datadoghq.com/all-jobs/?s={keyword}&region_Americas%5B0%5D=Americas"
        wd.get(url)
        search.numbers_present_in_element(wd, '[class="ais-Stats-text"]')
        hide.hideElement(wd, '[class="careers-nav-wrapper"]')
        target = (int(wd.find_element(By.CSS_SELECTOR, '[class="ais-Stats-text"]').text.split(" ")[0])) // 10
        for _ in range(target):
            job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="ais-Hits-item"]')
            for job in job_lists:
                job = job.find_element(By.CSS_SELECTOR, '[class="tw-text-black"]')
                job_title = job.find_element(By.CSS_SELECTOR, '[class="job-card-title"]').text
                job_link = job.get_attribute("href")
                job_id = job_link.split("/")[-2]
                job_loc = job.find_element(By.CSS_SELECTOR, '[class="job-card-location"]').text
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})

            try:
                search.click_ele(wd,
                                 '[class="ais-Pagination-item untranslated paginator-page ais-Pagination-item--nextPage"]')
                search.wait_x_sec(3)
            except:
                pass
