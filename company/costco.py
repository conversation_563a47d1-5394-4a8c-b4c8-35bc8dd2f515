import datetime

from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("costco")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = "https://phf.tbe.taleo.net/phf02/ats/careers/v2/jobSearch?org=COSTCO&cws=41"
        wd.get(url)
        WebDriverWait(wd, 10).until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, '[name="keywords"]')))
        wd.find_element(By.CSS_SELECTOR, '[name="keywords"]').send_keys(keyword)
        search.click_ele(wd, '[class="btn btn-primary oracletaleocwsv2-btn-fa fa-search"]')
        target = (int(wd.find_element(By.CSS_SELECTOR, '[class="oracletaleocwsv2-panel-number"]').text)) // 10
        for _ in range(target):
            wd.find_element(By.CSS_SELECTOR, 'body').send_keys(Keys.END)
            search.wait_x_sec(3)
        job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="oracletaleocwsv2-head-title"]')
        for job in job_lists:
            job = job.find_element(By.CSS_SELECTOR, 'a')
            job_title = job.text
            job_link = job.get_attribute("href").replace("viewRequisition", "applyRequisition")
            job_id = job_link.split("=")[-1]
            job_loc = job.find_elements(By.XPATH, "../..//div")[-1].text
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
