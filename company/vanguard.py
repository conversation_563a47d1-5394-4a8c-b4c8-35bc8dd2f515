from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://vanguard.wd5.myworkdayjobs.com/en-US/vanguard_external/?locations=b9f1251c6ce51052658909ff90c698c2&locations=b9f1251c6ce510526589ff2124bf999d&locations=db933e774fac016a8519278cc900cca7&locations=b9f1251c6ce51052658a21ca195399c1&locations=b9f1251c6ce510526589f89c5cca9995&locations=b9f1251c6ce51052658919a6dcd898d1&locations=8e37a72bac3e01015ddf14c7babc0000&locations=41ffcd54c0a80156ae317da90a117487&locations=b9f1251c6ce510526588e22b87f7989f&locations=b9f1251c6ce51052658a04b9e5c099a5&locations=b9f1251c6ce51052658a6756f75899f8"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
