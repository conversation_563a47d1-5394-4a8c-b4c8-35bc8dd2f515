import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("uber")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://www.uber.com/us/en/careers/list/?query={keyword}&location=USA-California-San%20Francisco&location=USA-California-Sunnyvale&location=USA-California-Los%20Angeles&location=USA-California-Palo%20Alto&location=USA-California-Santa%20Monica&location=USA-California-Oakland&location=USA-California-Sacramento&location=USA-California-San%20Jose&location=USA-California-Laguna%20Beach&location=USA-Washington-Seattle&location=USA-California-Burbank&location=USA-Arizona-Phoenix"
        wd.get(url)
        WebDriverWait(wd, 10).until(
            lambda driver: search.numbers_present_in_element(driver, '[class="css-cRJClr"]'))
        show_more = len(wd.find_elements(By.CSS_SELECTOR, '[class="css-hUNeqW"]')) > 0
        idx = 10
        while show_more and idx:
            search.click_ele(wd, '[class="css-hUNeqW"]')
            search.wait_x_sec(3)
            show_more = len(wd.find_elements(By.CSS_SELECTOR, '[class="css-hUNeqW"]')) > 0
            idx -= 1
        job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="css-bnia-Dv"]')[3] \
            .find_elements(By.CSS_SELECTOR, '[class="css-dCwqLp"]')
        for job in job_lists:
            try:
                if job.find_element(By.CSS_SELECTOR, 'a'):
                    job = job.find_element(By.CSS_SELECTOR, 'a')
                    job_title = job.text
                    job_link = job.get_attribute('href')
                    job_id = job_link.split("/")[-1]
                    job_loc = job.find_element(By.XPATH, '../../..').text.split("\n")[-1]
                    if job_loc.startswith('Multiple'):
                        job_loc = "Remote"
                    if experience.whatIWant(job_title) and len(
                            db.read(columns="jobid",
                                    where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                        job_state = search.get_job_location_by_job_link_location(job_loc)
                        if not job_state:
                            job_state = search.get_job_location_by_requests(job_loc)
                        job_loc = job_state
                        db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                                   "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                                   "location": f"{job_loc}"})
            except:
                pass
