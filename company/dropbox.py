import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("dropbox")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = f"https://jobs.dropbox.com/all-jobs"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    WebDriverWait(wd, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, '[class="open-positions__listing-title"]')))
    job_list = wd.find_element(By.CSS_SELECTOR, '[class="open-positions__container"]') \
        .find_elements(By.CSS_SELECTOR, 'li')
    for job in job_list:
        job_loc = job.get_attribute("data-location")
        if "US" not in job_loc:
            continue
        job = job.find_element(By.CSS_SELECTOR, 'a')
        job_title = job.find_element(By.CSS_SELECTOR, '[class="open-positions__listing-title"]').text
        job_link = job.get_attribute("href")
        job_id = job_link.split("/")[-1]
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid",
                        where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
