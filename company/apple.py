import datetime
import time

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("apple")
    experience = search.experience
    db = search.db
    hide = search.hide
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    company_name = search.name
    for keyword in search.keyword_list:
        url = f"https://jobs.apple.com/en-us/search?search={keyword}&sort=newest&key=software&location=united-states-USA"
        wd.get(url)
        WebDriverWait(wd, 10).until(
            lambda driver: search.numbers_present_in_element(driver, '[data-autom="paginationTotalPages"]'))
        target = min((int(wd.find_element(By.CSS_SELECTOR, '[data-autom="paginationTotalPages"]').text)), 10)
        for _ in range(target):
            job_lists = wd.find_element(By.CSS_SELECTOR, '[id="search-job-list"]').find_elements(By.CSS_SELECTOR,
                                                                                                 '[class="rc-accordion-button"]')
            for job in job_lists:
                job_info = job.text.split("\n")
                job_title = job_info[0]
                job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href")
                job_id = job_link.split("/")[-2]
                job_loc = job_info[-1]
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[aria-label="Next Page"]')
                time.sleep(3)
            except:
                pass
