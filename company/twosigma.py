import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("twosigma")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://careers.twosigma.com/careers/OpenRoles/{keyword}?5084=%5B16718352%2C16718354%2C16718355%2C16718356%2C16718358%2C16718357%5D&5084_format=3147&listFilterMode=1&jobRecordsPerPage=10&"
        wd.get(url)
        search.hide.hideHeadAndFoot(wd)
        search.numbers_present_in_element(wd, '[class="results__panel"]')
        job_lists = wd.find_element(By.CSS_SELECTOR, '[class="results__panel"]').find_elements(By.CSS_SELECTOR,
                                                                                               '[class="link"]')
        for job in job_lists:
            job_title = job.text
            job_link = job.get_attribute('href')
            job_id = job_link.split("/")[-1]
            job_loc = job.find_element(By.XPATH, '../..').text.split("\n")[1]
            if "-Campus-Hire" in job_link:
                continue
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
