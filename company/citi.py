# -*- coding: utf-8 -*-
# @Time  : 2023/2/20 下午2:33
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: citi.py
# @Software: PyCharm

from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://citi.wd5.myworkdayjobs.com/2?Country_and_Jurisdiction=bc33aa3152ec42d4995f4791a106ed09"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
