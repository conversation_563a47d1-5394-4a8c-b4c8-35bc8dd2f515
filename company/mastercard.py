from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://mastercard.wd1.myworkdayjobs.com/CorporateCareers?&ocations=6c91c6e8c8571086f3a1393157418daf&locations=67f224e9b8034c42bb29c44e7a02e387&locations=1a84f3e1633f010a0676f276c51c7a5b&locations=5ee74eca37db01224bf3395b8f0a9211&locations=0fb5e8f6a04a4a3ba7b2b1bf69d67428&locations=b8b0e6ca15354e24a5c8ba028e754c51&locations=a52da795b81f100109d635958d730000&locations=8eab563831bf10acbd2183c3090f1a20&locations=efacbd97fd67011dbc0f236fbb007b75&locations=8eab563831bf10acbcb35b24fcd616d7&locations=8eab563831bf10acbbdb817a833d0fa6&locations=a973e7cb5edf4c629de8fb48e2c1d880&locations=21900bbfa5d5488385df4c0929ad6598&locations=693cf272d15941e4bb7553073cbeff00&locations=977ef70c7cd14267b672b3d06d95b75c&locations=8eab563831bf10acbba0ec70e0f00d06&locations=c9c1221064f54d28ae6ab893b99923cf&locations=8eab563831bf10acb7f44286d1feeaf0&locations=3b2ad45f5eca10bd534be5fa946139c3&locations=8eab563831bf10acb54f0f21651ed219&locations=0b5759f321ea461fb27583cc1cc81b27&locations=80aa411711380141cd0cd42ebb01d76e&locations=85482cdd21931001b922354af3a90000&locations=348512b0397c47db99714e73afe39540"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
