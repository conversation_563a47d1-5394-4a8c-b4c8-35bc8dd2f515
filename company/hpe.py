from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://hpe.wd5.myworkdayjobs.com/en-US/Jobsathpe?locations=f607629c2aa10196b908a08e462c0567&locations=cc3bff87cf8101bded44c0f2dc00e375&locations=830a7b3bc18701132fdd1939d82b313f&locations=e459dee6bf0a01607d49d56409244b64&locations=47385449f64a01b916dd4870db2b6753&locations=acac3e510b17018557192527de2ba663&locations=62b0c47c914b014082b5023804240241&locations=bb84e32ded88013ee847c281d92b6745&locations=90058e87ba0e1001d2f4d7e27b0d0000&locations=bb84e32ded8801554f1e5500d92b8542&locations=e459dee6bf0a01cbf9be904907245b56&locations=47385449f64a018f7ca4ab09dc2b8957&locations=acac3e510b17018644380803dc2b7f55&locations=acac3e510b1701fbb44e1402dc2b7a55&locations=62b0c47c914b01cabae70b6907249e55&locations=bb84e32ded88017f2a05da01dc2b4656&locations=2e0d9cc9554b01761ed1696d442cbe56&locations=62b0c47c914b015d3c97fb6707248955&locations=acac3e510b1701630be274ffdb2b6d55&locations=e459dee6bf0a0154fcd8eb4307242956&locations=2e0d9cc9554b01deafe88869442cb356&locations=47385449f64a018cbe927302dc2b5b57&locations=830a7b3bc18701c4f679da02dc2b0c58&locations=47385449f64a017d4c903202dc2b5657&locations=acac3e510b1701ca09453efbdb2b5855"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
