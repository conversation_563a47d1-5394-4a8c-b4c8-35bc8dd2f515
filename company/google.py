import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base('google')
    hide = search.hide
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://www.google.com/about/careers/applications/jobs/results/?distance=50&hl=en_US&jlo=en_US&location=United%20States&q={keyword}&employment_type=FULL_TIME&target_level=EARLY&target_level=MID&target_level=INTERN_AND_APPRENTICE"
        wd.get(url)
        search.numbers_present_in_element(wd, '[class="SWhIm"]')
        hide.hideElement(wd, 'header')
        target = min(max(int(wd.find_element(By.CSS_SELECTOR, '[class="SWhIm"]').text) // 20 - 1, 1), 10)
        for _ in range(target):
            try:
                hide.hideElement(wd, 'header')
            except:
                pass
            job_lists = wd.find_element(By.XPATH,
                                        '//*[@id="yDmH0d"]/c-wiz/div/div[2]/div/div/div[2]/main/div/c-wiz/div/ul').find_elements(
                By.CSS_SELECTOR, 'a')
            for job in job_lists:
                job_title = job.get_attribute('aria-label')
                if not job_title: continue
                if job.get_attribute('aria-label').startswith("Learn"):
                    job_title = " ".join(job.get_attribute('aria-label').split()[3:])
                job_link = job.get_attribute('href').split("?")[0]
                job_id = job_link.split("/")[-1]
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_loc = job.find_element(By.XPATH, '../../../div[2]/div/span[2]').text.split("\n")[1]
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[aria-label="Go to next page"]')
                search.wait_x_sec(4)
            except:
                pass
