import datetime
import re

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base('roblox')
    experience = search.experience
    db = search.db
    geo = search.geo
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    url = f"https://boards.greenhouse.io/embed/job_board?for={company_name}"
    wd.get(url)
    Select(wd.find_element(By.CSS_SELECTOR, '[id="offices-select"]')).select_by_visible_text("United States")
    job_list = wd.find_elements(By.CSS_SELECTOR, '[class="opening"]')
    for job in job_list:
        job_title = job.find_element(By.CSS_SELECTOR, 'a').text
        if "Graduate" in job_title: continue
        job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
        job_id = job_link.split('=')[-1]
        job_link = f"https://boards.greenhouse.io/embed/job_app?for={company_name.lower()}&token={job_id}"
        job_loc = job.find_element(By.CSS_SELECTOR, '[class="location"]').text
        if bool(re.search(geo.getGeoUSRe(), job_loc)) and experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
