import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("tradedesk")
    url = "https://boards.greenhouse.io/thetradedesk"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    experience = search.experience
    db = search.db
    company_name = search.name
    WebDriverWait(wd, 10).until(
        EC.visibility_of_element_located((By.CSS_SELECTOR, '[id="office-filter"]')))
    search.wait_x_sec(3)
    wd.find_element(By.CSS_SELECTOR, '[id="office-filter"]').send_keys("United States")
    WebDriverWait(wd, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, '[id="react-select-office-filter-listbox"]')))
    us_ele = \
        wd.find_element(By.CSS_SELECTOR, '[id="react-select-office-filter-listbox"]').find_elements(By.CSS_SELECTOR,
                                                                                                    'div')[
            0].get_attribute('id')
    search.click_ele(wd, f'[id="{us_ele}"]')
    search.wait_x_sec(2)
    try:
        target = int(wd.find_element(By.CSS_SELECTOR, '[aria-label="Pagination"]')
                     .find_element(By.CSS_SELECTOR, 'ul').find_elements(By.CSS_SELECTOR, 'li')[-1].text)
    except:
        target = 1
    for _ in range(target):
        search.expect_shown_ele(wd, '[class="cell"]')
        job_list = wd.find_elements(By.CSS_SELECTOR, '[class="cell"]')
        for job in job_list:
            job_title, job_loc = [x.text for x in job.find_elements(By.CSS_SELECTOR, 'p')]
            if ";" in job_loc:
                job_loc = job_loc.split(";")[0].strip()
            try:
                tag_text = job.find_element(By.CSS_SELECTOR, '[class="ellipse"]').text
                job_title = job_title.replace(tag_text, '')
            except:
                pass
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
            job_id = job_link.split('/')[-1]
            if any(x in job_loc for x in ["India", "United Kingdom", "Spain", "Germany", "Kosovo", "France"]):
                continue
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
        try:
            search.click_ele(wd, '[aria-label="Next page"]')
            search.wait_x_sec(4)
        except:
            pass
