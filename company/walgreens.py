import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("walgreens")
    experience = search.experience
    db = search.db
    company_name = search.name
    hide = search.hide
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://jobs.walgreens.com/en/search-jobs/{keyword}/United%20States/1242/1/2/6252001/39x76/-98x5/25/2"
        wd.get(url)
        WebDriverWait(wd, 10).until(lambda driver: search.numbers_present_in_element(driver, '[class="headline"]'))
        try:
            hide.hideHeadAndFoot(wd)
            hide.hideElement(wd, '[id="featured-modal-window"]')
            hide.hideElement(wd, '[id="igdpr-alert"]')
        except:
            pass
        target = min(int(
            wd.find_element(By.CSS_SELECTOR, '[class="headline"]').text.split()[
                0]) // 15 - 1, 11)
        for _ in range(target):
            for job in wd.find_elements(By.CSS_SELECTOR, '[class="branded-list__list-item"]'):
                job_title = job.find_element(By.CSS_SELECTOR, '[class="headline headline--xs"]').text
                job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href")
                job_id = job_link.split("/")[-1]
                job_loc = job.find_element(By.CSS_SELECTOR, '[class="job-location"]').text
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[class="next"]')
                search.wait_x_sec(5)
            except:
                pass
