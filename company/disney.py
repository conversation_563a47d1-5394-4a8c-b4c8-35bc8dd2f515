from module.companyModule import companyModule as Base


def findIt(seen):
    url = "https://disney.wd5.myworkdayjobs.com/disneycareer?locations=4f84d9e8a09701011a5948763c4b0000&locations=4f84d9e8a09701011a58839baa360000&locations=4f84d9e8a09701011a578ea2b1250000&locations=4f84d9e8a09701011a562616cbb50000&locations=4f84d9e8a09701011a5826e77f1b0000&locations=4f84d9e8a09701011a642f1514a00000&locations=4f84d9e8a09701011a6a47d122a50000&locations=4f84d9e8a09701011a6bdc4d058b0000&locations=4f84d9e8a09701011a75e757d3850000&locations=4f84d9e8a09701011a6afaad40e30000&locations=4f84d9e8a09701011a5965f4a0e60000&locations=4f84d9e8a09701011a5b2c8df7e60000&locations=4f84d9e8a09701011a7140a91d9e0000&locations=4f84d9e8a09701011a595216d7b20000&locations=4f84d9e8a09701011a6f6d34a6070000&locations=4f84d9e8a09701011a69fd38359c0000&locations=4f84d9e8a09701011a762a24b0900000&locations=4f84d9e8a09701011a59d726dbab0000&locations=4f84d9e8a09701011a69c40e96710000&locations=4f84d9e8a09701011a6ff52600b00000&locations=4f84d9e8a09701011a71d2e0e0e50000&locations=4f84d9e8a09701011a72ab74b16d0000&locations=4f84d9e8a09701011a5a3cea04650000&locations=4f84d9e8a09701011a5e0d8577e40000&locations=4f84d9e8a09701011a6ded3d6b520000&locations=4f84d9e8a09701011a574498fbfb0000&locations=4f84d9e8a09701011a75aec3e82c0000&locations=4f84d9e8a09701011a5a6a0fddb20000&locations=4f84d9e8a09701011a73b14216da0000&locations=4f84d9e8a09701011a73b611c43c0000&locations=4f84d9e8a09701011a577e62d20b0000&locations=4f84d9e8a09701011a740910665e0000&locations=4f84d9e8a09701011a568bcec9810000&locations=4f84d9e8a09701011a69f86739820000&locations=4f84d9e8a09701011a66448e51f30000&locations=4f84d9e8a09701011a75abc108b40000&locations=4f84d9e8a09701011a5fa89789280000"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if seen == False else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
