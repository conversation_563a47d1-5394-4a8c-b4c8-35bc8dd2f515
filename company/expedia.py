from module.companyModule import companyModule as Base


def findIt(seen):
    url = "https://expedia.wd5.myworkdayjobs.com/en-US/search/?locations=c553432013ba103bbc581154b4104cfe&locations=c553432013ba103bbc581be7de044d12&locations=c553432013ba103bbc5a737343d54f66&locations=c553432013ba103bbc58583e3cce4d80&locations=c553432013ba103bbc5caadbcc62535f&locations=c553432013ba103bbc5c12b75a335250&locations=d4592151fa8e100bf6713848a881ae87&locations=c553432013ba103bbc5823dceb324d21&locations=c553432013ba103bbc5add671eaf5029&locations=c553432013ba103bbc5ad359b6015015&locations=c553432013ba103bbc58961dcd0a4df3&locations=0210e11067580172e7b490ef4646e32e&locations=c553432013ba103bbc5b0b33c7e35083"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
