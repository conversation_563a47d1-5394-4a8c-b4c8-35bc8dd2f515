import datetime
import re

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("rippling")
    experience = search.experience
    geo = search.geo
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    url = "https://www.rippling.com/careers/open-roles"
    wd.get(url)
    search.hide.hideHeadAndFoot(wd)
    search.hide.hideElement(wd, '[id="transcend-consent-manager"]')
    (Select(wd.find_element(By.CSS_SELECTOR,
                            '[class="bg-white rounded-54 border-2 border-black text-black font-medium"]'))
     .select_by_visible_text("Engineering"))
    job_list = wd.find_elements(By.CSS_SELECTOR, '[class="flex flex-col md:flex-row no-underline relative"]')
    for job in job_list:
        job_link = job.get_attribute('href')
        job_title = job.find_element(By.CSS_SELECTOR, 'p').text
        job_id = job_link.split('/')[-1]
        job_loc = job.find_elements(By.CSS_SELECTOR, 'p')[-1].text
        if "Canada" in job_loc:
            continue
        if (bool(re.search(geo.getGeoUSRe(), job_loc)) or (
                "US" in job_loc or "United States" in job_loc)) and experience.whatIWant(job_title) and len(
            db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
