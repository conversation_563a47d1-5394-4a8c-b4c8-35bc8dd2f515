import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("docker")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = "https://jobs.ashbyhq.com/docker?embed=js&locationId=************************************"
    wd = search.wd.driver_chrome_hide() if seen == False else search.wd.driver_chrome_seen()
    wd.get(url)
    WebDriverWait(wd, 10).until(
        expected_conditions.presence_of_element_located((By.CSS_SELECTOR, '[class="ashby-job-posting-brief-list"]')))
    search.hide.hideElement(wd, '[aria-label="Learn more about Ashby"]')
    job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="ashby-job-posting-brief-list"]')
    for dep in job_lists:
        for job in dep.find_elements(By.CSS_SELECTOR, 'a'):
            job_link = job.get_attribute("href").split("?")[0]
            job_title = job.find_element(By.CSS_SELECTOR, 'h3').text
            job_id = job_link.split("/")[-1]
            job_loc = 'Remote'
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
