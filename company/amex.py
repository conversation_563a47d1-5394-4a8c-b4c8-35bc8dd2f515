import datetime

from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("amex")
    hide = search.hide
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://aexp.eightfold.ai/careers?query={keyword}&location=United%20States&sort_by=relevance"
        wd.get(url)
        WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.flexbox")))
        hide.hideHeadAndFoot(wd)
        hide.hideElement(wd, '[class="ef-styles-2020 page-footer-ef "]')
        hide.hideElement(wd, '[role="navigation"]')
        hide.hideElement(wd, 'div.fixed-top')
        hide.hideElement(wd, '[id="main-container"]')
        hide.hideElement(wd, '[data-module-name="axp-global-header"]')
        hide.hideElement(wd, '[class="personalization-bar personalization-bar-pre-upload"]')
        hide.hideElement(wd, 'div.personalization-bar.personalization-bar-pre-upload')
        hide.hideElement(wd, '[class="row   position-job-container"]')
        hide.hideElement(wd, '[class="axp-footer__dls-module__module___1_EeR noindex"]')
        left_side_scroll = wd.find_element(By.CSS_SELECTOR, '[class="position-sidebar-scroll-handler  "]')
        ActionChains(wd).move_to_element(left_side_scroll).perform()
        show_more = len(
            wd.find_elements(By.CSS_SELECTOR, '[class="btn btn-sm btn-secondary show-more-positions"]')) > 0
        while show_more:
            try:
                search.click_ele(wd, '[class="btn btn-sm btn-secondary show-more-positions"]')
            except:
                search.wait_x_sec(3)
                search.click_ele(wd, '[class="btn btn-sm btn-secondary show-more-positions"]')
            search.wait_x_sec(3)
            show_more = len(
                wd.find_elements(By.CSS_SELECTOR, '[class="btn btn-sm btn-secondary show-more-positions"]')) > 0
        button_lists = left_side_scroll.find_elements(By.CSS_SELECTOR, 'div[role="link"]')
        for i, button in enumerate(button_lists):
            try:
                wd.execute_script(
                    f'''document.querySelector('[data-test-id="{button.get_attribute('data-test-id')}"]').scrollIntoView()''')
                search.click_ele(wd, button)
                job = wd.find_element(By.CSS_SELECTOR, '[class="position-full-card"]')
                job_title = job.find_element(By.CSS_SELECTOR, 'h1.position-title').text
                job_id = job.find_element(By.CSS_SELECTOR, 'p.faded.position-id-text').text.strip("ID:").replace(
                    " ", "")
                job_link = f"https://axp.taleo.net/careersection/2/jobapply.ftl?job={job_id}&lang=en&src=Eightfold"
                job_loc = job.find_element(By.CSS_SELECTOR, '[class="position-location"]').text
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            except Exception:
                pass
