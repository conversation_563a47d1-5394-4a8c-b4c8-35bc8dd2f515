import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("waymo")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://careers.withwaymo.com/jobs/search?page=1&employment_type_uids%5B%5D=2ea50d7de0fbb2247d09474fbb5ee4da&states%5B%5D=Arizona&states%5B%5D=California&states%5B%5D=Michigan&states%5B%5D=New+York&states%5B%5D=Pennsylvania&states%5B%5D=Texas&states%5B%5D=Washington&query={keyword}"
        wd.get(url)
        try:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[id="consent_reject"]')))
            search.click_ele(wd, '[id="consent_reject"]')
        except:
            pass
        try:
            WebDriverWait(wd, 10).until(
                lambda driver: search.numbers_present_in_element(driver, '[class="table-counts"]'))
        except:
            continue
        try:
            target = max(min(int(
                wd.find_elements(By.CSS_SELECTOR, '[class="page-link"]')[-2].text.split()[
                    0]) - 1, 10), 1)
        except:
            target = 1
        job_dict = {}
        for _ in range(target):
            for job in wd.find_elements(By.CSS_SELECTOR, '[class="card-body job-search-results-card-body"]'):
                job_title = job.find_element(By.CSS_SELECTOR, 'a').text
                job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute("href")
                job_dict[job_title] = job_link
            try:
                search.click_ele(wd, '[aria-label="Next page"]')
                search.wait_x_sec(4)
            except:
                pass
    for job_title, job_link in job_dict.items():
        wd.get(job_link)
        WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[class="block-html"]')))
        job_loc, _, _, job_id = [x.text for x in
                                 wd.find_element(By.CSS_SELECTOR, '[class="page-block"]').find_elements(By.XPATH,
                                                                                                        'h2//span')]
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc.title())
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
