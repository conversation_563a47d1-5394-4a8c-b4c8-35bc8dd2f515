import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen: bool):
    search = Base("amazon")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://www.amazon.jobs/en/search?offset=0&result_limit=10&sort=recent&job_type%5B%5D=Full-Time&country%5B%5D=USA&distanceType=Mi&radius=24km&is_manager%5B%5D=0&base_query={keyword}"
        wd.get(url)
        WebDriverWait(wd, 10).until(
            lambda driver: search.numbers_present_in_element(driver, '[class="pagination-control"]'))
        try:
            search.hide.hideElement(wd, '[class="navigation"]')
            search.hide.hideElement(wd, '[id="versionized-cookie-banner"]')
        except:
            pass
        target = min(max((int(wd.find_elements(By.CSS_SELECTOR, '[class="page-button"]')[-1].text), 1)), 20)
        for _ in range(target):
            job_lists = wd.find_elements(By.CSS_SELECTOR, '[class="job"]')
            for job in job_lists:
                job_loc = job.text.split("\n")[1]
                job = job.find_element(By.CSS_SELECTOR, 'a')
                job_title = job.text
                job_link = job.get_attribute("href")
                job_id = job_link.split("/")[-2]
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                search.click_ele(wd, '[aria-label="Next page"]')
                search.wait_x_sec(3)
            except:
                pass
