from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("snap")
    url = f"https://wd1.myworkdaysite.com/en-US/recruiting/snapchat/snap?locations=256f279d5e741082c567cf2872866286&locations=137dd6cbab601000bf830bdee83d0000&locations=efe1a86507310144a123773b020a0e37&locations=efe1a86507310187e01ef207030a7937&locations=2b0a835c9646011d58da08236e4f6726&locations=08836f686d73101d8025e3c730de4ba5&locations=efe1a865073101e5380680f9020a7437&locations=8bf70c1877bb01f58a864a033aab9149&locations=a66859edee62018a4052b8b2ee069d1b&locations=1f0f8aa27ae301b75b751228f47c06fb&locations=30ed318fdf1201e340dd699ac640dac6&locations=a66859edee620185f846b98be5060519&locations=b9cf6982655e1001a9ff7ae350d10000&locations=7a68e5b6d6b51001a9de39167c5f0000&locations=efe1a865073101b9db6c8da7020a6037&locations=256f279d5e741082c567c24fca236272&locations=efe1a8650731016c130aaddd010aed36&locations=efe1a865073101717b72c224020a0137&locations=256f279d5e741082c567c8a528f4627c"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
