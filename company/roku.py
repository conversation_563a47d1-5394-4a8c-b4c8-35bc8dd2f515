import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base('roku')
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://www.weareroku.com/jobs/search?page=1&country_codes[]=US&query={keyword}"
        wd.get(url)
        search.numbers_present_in_element(wd, '[class="table-counts"]')
        try:
            search.click_ele(wd, '[id="consent_reject"]')
        except:
            pass
        job_lists_map = {}
        try:
            target = max(int(wd.find_element(By.CSS_SELECTOR, '[class="table-counts"]').text.split()[-2]) // 30, 1)
        except:
            target = max(int(wd.find_element(By.CSS_SELECTOR, '[class="table-counts"]').text.split()[-3]) // 30, 1)
        for _ in range(target):
            job_list = wd.find_element(By.CSS_SELECTOR, 'tbody').find_elements(By.CSS_SELECTOR,
                                                                               '[class="job-search-results-title"]')
            for job in job_list:
                job_title = job.find_element(By.CSS_SELECTOR, 'a').text
                job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
                if experience.whatIWant(job_title):
                    job_lists_map[job_link] = job_title
                    break
            try:
                search.click_ele(wd, '[aria-label="Next page"]')
            except:
                pass
        for job_link, job_title in job_lists_map.items():
            wd.get(job_link)
            search.numbers_present_in_element(wd, '[class="editor-placeholder"]')
            job_id = wd.find_elements(By.CSS_SELECTOR, '[class="editor-placeholder"]')[-1].text
            job_loc = wd.find_elements(By.CSS_SELECTOR, '[class="editor-placeholder"]')[-2].text
            if len(db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
