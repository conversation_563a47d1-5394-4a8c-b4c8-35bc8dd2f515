from module.companyModule import companyModule as Base


def findIt(seen):
    url = f"https://nike.wd1.myworkdayjobs.com/nke/?locations=53aa8f3ea4191001f6114eca9cbf0000&locations=53aa8f3ea4191001f60d6c97a8a10000&locations=53aa8f3ea4191001f612a5273fe50000&locations=53aa8f3ea4191001f60f6328ef7b0000&locations=53aa8f3ea4191001f60bfcab94480000&locations=53aa8f3ea4191001f60bf5749ce10000&locations=53aa8f3ea4191001f60e8d141cd70000"
    search = Base(url.replace("https://", "").split(".")[0])
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    search.workday_company_search(wd, url)
