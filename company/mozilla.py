import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen: bool):
    search = Base("mozilla")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_seen() if seen else search.wd.driver_chrome_hide()
    url = "https://job-boards.greenhouse.io/mozilla?offices%5B%5D=77552&offices%5B%5D=77892&page=1"
    wd.get(url)
    search.expect_shown_ele(wd, '[role="navigation"]')
    target = int(wd.find_element(By.CSS_SELECTOR, '[role="navigation"]').find_elements(By.CSS_SELECTOR, 'li')[-1].text)
    for _ in range(target):
        search.expect_shown_ele(wd, '[class="cell"]')
        search.wait_x_sec(4)
        job_list = wd.find_elements(By.CSS_SELECTOR, '[class="cell"]')
        for job in job_list:
            job_title, job_loc = [x.text for x in job.find_elements(By.CSS_SELECTOR, 'p')]
            try:
                tag_text = job.find_element(By.CSS_SELECTOR, '[class="ellipse"]').text
                job_title = job_title.replace(tag_text, '')
            except:
                pass
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
            job_id = job_link.split('/')[-1].split("?")[0]
            if not any(x in job_loc for x in ["US"]) or job_loc != 'Remote':
                continue
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
        search.click_ele(wd, '[aria-label="Next page"]')
