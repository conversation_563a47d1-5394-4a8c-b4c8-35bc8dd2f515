import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("tiktok")
    experience = search.experience
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    db = search.db
    company_name = search.name
    for keyword in search.keyword_list:
        url = f"https://lifeattiktok.com/search?keyword={keyword}+United+States&recruitment_id_list=&job_category_id_list=&subject_id_list=&location_code_list=&limit=12&offset=0"
        wd.get(url)
        WebDriverWait(wd, 20).until(EC.presence_of_element_located(
            (By.CSS_SELECTOR, '[data-testid="pagination"]')))
        try:
            decline_button = wd.find_element(By.XPATH, "//p[text()='Decline all']")
            decline_button.click()
        except:
            pass
        search.hide.hideElement(wd, '[data-testid="menu-bar"]')
        for _ in range(int(
                wd.find_element(By.CSS_SELECTOR, '[data-testid="pagination"]').find_elements(By.CSS_SELECTOR, 'button')[
                    -2].text)):
            WebDriverWait(wd, 20).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class=" flex flex-col w-full"]')))
            job_lists = wd.find_element(By.CSS_SELECTOR, '[class=" flex flex-col w-full"]') \
                .find_elements(By.CSS_SELECTOR, 'a')
            for job in job_lists:
                job_title, job_loc = job.find_elements(By.CSS_SELECTOR, 'span')[:2]
                job_loc = job_loc.text
                if "London" in job_loc or "Singapore" in job_loc: continue
                job_title = job_title.text
                job_link = job.get_attribute('href')
                job_id = job_link.split("/")[-1]
                job_link = f"https://careers.tiktok.com/resume/{job_id}/apply"
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            try:
                button = wd.find_elements(By.CSS_SELECTOR, 'button[class="cursor-pointer"]')
                search.click_ele(wd, button[-1])
                search.wait_x_sec(4)
            except:
                pass
