from datetime import datetime

from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("netflix")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f"https://explore.jobs.netflix.net/careers?query={keyword}&location=United%20States"
        wd.get(url)
        search.hide.hideElement(wd, '[class="ef-styles-2020 page-footer "]')
        search.hide.hideElement(wd, '[id="EFSmartApplyCustomFooterContainer"]')
        left_side_scroll = wd.find_element(By.CSS_SELECTOR, '[class="position-sidebar-scroll-handler  "]')
        idx = 3
        ActionChains(wd).move_to_element(left_side_scroll).perform()
        while idx:
            wd.execute_script("arguments[0].scrollTo(0, arguments[0].scrollHeight)", left_side_scroll)
            idx -= 1
            left_side_scroll = wd.find_element(By.CSS_SELECTOR, '[class="position-sidebar-scroll-handler  "]')
        idx = 10
        show_more = len(
            wd.find_elements(By.CSS_SELECTOR, '[class="btn btn-sm btn-secondary show-more-positions"]')) > 0
        while show_more and idx:
            search.click_ele(wd, '[class="btn btn-sm btn-secondary show-more-positions"]')
            search.wait_x_sec(3)
            show_more = len(
                wd.find_elements(By.CSS_SELECTOR, '[class="btn btn-sm btn-secondary show-more-positions"]')) > 0
            idx -= 1
        button_lists = left_side_scroll.find_elements(By.CSS_SELECTOR, 'div[role="button"]')

        for i, button in enumerate(button_lists[:50]):
            try:
                wd.execute_script(
                    f'''document.querySelector('[data-test-id="{button.get_attribute('data-test-id')}"]').scrollIntoView()''')
            except Exception:
                pass
            search.wait_x_sec(1)
            button.click()
            search.wait_x_sec(3)
            try:
                job = wd.find_element(By.CSS_SELECTOR, '[class="position-full-card"]')
                job_title = job.find_element(By.CSS_SELECTOR, 'h1.position-title').text
                job_id = wd.current_url.split("pid")[-1].split("&")[0].replace("=", "")
                job_link = f"https://explore.jobs.netflix.net/careers?pid={job_id}"
                job_loc = job.find_element(By.CSS_SELECTOR, '[class="position-location"]').text
                if 'California' in job_loc:
                    job_loc = 'California'
                if 'Remote' in job_loc:
                    job_loc = 'Remote'
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})
            except:
                pass
