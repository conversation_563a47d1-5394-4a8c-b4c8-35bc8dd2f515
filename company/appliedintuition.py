import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("appliedintuition")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = f"https://boards.greenhouse.io/embed/job_board?for={company_name}"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[id="select2-chosen-2"]')))
    search.click_ele(wd, '[id="select2-chosen-2"]')
    WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[class="select2-results"]')))
    us_ele = list(filter(lambda x: "North America" in x.text,
                         wd.find_elements(By.CSS_SELECTOR, '[id="select2List1"]')[0].find_elements(
                             By.CSS_SELECTOR, 'li')))
    search.click_ele(wd, us_ele[0])
    job_list = wd.find_elements(By.CSS_SELECTOR, '[class="opening"]')
    for job in job_list:
        job_title = job.find_element(By.CSS_SELECTOR, 'a').text
        if "New Grad" in job_title or "Graduate" in job_title:
            continue
        job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
        job_id = job_link.split('/')[-1].split("?")[0]
        job_loc = job.find_element(By.CSS_SELECTOR, '[class="location"]').text
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
