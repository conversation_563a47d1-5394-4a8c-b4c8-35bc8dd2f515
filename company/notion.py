import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("notion")
    experience = search.experience
    db = search.db
    company_name = search.name
    url = "https://job-boards.greenhouse.io/notion?offices%5B%5D=4009671003&offices%5B%5D=4027145003&offices%5B%5D=4009672003"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[class="padding"]')))
    target = max(int(
        wd.find_element(By.CSS_SELECTOR, '[class="padding"]').find_element(By.CSS_SELECTOR, 'h2').text.split()[
            0]) // 50 + 1, 1)
    for i in range(target):
        job_list = wd.find_element(By.CSS_SELECTOR, '[class="padding"]').find_elements(By.CSS_SELECTOR, 'a')
        for job in job_list:
            job_title, job_loc = job.find_elements(By.CSS_SELECTOR, 'p')
            job_title = job_title.text
            job_loc = job_loc.text
            job_link = job.get_attribute('href').split("?")[0]
            job_id = job_link.split('/')[-1]
            if experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                if "Early Career" in job_title:
                    db.create({"company": f"{company_name.lower()}", "link": f"{job_link.replace(' ', '')}"},
                              "job_info_ng")
                    continue
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})

        try:
            wd.get(url + f'&page={i + 1}')
            search.wait_x_sec(4)
        except:
            pass
