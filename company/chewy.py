import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("chewy")
    experience = search.experience
    db = search.db
    hide = search.hide
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    for keyword in search.keyword_list:
        url = f'https://careers.chewy.com/us/en/search-results?keywords={keyword}NOT%20Manager'
        wd.get(url)
        search.numbers_present_in_element(wd, '[class="result-count"]')
        hide.hideElement(wd, '[class="ph-header"]')
        target = max(min(int(wd.find_element(By.CSS_SELECTOR, '[class="result-count"]').text) // 10 - 1, 20), 1)
        for _ in range(target):
            job_lists = wd.find_elements(By.CSS_SELECTOR, '[data-ph-at-id="job-link"]')
            for job in job_lists:
                job_title = job.get_attribute('data-ph-at-job-title-text')
                job_link = job.get_attribute("href")
                job_id = job_link.split("/")[-2]
                job_loc = job.get_attribute("data-ph-at-job-location-text")
                if experience.whatIWant(job_title) and len(
                        db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                    job_state = search.get_job_location_by_job_link_location(job_loc)
                    if not job_state:
                        job_state = search.get_job_location_by_requests(job_loc)
                    job_loc = job_state
                    db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                               "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                               "location": f"{job_loc}"})

            try:
                search.click_ele(wd, '[aria-label="View next page"]')
                search.wait_x_sec(4)
            except:
                pass
