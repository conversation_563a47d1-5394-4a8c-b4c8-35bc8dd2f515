import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("janestreet")
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    url = "https://www.janestreet.com/join-jane-street/open-roles/?type=experienced-candidates&location=new-york"
    wd.get(url)
    search.hide.hideHeadAndFoot(wd)
    search.hide.hideElement(wd, '[aria-label="cookieconsent"]')
    job_lists = wd.find_element(By.CSS_SELECTOR,
                                '[class="jobs-container row"]').find_elements(By.CSS_SELECTOR, 'a')
    for job in job_lists:
        if not ("New York" in job.text):
            continue
        job_title = job.find_element(By.CSS_SELECTOR, '[class="item experienced position"]').text
        job_link = job.get_attribute('href')
        job_id = job_link.split("/")[-2]
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_loc = "New York"
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
    try:
        search.click_ele(wd, '[class="paginationItem paginationNextLink"]')
    except:
        pass
