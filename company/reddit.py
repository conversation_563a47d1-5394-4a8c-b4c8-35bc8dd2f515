import datetime

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base('reddit')
    experience = search.experience
    db = search.db
    company_name = search.name
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    url = f"https://job-boards.greenhouse.io/reddit?offices%5B%5D=88237&offices%5B%5D=48028&offices%5B%5D=10769&offices%5B%5D=10168&offices%5B%5D=10167"
    wd.get(url)
    job_list = wd.find_elements(By.CSS_SELECTOR, '[class="job-post"]')
    for job in job_list:
        if ("Canada" in job.find_element(By.CSS_SELECTOR, 'a').text
                or "Ontario" in job.find_element(By.CSS_SELECTOR, 'a').text): continue
        job_title, job_loc = job.find_element(By.CSS_SELECTOR, 'a').find_elements(By.CSS_SELECTOR, 'p')
        try:
            tag_text = job.find_element(By.CSS_SELECTOR, '[class="ellipse"]').text
            job_title = job_title.replace(tag_text, '')
        except:
            pass
        job_title = job_title.text
        job_loc = job_loc.text
        job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
        job_id = job_link.split('/')[-1]
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
