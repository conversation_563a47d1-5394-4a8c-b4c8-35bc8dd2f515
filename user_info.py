import traceback
import os
from dotenv import load_dotenv

import mysql.connector

load_dotenv()

# Configuration
LOCAL_DB_HOST = os.getenv("LOCAL_DB_HOST")
LOCAL_DB_USER = os.getenv("LOCAL_DB_USER")
LOCAL_DB_PWD = os.getenv("LOCAL_DB_PWD")
LOCAL_DB_DATABASE = os.getenv("LOCAL_DB_DATABASE")
LOCAL_DB_TABLE = os.getenv("LOCAL_DB_TABLE")


def read_info(user_phone):
    # SQL statement
    sql = f"SELECT * FROM user_info WHERE user_phone = '{user_phone}'"
    sql2 = f"SELECT user_email_pwd FROM user_email_info WHERE user_phone = '{user_phone}'"

    # Establish a connection to the MySQL database using a context manager
    with mysql.connector.connect(
            host=LOCAL_DB_HOST,
            user=LOCAL_DB_USER,
            password=LOCAL_DB_PWD,
            database=LOCAL_DB_DATABASE
    ) as cnx:
        # Create a cursor object
        cursor = cnx.cursor()

        try:
            # Execute the SQL statement
            cursor.execute(sql)

            # Fetch the results
            results = cursor.fetchall()[0]

            # Print the retrieved information
            user_active = results[1]
            user_name = results[2]
            user_account = results[3]
            user_pwd = results[4]
            user_address = results[5]
            user_city = results[6]
            user_state = results[7]
            user_zipcode = results[8]
            user_phone = results[9]
            user_gender_male = True if results[10] == 1 else False
            user_linkedin = results[11]
            user_visa = results[12]
            user_title = results[13]
            user_company = results[14]
            user_start_time = results[15]
            user_end_time = results[16]
            user_role_desc = results[17]
            user_school = results[18]
            user_degree = results[19]
            user_school_start_time = results[20]
            user_school_end_time = results[21]
        except mysql.connector.Error:
            traceback.format_exc()
        try:
            cursor.execute(sql2)
            user_email_pwd = cursor.fetchall()[0][0]
        except:
            user_email_pwd = None
        cursor.close()
    return user_active, user_name, user_account, user_pwd, user_address, user_city, user_state, user_zipcode \
        , user_phone, user_gender_male, user_linkedin, user_visa, user_title, user_company, \
        user_start_time, user_end_time, user_role_desc, user_school, user_degree, \
        user_school_start_time, user_school_end_time, user_email_pwd
