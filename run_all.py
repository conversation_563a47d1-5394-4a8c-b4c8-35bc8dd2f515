import datetime
import os
import random
import subprocess
import threading
import time
from concurrent.futures import as_completed, ThreadPoolExecutor

import psutil
import schedule
from dotenv import load_dotenv

from module.configLoaderModule import ConfigLoader
from module.databaseModule import databaseModule as db
from module.emailModule import Email<PERSON>ender
from module.loggingModule import logger
from search import SearchModule


class ApplicationState:
    def __init__(self):
        self.companies_dict = None
        self.user_dict = None
        self._lock = threading.Lock()
        self.config_loader = ConfigLoader()
        # Thread-safe flag to track scheduled search status
        self.search_running = threading.Event()
        # Thread-safe flag to track database sync status
        self.db_sync_running = threading.Event()
        self.reload_configs()

    def reload_configs(self):
        with self._lock:
            self.user_dict, self.companies_dict = self.config_loader.get_configs()
            logger.info("Configurations reloaded")

    @property
    def lock(self):
        return self._lock


def kill_chrome(search_running_flag=None):
    """
    Kill Chrome browser processes but preserve chromedriver binaries.
    This prevents interference with chromedriver caching.
    Only kills Chrome processes when no scheduled search is running.
    """
    # Check if scheduled search is running
    if search_running_flag and search_running_flag.is_set():
        logger.info("Scheduled search is running, skipping Chrome cleanup")
        return

    try:
        killed_count = 0
        for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
            process_name = process.name()

            # Kill Chrome browser processes but NOT chromedriver
            if (process_name.startswith('Google Chrome') or
                    (process_name.startswith("chrome") and
                     not process_name.startswith("chromedriver") and
                     "chromedriver" not in process_name.lower())):

                # Additional safety check: examine command line to avoid killing chromedriver
                try:
                    cmdline = process.cmdline()
                    if cmdline and any("chromedriver" in arg.lower() for arg in cmdline):
                        logger.debug(f"Skipping chromedriver process: {process_name} (PID: {process.pid})")
                        continue
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    pass

                try:
                    process.kill()
                    killed_count += 1
                    logger.debug(f"Killed Chrome process: {process_name} (PID: {process.pid})")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

        if killed_count > 0:
            logger.info(f"Killed {killed_count} Chrome browser processes")

    except Exception as e:
        logger.error(f"Failed to kill Chrome processes: {e}")


def read_last_sent_date():
    try:
        with open('last_sent_date.txt', 'r') as file:
            return file.read().strip()
    except FileNotFoundError:
        return ''
    finally:
        file.close()


def write_last_sent_date(date):
    with open('last_sent_date.txt', 'w') as file:
        file.write(date)


def search_limit(search_fun):
    load_dotenv()
    INSTANCE_ROLE = os.getenv("INSTANCE_ROLE", "slave")

    today_date = time.strftime('%Y-%m-%d')
    last_sent_date = read_last_sent_date()
    if today_date != last_sent_date:
        if INSTANCE_ROLE == "master":
            try:
                # check if docker container is running
                import requests
                response = requests.get("http://localhost:8080/status.php", timeout=5)
                if response.status_code == 200:
                    search_fun.search_jobs()
                else:
                    logger.error("Nominatim is not running, skipping search (MASTER)")
            except Exception as e:
                logger.error(f"Error during master tasks (Nominatim/Search): {e}")
        write_last_sent_date(today_date)
        logger.info("Successfully refresh jobs!")


def read_write_record(db1, db2):
    today_opening_lists = db1.read(columns='company, jobid, title, link, retrievetime, location',
                                   where="retrievetime between now() - interval 1 week and now() order by retrievetime desc",
                                   table='job_info')
    data_list = []
    for record in today_opening_lists:
        company_name, jobid, title, link, retrievetime, location = record
        data = (
            company_name,
            jobid,
            title,
            retrievetime,
            link,
            location
        )
        data_list.append(data)
    columns = "company, jobid, title, retrievetime, link, location"
    placeholders = "%s, %s, %s, %s, %s, %s"
    update_stmt = """
        title = VALUES(title),
        retrievetime = VALUES(retrievetime),
        link = VALUES(link),
        location = VALUES(location)
    """
    db2.batch_create(data_list, columns=columns, placeholders=placeholders, update_stmt=update_stmt)


def process_user(data):
    user, user_dict, companies_dict = data
    shuffled_companies = list(companies_dict.items())
    random.shuffle(shuffled_companies)
    shuffled_dict = dict(shuffled_companies)

    try:
        from apply import ApplyModule
        ApplyModule(user, user_dict, shuffled_dict).call_apply_fun()
    except Exception as e:
        logger.error(f"Error processing user {user}: {e}")


def schedule_search(search_fun, search_running_flag):
    """
    Run scheduled search in a separate thread with thread-safe flag management.
    Only runs on weekdays and ensures proper cleanup even on errors.
    Includes a 3-hour timeout to prevent the search from running indefinitely.
    """

    def search_worker():
        search_running_flag.set()  # Set flag to indicate search is running
        try:
            logger.info(f"Scheduled search started at {datetime.datetime.now()}")
            search_limit(search_fun)
            logger.info(f"Scheduled search completed at {datetime.datetime.now()}")
        except Exception as e:
            logger.error(f"Error during scheduled search: {e}")
        finally:
            search_running_flag.clear()  # Always clear flag, even on error
            logger.info("Scheduled search flag cleared")

    def timeout_monitor(search_thread, timeout_seconds=3 * 60 * 60):  # 3 hours timeout
        """Monitor search thread and force cleanup if it exceeds timeout"""
        search_thread.join(timeout=timeout_seconds)
        if search_thread.is_alive():
            logger.warning(f"Search thread exceeded {timeout_seconds/3600:.1f} hour timeout, forcing cleanup")
            # Kill Chrome processes to free up stuck drivers
            try:
                killed_count = 0
                for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                    process_name = process.name()

                    # Kill both Chrome browser processes AND chromedriver processes on timeout
                    if (process_name.startswith('Google Chrome') or
                            process_name.startswith("chrome") or
                            "chromedriver" in process_name.lower()):
                        try:
                            process.kill()
                            killed_count += 1
                            logger.debug(f"Killed Chrome/chromedriver process due to timeout: {process_name} (PID: {process.pid})")
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass

                if killed_count > 0:
                    logger.info(f"Killed {killed_count} Chrome/chromedriver processes due to search timeout")

            except Exception as e:
                logger.error(f"Failed to kill Chrome processes during timeout cleanup: {e}")

            # Force clear the flag since the thread is stuck
            search_running_flag.clear()
            logger.info("Search flag forcibly cleared due to timeout")

    # Only run on weekdays
    if datetime.datetime.now().weekday() < 5:
        if not search_running_flag.is_set():
            # Start search in a separate thread
            search_thread = threading.Thread(
                target=search_worker,
                daemon=True,
                name="ScheduledSearchThread"
            )
            search_thread.start()
            logger.info("Scheduled search thread started")

            # Start timeout monitor in another daemon thread
            timeout_thread = threading.Thread(
                target=timeout_monitor,
                args=(search_thread,),
                daemon=True,
                name="SearchTimeoutMonitor"
            )
            timeout_thread.start()
            logger.info("Search timeout monitor started (3 hour limit)")
        else:
            logger.info("Scheduled search already running, skipping")
    else:
        logger.info("Weekend detected, skipping scheduled search")


def schedule_db_sync(db_sync_running_flag):
    """
    Run scheduled database synchronization in a separate thread with thread-safe flag management.
    Syncs job records from the past week between local and remote databases.
    Ensures proper cleanup even on errors.
    """

    def db_sync_worker():
        db_sync_running_flag.set()  # Set flag to indicate sync is running
        try:
            logger.info(f"Scheduled database sync started at {datetime.datetime.now()}")

            # Load environment variables for database connection
            load_dotenv()
            INSTANCE_ROLE = os.getenv("INSTANCE_ROLE", "slave")
            REMOTE_DB_HOST = os.getenv("REMOTE_DB_HOST")
            REMOTE_DB_PWD = os.getenv("REMOTE_DB_PWD")

            if not REMOTE_DB_HOST or not REMOTE_DB_PWD:
                logger.error("Missing REMOTE_DB_HOST or REMOTE_DB_PWD environment variables")
                return

            # Initialize database connections
            local_db = db()
            remote_db = db(host=REMOTE_DB_HOST, pwd=REMOTE_DB_PWD)

            # Perform sync based on instance role
            if INSTANCE_ROLE == "master":
                logger.info("Running database sync as master (local -> remote)")
                read_write_record(local_db, remote_db)
            elif INSTANCE_ROLE == "slave":
                logger.info("Running database sync as slave (remote -> local)")
                read_write_record(remote_db, local_db)
            else:
                logger.warning(f"Unknown INSTANCE_ROLE: {INSTANCE_ROLE}, defaulting to slave mode")
                read_write_record(remote_db, local_db)

            logger.info(f"Scheduled database sync completed successfully at {datetime.datetime.now()}")

        except Exception as e:
            logger.error(f"Error during scheduled database sync: {e}")
        finally:
            db_sync_running_flag.clear()  # Always clear flag, even on error
            logger.info("Database sync flag cleared")

    # Check if sync is already running
    if not db_sync_running_flag.is_set():
        # Start sync in a separate thread
        sync_thread = threading.Thread(
            target=db_sync_worker,
            daemon=True,
            name="ScheduledDBSyncThread"
        )
        sync_thread.start()
        logger.info("Scheduled database sync thread started")
    else:
        logger.info("Database sync already running, skipping")


def run_scheduler():
    logger.info("Scheduler thread started")
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # 改为每分钟检查一次，减少日志量
        except Exception as e:
            logger.error(f"Error in scheduler thread: {e}")


def main():
    app_state = ApplicationState()

    try:
        # Create a persistent SearchModule instance
        search_module = SearchModule()
        schedule.every().day.at("04:00").do(
            schedule_search,
            search_fun=search_module,
            search_running_flag=app_state.search_running
        )
        # Schedule daily database sync at 8:00 AM
        schedule.every().day.at("08:00").do(
            schedule_db_sync,
            db_sync_running_flag=app_state.db_sync_running
        )
        # Start scheduler thread
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True, name="SchedulerThread")
        scheduler_thread.start()

        while True:
            os.system('clear')
            try:
                subprocess.run(['git', 'pull'], check=True)
            except Exception as e:
                logger.error(f"Failed to pull from GitHub: {e}")

            # Pass the search_running flag to kill_chrome
            kill_chrome(app_state.search_running)

            start_time = datetime.datetime.now()
            with db() as database:
                active_user_set = {i[0] for i in database.read("user_phone", "user_active = 1", "user_info")}

            active_user_cnt = len(active_user_set)
            logger.info(f"Current active user count: {active_user_cnt}")

            with app_state.lock:
                not_running_users = active_user_set - app_state.user_dict.keys()
            if not_running_users:
                logger.warning(f"Not running users: {not_running_users}")

            time_out = 10 * 60
            max_workers = min(len(app_state.user_dict.keys()), 5 if datetime.datetime.now().hour in range(1, 8) else 4)
            logger.info(f"Max workers: {max_workers}")
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                with app_state.lock:
                    for cur_user, cur_user_dict in app_state.user_dict.items():
                        if cur_user in active_user_set:
                            future = executor.submit(
                                process_user,
                                (cur_user, cur_user_dict, app_state.companies_dict)
                            )
                            futures.append(future)

                for future in as_completed(futures):
                    try:
                        future.result(timeout=time_out)
                    except Exception as e:
                        logger.error(f"Task failed: {e}")

            end_time = datetime.datetime.now()
            time_diff = end_time - start_time
            time_diff_minutes = int(time_diff.total_seconds() // 60)
            logger.info(f"Apply time: {time_diff_minutes} minutes")

            cool_down_period = random.randint(120, 180) if active_user_cnt <= 10 else random.randint(50, 80)
            logger.info(f"Cooling down for {cool_down_period} minutes...")

            time.sleep(cool_down_period * 60)
            logger.info("Resume apply...")
    finally:
        app_state.config_loader.stop_watching()


if __name__ == "__main__":
    try:
        send_email = EmailSender()
        main()
    except Exception as e:
        logger.error(f"Application crashed: {e}")
