import datetime
import json
import re
import sys
import threading
import time
from collections import defaultdict
from multiprocessing import Lock
from threading import RLock

import pandas as pd
import pytz
from loguru import logger
from selenium.common.exceptions import WebDriverException

from module.companyModule import companyModule as Base


def get_workday_companies():
    companies_set = {
        "abbott", "adobe", "athenahealth", "att", "autodesk", "bankofamerica",
        "cadence", "capitalone", "caterpillar", "cigna", "citi", "comcast", "dell",
        "discover", "disney", "ebay", "fidelity", "fis", "fiserv", "freddiemac", "geico",
        "homedepot", "hp", "hpe", "humana", "intel", "lowes", "mastercard", "mckesson",
        "nasdaq", "ncr", "nike", "nordstrom", "nvidia", "paypal", "RTX", "salesforce",
        "snap", "spgi", "target", "tmobile", "tradedesk", "ups", "usbank", "vanguard",
        "vmware", "walmart", "wellsfargo", "workday", "yahoo", "zillow", "zoom", "expedia",
        'cvs', 'athenahealth', 'dell', 'geico', 'travelers', 'accenture'
    }
    return companies_set


class ApplyModule:
    _user_instances = {}  # 类级实例管理
    _lock = threading.Lock()
    _user_locks = defaultdict(RLock)  # 添加用户级锁

    def __init__(self, user: str, user_dict: dict, companies_dict: dict):
        self.lock = Lock()
        self.companies_apply_dict = companies_dict
        self.companies = None
        self.user = user
        self.user_dict = user_dict
        self.dependency_object = Base("test")
        self.filtered_jobs_df = self.fetch_sql_to_df()
        self.pdt_time = datetime.datetime.now(pytz.timezone('America/Los_Angeles'))
        self.workday_maintain_flag = ((self.pdt_time.weekday() == 4 and
                                       23 <= self.pdt_time.hour) or
                                      (self.pdt_time.weekday() == 5 and self.pdt_time.hour < 6))
        self.email_set = set(
            i[0] for i in self.dependency_object.db.read(columns='user_phone', table='user_email_info'))

    def fetch_sql_to_df(self):
        filter_time = "retrievetime BETWEEN NOW() - INTERVAL 4 WEEK AND NOW()"
        ng_year = datetime.datetime.now().year + 1
        filter_ng_title = f"title NOT LIKE '%grad%' AND title NOT LIKE '%{ng_year}%'"
        filter_ng = "link NOT IN (SELECT distinct link FROM job_info_ng)"
        filter_companies = "company IN ({})".format(','.join(map(repr, self.companies_apply_dict.keys())))
        where_clause = f"{filter_time} AND {filter_ng_title} AND {filter_companies} AND {filter_ng}"
        df = pd.DataFrame(
            self.dependency_object.db.read(
                columns='company, jobid, title, link, location',
                where=where_clause,
            ),
            columns=['company', 'jobid', 'title', 'link', 'location']
        )
        df = df.sample(frac=1).reset_index(drop=True)
        return df

    def _get_user_preferences(self):
        """获取用户偏好设置"""
        return {
            'company_dict': self.user_dict.get('company', {}),
            'directions': self.user_dict.get('directions', ["software"]),
            'wanted_keywords': self.user_dict.get('wanted_keyword', []),
            'unwanted_keywords': self.user_dict.get('unwanted_keyword', ['staff']),
            'experience': self.user_dict.get('experience', ''),
            'preferred_locations': self.user_dict.get('preferred_locations', []),
            'excluded_locations': self.user_dict.get('excluded_locations', [])
        }

    def _filter_jobs_by_keywords(self, df, wanted_keywords, unwanted_keywords):
        """根据关键词过滤职位"""
        filtered_df = df.copy()

        if wanted_keywords:
            pattern = '|'.join(map(re.escape, wanted_keywords))
            filtered_df = filtered_df[
                filtered_df['title'].str.contains(pattern, case=False, regex=True, na=False)]

        if unwanted_keywords:
            pattern = '|'.join(map(re.escape, unwanted_keywords))
            filtered_df = filtered_df[
                ~filtered_df['title'].str.contains(pattern, case=False, regex=True, na=False)]

        return filtered_df

    def _filter_by_manager(self, df, directions):
        """根据manager过滤职位"""
        if directions != 'product':
            df = df[~df['title'].str.contains('manager', case=False, regex=True, na=False)]
        else:
            df = df[df['title'].str.contains('product manager', case=False, regex=True, na=False)]
        return df

    def _get_blocked_companies(self):
        """获取被屏蔽的公司列表"""
        try:
            block_db = json.loads(self.dependency_object.db.read(
                "block_companies",
                where=f"user_phone='{self.user}'",
                table='user_block')[0][0])
        except:
            block_db = []

        user_block_companies = block_db + self.user_dict.get('block', [])
        return (set(user_block_companies) if user_block_companies else set()) | (
            get_workday_companies() if self.workday_maintain_flag else set())

    def _filter_by_experience(self, df, experience):
        """根据经验要求过滤职位"""
        if experience == "junior":
            senior_titles = ['senior', 'sr']
            df = df[~df['title'].str.contains('|'.join(senior_titles), case=False)]
            logger.info(f"{self.user} skipped Senior")
        return df

    def _filter_jobs_by_location(self, df, preferred_locations=None, excluded_locations=None):
        """
        根据位置过滤职位，默认包含Remote职位
        Args:
            df: 职位DataFrame
            preferred_locations: 首选位置列表 (e.g., ['California', 'Washington'])
            excluded_locations: 排除位置列表 (e.g., ['Alaska', 'Hawaii'])
        Returns:
            过滤后的DataFrame，包含所有Remote职位和匹配的位置
        """
        filtered_df = df.copy()

        filtered_df['location'] = filtered_df['location'].fillna('')

        # 如果没有指定位置偏好，返回所有职位（包括Remote）
        if not preferred_locations and not excluded_locations:
            return filtered_df
        # 处理首选位置（自动包含Remote）
        if preferred_locations:
            # 确保Remote在位置列表中
            locations = set(preferred_locations) | {'Remote'}
            pattern = '|'.join(map(re.escape, locations))
            filtered_df = filtered_df[
                filtered_df['location'].str.contains(pattern, case=False, regex=True, na=False)
            ]

        # 处理排除位置（但保留Remote）
        if excluded_locations:
            pattern = '|'.join(map(re.escape, excluded_locations))
            # 保留Remote职位和不在排除列表中的职位
            filtered_df = filtered_df[
                (filtered_df['location'].str.contains('remote', case=False, regex=True, na=False)) |
                (~filtered_df['location'].str.contains(pattern, case=False, regex=True, na=False))
                ]

        return filtered_df

    def call_apply_fun(self):
        """主函数：处理和应用职位"""
        user_lock = self._user_locks[self.user]
        with user_lock:  # 每个用户独立锁
            with self._lock:
                if self.user not in self._user_instances:
                    max_retries = 3
                    for _ in range(max_retries):
                        try:
                            self._user_instances[self.user] = {
                                'wd': self.dependency_object.wd.driver_chrome_seen(),
                                'count': 0
                            }
                            break
                        except Exception as e:
                            if isinstance(e, WebDriverException) and (
                                    "version of ChromeDriver only supports" in str(
                                e) or "Current browser version is" in str(e)
                            ):
                                logger.error(
                                    "Mismatched Chrome and ChromeDriver versions. "
                                    "Please update your Chrome browser to the latest version."
                                )
                                sys.exit(1)
                            # Reset chromedriver check and let it re-download if needed
                            self.dependency_object.wd.reset_chromedriver_check()
                            logger.warning(f"WebDriver init failed, retrying... ({_ + 1}/{max_retries})")
                            time.sleep(2)
                    else:
                        raise RuntimeError(f"Failed to initialize WebDriver for user {self.user}")
            user_data = self._user_instances[self.user]
            user_data['count'] += 1

        try:
            # 获取用户偏好
            prefs = self._get_user_preferences()
            # 按关键词过滤职位
            self.filtered_jobs_df = self._filter_jobs_by_keywords(
                self.filtered_jobs_df,
                prefs['wanted_keywords'],
                prefs['unwanted_keywords']
            )

            # 获取被屏蔽公司
            block_company = self._get_blocked_companies()

            # 按经验过滤
            self.filtered_jobs_df = self._filter_by_experience(
                self.filtered_jobs_df,
                prefs['experience']
            )

            # 按位置过滤
            self.filtered_jobs_df = self._filter_jobs_by_location(
                self.filtered_jobs_df,
                prefs['preferred_locations'],
                prefs['excluded_locations']
            )

            # 如果不是pm，filter manager
            self.filtered_jobs_df = self._filter_by_manager(self.filtered_jobs_df, prefs['directions'])

            # filter block company
            if block_company:
                self.filtered_jobs_df = self.filtered_jobs_df[
                    ~self.filtered_jobs_df['company'].str.contains('|'.join(block_company), case=False, regex=True,
                                                                   na=False)]

            # 应用职位
            for company, fun in self.companies_apply_dict.items():
                # 跳过被屏蔽的公司
                if company in block_company:
                    continue

                # 使用用户级实例
                wd = user_data['wd']
                if not wd.service.is_connectable():
                    wd = self.dependency_object.wd.driver_chrome_seen()
                    user_data['wd'] = wd

                # 调用申请方法时传递实例
                for direction in prefs['directions']:
                    fun(self.user, self.dependency_object, direction).apply_jobs_with_limit(
                        company,
                        self.filtered_jobs_df,
                        wd,
                        prefs['company_dict'],
                    )

        finally:
            with self._lock:
                user_data['count'] -= 1
                if user_data['count'] == 0:
                    self._user_instances[self.user]['wd'].quit()
                    del self._user_instances[self.user]

    def debug_job_link(self, company: str, user: str, link: str):
        self.user = user
        wd = self.dependency_object.wd.driver_chrome_seen()
        custom_acc, custom_pwd = self.get_custom_credentials(user, company)
        self.companies_apply_dict[company](self.user, self.dependency_object, "").debug_link(wd, link, custom_acc,
                                                                                             custom_pwd)

    def get_custom_credentials(self, user: str, company: str):
        if self.user in self.user_dict and 'company' in self.user_dict[user]:
            custom_acc, custom_pwd = self.user_dict[user]['company'].get(company, ("", ""))
        else:
            custom_acc, custom_pwd = "", ""
        return custom_acc, custom_pwd
