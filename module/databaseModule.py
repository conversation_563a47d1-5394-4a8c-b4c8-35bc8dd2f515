import json

import mysql.connector

HOST, USER, PWD, DATABASE = "localhost", "root", "ZXY13798210752", "JobInfo"


class databaseModule:

    def __init__(self, host=None, pwd=None):
        self.connection = mysql.connector.connect(
            host=HOST if host is None else host, user=USER, password=PWD if pwd is None else pwd,
            database=DATABASE
        )
        self.cursor = self.connection.cursor(buffered=True)
        self.table = "job_info"

    def create(self, data: dict, table: str = None) -> None:
        columns = ", ".join(data.keys())
        placeholders = ", ".join(["%s"] * len(data))
        if table == 'user_block':
            json_column = 'block_companies'
            sql = f"SELECT {json_column} FROM user_block WHERE user_phone = '{data['user_phone']}'"
            self.cursor.execute(sql)
            existing_data = self.cursor.fetchone()
            if existing_data and existing_data[0]:
                existing_companies = json.loads(existing_data[0]) if existing_data[0] else []
                cur_company = data['block_companies'].replace('["', "").replace('"]', "")
                if cur_company not in existing_companies:
                    existing_companies = json.loads(existing_data[0]) if existing_data[0] else []
                    existing_companies.append(cur_company)
                    sql = f"UPDATE {table} SET {json_column} = %s WHERE user_phone = '{data['user_phone']}'"
                    self.cursor.execute(sql, (json.dumps(existing_companies),))
            else:
                sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                self.cursor.execute(sql, list(data.values()))
        else:
            if not table:
                sql = f"INSERT INTO {self.table} ({columns}) VALUES ({placeholders})"
                self.cursor.execute(sql, list(data.values()))
            else:
                sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                self.cursor.execute(sql, list(data.values()))
        self.connection.commit()

    def read(self, columns=None, where=None, table=None, join=None):
        if not columns:
            columns = "*"
        if not table:
            sql = f"SELECT {columns} FROM {self.table}"
        else:
            sql = f"SELECT {columns} FROM {table}"
        if join:
            sql += f" {join}"
        if where:
            sql += f" WHERE {where}"
        self.cursor.execute(sql)
        return self.cursor.fetchall()

    def update(self, data: str, where: str, table: str = None) -> int:
        if not table:
            sql = f"UPDATE {self.table} SET {data} WHERE {where}"
        else:
            sql = f"UPDATE {table} SET {data} WHERE {where}"
        self.cursor.execute(sql)
        self.connection.commit()
        return self.cursor.rowcount

    def delete(self, where: str, table: str = None) -> int:
        if not table:
            sql = f"DELETE FROM {self.table} WHERE {where}"
        else:
            sql = f"DELETE FROM {table} WHERE {where}"
        self.cursor.execute(sql)
        self.connection.commit()
        return self.cursor.rowcount

    def batch_create(self, data: list, table: str = None, columns: str = None, placeholders: str = None, update_stmt: str = None) -> None:
        if not table:
            sql = f"INSERT INTO {self.table} ({columns}) VALUES ({placeholders})"
        else:
            sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        if update_stmt:
            sql += f" ON DUPLICATE KEY UPDATE {update_stmt}"
        self.cursor.executemany(sql, data)
        self.connection.commit()

    def close_connection(self):
        self.connection.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.close_connection()
