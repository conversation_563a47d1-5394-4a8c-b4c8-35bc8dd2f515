from openai import OpenAI


class Deepseek:
    def __init__(self):
        self.client = OpenAI(api_key="sk-3607e027a1684118b627e7c6a1f72445", base_url="https://api.deepseek.com")

    def analyze_question(self, resume_content, question, answer_options):
        if answer_options:
            answer_options = f"Answer Options: {answer_options} return the most likely answer_options"
        else:
            answer_options = ""
        messages = [
            {
                "role": "system",
                "content": """You are an application assistant. Your task is to answer questions in job applications based on the provided resume. 
                You will be given a question and a context (the resume). If answer options are provided, return the most matched answer directly JUST THE ANSWER ITSELF. 
                If answer options are not provided, return the most likely answer based on the resume."""
            },
            {
                "role": "user",
                "content": f"Question: {question}\nContext: {resume_content}\n {answer_options}"
            }
        ]
        response = self.client.chat.completions.create(
            model="deepseek-chat",
            temperature=0.7,
            messages=messages,
            stream=False
        )
        return response.choices[0].message.content
