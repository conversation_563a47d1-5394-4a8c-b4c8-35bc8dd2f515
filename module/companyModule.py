from __future__ import annotations

import datetime
import json
import logging
import random
import re
import time

from loguru import logger
from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from undetected_chromedriver import Chrome

from module.databaseModule import databaseModule as DM
from module.experienceModule import experienceModule as EM
from module.geoModule import geoModule as GM
from module.hideElementModule import hideElementModule as HM
from module.requestsModule import requestModule as RM
from module.webdriverModule import WebdriverModule as WM

states = {
    'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas',
    'CA': 'California', 'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware',
    'FL': 'Florida', 'GA': 'Georgia', 'HI': 'Hawaii', 'ID': 'Idaho',
    'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa', 'KS': 'Kansas',
    'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
    'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi',
    'MO': 'Missouri', 'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada',
    'NH': 'New Hampshire', 'NJ': 'New Jersey', 'NM': 'New Mexico', 'NY': 'New York',
    'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio', 'OK': 'Oklahoma',
    'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
    'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah',
    'VT': 'Vermont', 'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia',
    'WI': 'Wisconsin', 'WY': 'Wyoming'
}

# Create reverse mapping for full names to abbreviations
states_reverse = {v.upper(): v for v in states.values()}


class companyModule:

    def __init__(self, company):
        self.experience = EM()
        self.geo = GM()
        self.db = DM()
        self.name = company.capitalize()
        self.hide = HM()
        self.wd = WM()
        self.re = RM()
        self.enable_apply = False
        self.keyword_list = ["Software", "Analyst", "Data", "Product Manager"]
        self.logger = logger

    def expect_shown_ele(self, wd, ele):
        WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, ele)))

    def wait_x_sec(self, sec: float) -> None:
        time.sleep(sec)

    def click_ele(self, wd: Chrome, s: str) -> None:
        def human_click(element):
            try:
                # 随机起始位置
                start_x = random.randint(50, 150)
                start_y = random.randint(50, 150)
                ActionChains(wd).move_by_offset(start_x, start_y).perform()

                # 生成移动路径
                loc = element.location_once_scrolled_into_view
                path = self._generate_human_path(
                    start=(start_x, start_y),
                    end=(loc['x'] + random.randint(-5, 5), loc['y'] + random.randint(-5, 5))
                )

                # 执行人类化移动
                actions = ActionChains(wd)
                for point in path:
                    actions.move_by_offset(point[0], point[1])
                    actions.pause(random.uniform(0.01, 0.05))

                # 随机点击方式
                if random.random() < 0.8:  # 80%正常点击
                    actions.click(element)
                else:  # 20%坐标偏移点击
                    offset_x = random.randint(-3, 3)
                    offset_y = random.randint(-3, 3)
                    actions.move_to_element_with_offset(element, offset_x, offset_y).click()

                # 点击后随机行为
                actions.pause(random.uniform(0.1, 0.3))
                actions.move_by_offset(random.randint(-10, 10), random.randint(-10, 10))
                actions.perform()

            except Exception as e:
                element.click()

        try:
            # 原有等待逻辑保持不变
            element = WebDriverWait(wd, 5).until(EC.presence_of_element_located((By.CSS_SELECTOR, s)))

            # 增强版滚动（保持原有scrollIntoView）
            wd.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            """, element)

            # 随机等待时间（保持原有等待机制）
            self.wait_x_sec(random.gauss(0.8, 0.2))  # 高斯分布更自然

            # 执行人类化点击
            human_click(element)

            # 保持原有等待时间
            self.wait_x_sec(random.uniform(1.0, 3.0))

        except Exception:
            try:
                # 改进的备用点击
                element = wd.find_element(By.CSS_SELECTOR, s)
                for _ in range(2):  # 有限重试
                    try:
                        ActionChains(wd).move_to_element(element).pause(
                            random.uniform(0.2, 0.5)
                        ).click().perform()
                        if random.random() < 0.3:
                            element.click()
                        break
                    except:
                        ActionChains(wd).move_by_offset(
                            random.randint(-10, 10),
                            random.randint(-10, 10)
                        ).perform()
            except:
                # 保持原有最终备用方案
                ActionChains(wd).move_to_element(s).click().perform()

    def _generate_human_path(self, start, end, steps=15):
        """生成类人鼠标移动路径（保持原有方法结构）"""
        path = []
        for i in range(steps):
            x = start[0] + (end[0] - start[0]) * (i / steps) + random.randint(-2, 2)
            y = start[1] + (end[1] - start[1]) * (i / steps) + random.randint(-2, 2)
            if 3 < i < steps - 3:
                x += random.randint(-3, 3)
                y += random.randint(-3, 3)
            path.append((x - (path[-1][0] if path else start[0]),
                         y - (path[-1][1] if path else start[1])))
        return path

    def fill_and_verify(self, wd: Chrome, selector: str, value: str, retries: int = 3) -> bool:
        """
        Fills an input field and verifies the content, with retries.

        Args:
            wd: The WebDriver instance.
            selector: The CSS selector for the input element.
            value: The string value to enter into the field.
            retries: The number of times to retry if verification fails.

        Returns:
            True if the field was successfully filled and verified, False otherwise.
        """
        for attempt in range(retries):
            try:
                element = WebDriverWait(wd, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )

                # Create a human-like sequence of actions.
                actions = ActionChains(wd)
                actions.move_to_element(element).pause(random.uniform(0.1, 0.4)).click()

                # Clear the field reliably before typing.
                element.clear()
                actions.pause(random.uniform(0.1, 0.2))

                # Type the value with small delays between keystrokes.
                for char in value:
                    actions.send_keys(char)
                    actions.pause(random.uniform(0.03, 0.1))
                actions.perform()

                # Verify that the element's value is what we expect.
                if element.get_attribute('value') == value:
                    self.logger.info(f"Successfully filled and verified element '{selector}'.")
                    return True
                else:
                    self.logger.warning(
                        f"Verification failed for element '{selector}' on attempt {attempt + 1}. "
                        f"Expected '{value}', got '{element.get_attribute('value')}'."
                    )
            except Exception as e:
                self.logger.warning(f"Exception on attempt {attempt + 1} for '{selector}': {e}")
        
        self.logger.error(f"Failed to fill and verify element '{selector}' after {retries} attempts.")
        return False

    def replace_content(self, wd: Chrome, ele: str, value: str) -> None:
        ele = wd.find_element(By.CSS_SELECTOR, ele)
        ele.clear()
        action = ActionChains(wd)
        action.click(ele).send_keys(value).perform()

    def server_unavailable(self, wd: Chrome) -> bool:
        try:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class="page-title"]')))
            if wd.find_element(By.CSS_SELECTOR, '[class="page-title"]').text == 'Workday is currently unavailable.':
                return True
        except:
            pass
        return False

    def numbers_present_in_element(self, wd: Chrome, css: str) -> bool:
        WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, css)))
        return bool(re.search(r'\d+', wd.find_element(By.CSS_SELECTOR, css).text))

    def workday_company_search(self, wd: Chrome, url: str, url_para=None) -> None:
        try:
            for keyword in self.keyword_list:
                if "?" in url:
                    cur_url = url + f"&q={keyword}"
                else:
                    cur_url = url + f"?q={keyword}"
                if url_para is not None:
                    cur_url += url_para
                wd.get(cur_url)
                if self.server_unavailable(wd):
                    logging.error(f"\t {self.name} server unavailable")
                    return
                WebDriverWait(wd, 10).until(
                    lambda driver: self.numbers_present_in_element(driver, '[data-automation-id="jobFoundText"]'))
                target = min(max((int(
                    wd.find_element(By.CSS_SELECTOR,
                                    '[data-automation-id="jobFoundText"]').text.split(" ")[0])) // 20 - 1, 1), 10)
                for _ in range(target):
                    for job, j_id, j_loc in zip(wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="jobTitle"]'),
                                                wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="subtitle"]'),
                                                wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="locations"]')
                                                ):
                        job_title = job.text
                        job_link = job.get_attribute('href').split("?")[0]
                        job_id = j_id.find_elements(By.CSS_SELECTOR, 'li')[0].text
                        if not bool(re.compile(r'(\d+)').search(job_id)):
                            job_id = j_id.find_elements(By.CSS_SELECTOR, 'li')[-1].text
                        link_loc = job_link.split("/")[-2]

                        def is_remote_location(location: str) -> bool:
                            remote_indicators = {
                                'United-States-Work-at-Home',
                                'United-States-of-America',
                                'United-States',
                                'US',
                                'Remote',
                                'Virtual',
                                'US-Anywhere'
                            }
                            return any(indicator == location for indicator in remote_indicators)

                        def is_non_us_location(location: str) -> bool:
                            non_us_locations = {
                                '-UK', '-CAN', '-In', '-India', '-Mexico', 'Bengaluru',
                                'Philippines', 'London', 'Toronto', 'Singapore', 'Bangalore',
                                'Great Britain', 'Nottingham', '-Canada', 'United-Kingdom', 'Japan', '-China', "Canada",
                                "-Qatar", "-Ireland", "-Netherlands", "-Italy", 'Mexico', '-France'
                            }
                            # Special case for India (but not Indiana)
                            if 'India' in location and 'Indiana' not in location:
                                return True
                            return any(loc in location for loc in non_us_locations)

                        if is_remote_location(link_loc):
                            link_loc = "Remote"
                        if is_non_us_location(link_loc):
                            continue
                        parts = link_loc.split('-')
                        job_state = (
                                self.get_job_location_by_job_link_location(link_loc) or
                                self.get_job_location_by_job_link_location(link_loc.replace("-", " ")) or
                                next((self.get_job_location_by_job_link_location(i) for i in parts), None) or
                                next((
                                    self.get_job_location_by_requests('-'.join(parts[:i]))
                                    for i in range(len(parts), 0, -1)
                                    if self.get_job_location_by_requests('-'.join(parts[:i]))
                                ), None) or
                                (self.get_job_location_by_requests(
                                    link_loc.split("--")[1]) if "--" in link_loc else None) or
                                next((
                                    self.get_job_location_by_requests('-'.join(parts[i:]))
                                    for i in range(len(parts))
                                    if self.get_job_location_by_requests('-'.join(parts[i:]))
                                ), None)
                        )
                        additional_link_loc = {
                            "US---NELC-PRATT--WHITNEY-10364-NHLDR": "Florida",
                            "US---DALLASCOPPELL-TXCPP": "Texas"
                        }
                        if link_loc in additional_link_loc.keys():
                            job_state = additional_link_loc[link_loc]
                        if not job_state:
                            logging.error(f"Job location not found: {link_loc}")
                            job_loc = "Not Found"
                        else:
                            job_loc = job_state
                        if "clearance" in job_title: continue
                        if self.experience.whatIWant(job_title) and len(
                                self.db.read(columns="jobid",
                                             where=f"jobid = '{job_id}' and company = '{self.name}'")) == 0:
                            self.db.create(
                                {"company": f"{self.name}", "jobid": f"{job_id}", "title": f"{job_title}",
                                 "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                                 "location": f"{job_loc}"})
                    try:
                        self.click_ele(wd, '[aria-label="next"]')
                        self.wait_x_sec(4)
                    except:
                        pass
        except Exception as e:
            self.logger.error(f"Error during company search: {str(e)}")
            raise

    def get_job_location_by_requests(self, location) -> str:
        location = location.replace(" ", "-")
        request = self.re.sendRequest(f"http://localhost:8080/search.php?q={location}&format=jsonv2")
        if request.status_code == 200:
            response = request.text
            data = json.loads(response)
            if data and isinstance(data, list) and len(data) > 0:
                display_name = data[0].get("display_name", "")
                parts = display_name.split(", ")
                for part in parts:
                    if part in states.values() or part in states_reverse.keys():
                        return part
        else:
            return None

    def get_job_location_by_job_link_location(self, location):
        if not location:
            return None
        # Add direct city to state mapping
        city_to_state = {
            'Santa Clara': 'California',
            'San Francisco': 'California',
            'New York City': 'New York',
            'Los Angeles': 'California',
            'Chicago': 'Illinois',
            'Houston': 'Texas',
            'Phoenix': 'Arizona',
            'Philadelphia': 'Pennsylvania',
            'San Antonio': 'Texas',
            'San Diego': 'California',
            'Dallas': 'Texas',
            'San Jose': 'California',
            'Austin': 'Texas',
            'Jacksonville': 'Florida',
            'Fort Worth': 'Texas',
            'Columbus': 'Ohio',
            'Charlotte': 'North Carolina',
            'Las Vegas': 'Nevada',
            'Seattle': 'Washington',
            'Denver': 'Colorado',
            'Washington': 'District of Columbia',
            'Boston': 'Massachusetts',
            'Portland': 'Oregon',
            'Minneapolis': 'Minnesota',
            'Atlanta': 'Georgia',
            'Miami': 'Florida',
            'Oakland': 'California',
            'Tampa': 'Florida',
            'Orlando': 'Florida',
            'Lowes-Charlotte-Technology-Hub-3505': "Florida",
            'USA': 'Remote'
        }
        # Check direct city mapping first
        if location in city_to_state:
            return city_to_state[location]

        if location.startswith('USA-'):
            parts = location.split('-')
            state_abbrev = parts[1]
            if state_abbrev in states:
                return states[state_abbrev]

        if location.startswith('USA'):
            location = location[3:5]
            for state in states.keys():
                if location.startswith(state):
                    return state

        if ',' in location:
            parts = [part.strip() for part in location.split(',')]
            for part in parts:
                # Check for state abbreviation
                if len(part) == 2 and part in states:
                    return states[part]
                # Check for full state name
                if part in states.values():
                    return part
        # Handle US-STATE-CITY pattern
        us_state_match = re.match(r'US-([A-Z]{2})-', location)
        if us_state_match:
            state_abbrev = us_state_match.group(1)
            if state_abbrev in states:
                return states[state_abbrev]

        # Handle CITY-STATE pattern (e.g., McLean-VA, Plano-TX)
        city_state_match = re.search(r'-([A-Z]{2})(?:-|$)', location)
        if city_state_match:
            state_abbrev = city_state_match.group(1)
            if state_abbrev in states:
                return states[state_abbrev]

        # Split the string and clean up
        parts = re.split(r'[-\s,]+', location)

        # Check for two-word state names first
        for i in range(len(parts)):
            if i < len(parts) - 1:
                two_word = f"{parts[i]} {parts[i + 1]}"
                if two_word.upper() in states_reverse:
                    return states_reverse[two_word.upper()]

        # Split the string and look for state abbreviations
        parts = location.split('-')
        for part in parts:
            if part in states:
                return states[part]
            # Check if the part ends with a state abbreviation
            if len(part) > 2 and part[-2:] in states:
                return states[part[-2:]]

        # Add check for City-FullState-Country pattern (e.g., Irving-Texas-United-States)
        parts = location.split('-')
        for i, part in enumerate(parts):
            if part in states.values():  # Check for full state names
                return part
        if "VIRTUAL" in location.upper() or "REMOTE" in location.upper():
            return "Remote"
        return None

    def close(self):
        if hasattr(self, 'db'):
            self.db.close_connection()

    def __del__(self):
        self.close()
