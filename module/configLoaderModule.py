import json
import importlib
import os
import glob
import sys
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading
from module.loggingModule import logger

class ConfigLoader:
    def __init__(self):
        self._lock = threading.Lock()
        self.user_dict = {}
        self.companies_dict = {}
        self.reload_configs()
        self.observer = Observer()
        self.setup_file_watcher()
        
    def setup_file_watcher(self):
        event_handler = self.ConfigHandler(self.reload_configs)
        self.observer.schedule(event_handler, 'application', recursive=False)
        self.observer.schedule(event_handler, '.', recursive=False)
        self.observer.start()

    def stop_watching(self):
        self.observer.stop()
        self.observer.join()

    class ConfigHandler(FileSystemEventHandler):
        def __init__(self, callback):
            self.callback = callback
            self._lock = threading.Lock()
            self._last_reload = 0
            self.RELOAD_COOLDOWN = 1

        def on_modified(self, event):
            if event.src_path.endswith(('.py', '.json')):
                current_time = time.time()
                with self._lock:
                    if current_time - self._last_reload > self.RELOAD_COOLDOWN:
                        self.callback()
                        self._last_reload = current_time

    def load_user_dict(self):
        try:
            with open('user_dict.json', 'r') as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"Failed to load user_dict: {e}")
            return {}

    def load_companies_dict(self):
        companies_dict = {}
        for file_path in glob.glob(os.path.abspath("application/*.py")):
            try:
                company_name = os.path.splitext(os.path.basename(file_path))[0]
                if f"application.{company_name}" in sys.modules:
                    job = importlib.reload(sys.modules[f"application.{company_name}"])
                else:
                    job = importlib.import_module(f"application.{company_name}")
                companies_dict[company_name] = getattr(job, f"{company_name}Apply")
            except Exception as e:
                logger.error(f"Failed to load company {company_name}: {e}")
        return companies_dict

    def reload_configs(self):
        with self._lock:
            logger.info("Reloading configurations...")
            self.user_dict = self.load_user_dict()
            self.companies_dict = self.load_companies_dict()
            logger.info("Configurations reloaded successfully")

    def get_configs(self):
        with self._lock:
            return self.user_dict.copy(), self.companies_dict.copy()