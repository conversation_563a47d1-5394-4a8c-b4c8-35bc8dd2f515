import datetime
import email
import imaplib
import json
import logging
import os
import random
import re
import sys
import threading
from abc import ABC, abstractmethod
from re import Match
from typing import Optional

import pandas as pd
import PyPDF2
from docx import Document
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait
from undetected_chromedriver import Chrome

from module.deepseek import Deepseek
from module.emailModule import EmailSender
from module.loggingModule import Logger
from user_info import read_info


class applicationInterface(ABC):
    def __init__(self, user: str, dependency_object, direction: str):
        self.wd = None
        self.apply = dependency_object
        self.database = self.apply.db
        self.hide = self.apply.hide
        self.click_ele = self.apply.click_ele
        self.wait_x_sec = self.apply.wait_x_sec
        self.replace_content = self.apply.replace_content
        self.direction = direction
        self.company = ""
        self.user = user
        self.logger = Logger().logger
        self.lock = threading.Lock()
        self.user_active, self.user_name, self.user_account, self.user_pwd, self.user_address, self.user_city, self.user_state, \
            self.user_zipcode, self.user_phone, self.user_gender_male, self.user_linkedin, self.user_visa, \
            self.user_title, self.user_company, self.user_start_time, self.user_end_time, self.user_role_desc, \
            self.user_school, self.user_degree, self.user_school_start_time, self.user_school_end_time, self.user_email_code = read_info(
            self.user)
        self.user_resume_path = \
            f"""{"/".join(os.path.realpath(__file__).split("/")[:-2])}/resume/{'_'.join(self.user_name.split())}_Resume"""
        self.page_not_found_pattern = re.compile(
            r"(?i)\b(page\s+)?not\s+found\b|\berror\s+404\b|unavailable|does\s+not\s+exist|404")
        self.email_sender = EmailSender()
        self.user_visa_ans = "Yes" if self.user_visa == 'H1b' else "No"
        self.common_dict = {
            # Work Eligibility Questions
            "Are you able to work": "Yes",
            "Are you at least 18 years": "Yes",
            "Are you eligible to work": "Yes",
            "Are you legally authorized to work": "Yes",
            "Are you currently authorized to work": "Yes",
            "Are you authorized to work": "Yes",
            # Visa Sponsorship Questions (using user's visa answer)
            "Do you need visa sponsorship": self.user_visa_ans,
            "Do you now or in the future require": self.user_visa_ans,
            "Do you now, or will you in the future": self.user_visa_ans,
            "Do you now or will you in the future": self.user_visa_ans,
            "Will you now or in the future": self.user_visa_ans,
            "Do you, now or in the future": self.user_visa_ans,
            "Will you need sponsorship": self.user_visa_ans,
            "Will you need a visa": self.user_visa_ans,
            "Do you require visa sponsorship": self.user_visa_ans,
            # School
            "Start date year": self.user_school_start_time.split("/")[-1],
            "End date year": self.user_school_end_time.split("/")[-1],
            # Experience and Skills Questions
            "Do you have experience with": "Yes",
            "Are you proficient": "Yes",
            # Company and Title Information (using user's input)
            "Current Company": self.user_company,
            "Most Recent Employer": self.user_company,
            "Current Title": self.user_title,
            "Most Recent Job Title": self.user_title,
            # Personal Information (using user's input)
            "Preferred Name": self.user_name,
            "Legal Name": self.user_name,
            "Full Legal Name": self.user_name,
            "What is your Legal First Name": self.user_name.split()[0],
            "What is your Legal Last Name": self.user_name.split()[-1],
            "School": self.user_school,
            "Discipline": "Computer Science",
            # Location and Contact Information (using user's input)
            "Which state": self.user_state,
            "LinkedIn": self.user_linkedin,
            "LinkedIn Profile": self.user_linkedin,
            "Other Links": self.user_linkedin,
            "Website": self.user_linkedin,
            "Link to Portfolio": self.user_linkedin,
            "Street Address": self.user_address,
            "City/Town": self.user_city,
            "Zip": self.user_zipcode,
            "Country": "United States",
            "Degree": self.user_degree + "'s" if self.user_degree != 'PhD' else "Doctor of Philosophy",
            # Job Application Process Questions
            "How did you hear about": ["LinkedIn", "Social media"],
            "Have you been employed": "No",
            # demographic question
            "Please identify your gender": "Male" if self.user_gender_male else "Female",
            "Please identify your ethnicity": "East Asian",
            "Please identify your race": "Asian",
            "Please identify your veteran status": "I am not",
            "Please identify your disability status": "No",
            "Indicate Gender": "Male" if self.user_gender_male else "Female",
            "Indicate Ethnic": "Not Hispanic or Latino",
            "Indicate your Race": "Asian",
            "If you believe you belong to any": "I am not",
            "What is your race": "East Asian",
            "What is your military status": "I have never",
            "What is your disability status": "No",
            "Are you Hispanic": ["Decline", "I don't"],
            "Gender": ["Male" if self.user_gender_male else "Female", "Man" if self.user_gender_male else "Woman"],
            "Veteran Status": ["No", "I am not"],
            "Disability Status": "No",
            "How would you describe your gender": ["I don't wish",
                                                   "Male" if self.user_gender_male else "Female",
                                                   "Man" if self.user_gender_male else "Woman"],
            "I identify as transgender": "No",
            "I identify my race": "East Asian",
            "I have a disability": "No",
            "I identify as a first-generation professional": "No",
            "How would you describe your racial": "East Asian",
            "How would you describe your sexual orientation": "Heterosexual",
            "Do you identify as transgender": "No",
            "Do you have a disability or chronic condition": "No",
            "Are you a veteran": "No",
            "Please review Form CC-305": "No",
            "What is the name of your current company": self.user_company,
            "What gender identity": "Male" if self.user_gender_male else "Female",
            "Are you a person of transgender": "No",
            "What sexual orientation": "Heterosexual",
            "Do you live with a disability": "No",
            "Please select up to 2 ethnicities": "East Asian",
            "Current Job Title & Employer": ", ".join([self.user_title, self.user_company]),
            "How would you describe your sexual": "Heterosexual",
            "Do you have a disability": "No",
            "I identify my gender as": "Man" if self.user_gender_male else "Woman",
            "I identify as": "I don't wish to answer",
            "I identify my sexual orientation as": ["Straight/Heterosexual", "I don't"],
            "I identify my race/ethnicity as": "I don't wish to answer",
            "What is your gender": ["Male" if self.user_gender_male else "Female",
                                    "Man" if self.user_gender_male else "Woman",
                                    "Cisgender man" if self.user_gender_male else "Cisgender woman"],
            "What is your sexual orientation": "Straight",
            "What race/ethnicity do you identify as": "Asian",
            "Do you identify": "No",
            "What is your race and/or ethnicity?": "Asian",
            "Military Veteran Status": "I am not a Protected Veteran",
            "Please indicate which best describes your race": "Asian",
            "Disability": "No",
            "Are you a Canadian Armed Forces": "No",
            "Please note your experience administering 401k": "2",
            "How many participants did the 401k plan": "0",
        }
        self.greenhouse_list_idx = 0
        self.deepseek = Deepseek()
        self.user_resume_content = None

    def filtered_jobs_by_user(self, company: str, upcoming_jobs_df: pd.DataFrame) -> list[tuple[str, str]]:
        filters = {
            'software': ['software', 'application', 'backend', 'reliability engineer', 'cloud', 'development engineer',
                         'python', 'java', 'system engineer', 'sre', 'SW', 'qa engineer', 'solutions architect',
                         'solution architect', 'platform engineer', 'back end', 'back-end', 'azure', 'aws',
                         'gcp', 'infrastructure engineer'],
            'analyst': ['data analyst', 'business intelligence', 'analyst', 'analytics'],
            'data': ['data scientist', 'data engineer', 'data architect', 'database', 'machine learning']
        }
        selected_keywords = ""
        unselected_keywords = ""
        if self.direction in filters:
            selected_keywords = filters[self.direction]
            unselected_keywords = list(set().union(*filters.values()) - set(selected_keywords)) + ['research']
            self.user_resume_path += ".pdf"
            self.user_resume_content = self.get_resume_content(self.user_resume_path)
            if not os.path.exists(self.user_resume_path):
                self.logger.error("Resume Not Found for User: " + self.user_phone)
                sys.exit(0)
        with self.lock:
            applied_job_id = [i[0] for i in
                              self.database.read("job_id", f"user_id = '{self.user}' and company = '{company}'",
                                                 "application_status")]
        filtered_df = upcoming_jobs_df[
            ((upcoming_jobs_df['company'] == company.title())
             & upcoming_jobs_df['title'].str.contains('|'.join(selected_keywords), flags=re.IGNORECASE, regex=True)
             & ~upcoming_jobs_df['title'].str.contains('|'.join(unselected_keywords), flags=re.IGNORECASE, regex=True)
             & ~upcoming_jobs_df['jobid'].isin(applied_job_id))
        ].head(1)
        return [(row['jobid'], row['link']) for _, row in filtered_df.iterrows()]

    def is_page_not_found(self, page_title: str) -> Optional[Match[str]]:
        return self.page_not_found_pattern.search(page_title)

    @abstractmethod
    def applyIt(self, url: str, wd: Chrome):
        pass

    def apply_job_worker(self, job: tuple[str, str]) -> None:
        job_id, url = job
        try:
            result = self.applyIt(url, self.wd)
            if result == 0:
                self.logger.error(f"{url}, {self.user}")
            elif result == -3:
                self.logger.warning(f"{self.company.title()} server unavailable")
            elif result == -2:
                with self.lock:
                    self.database.create(
                        {"user_phone": self.user, "block_companies": json.dumps([self.company])}, 'user_block')
                self.logger.info(f"{self.user} not applied before for {self.company.title()}.")
            elif result == -1:
                with self.lock:
                    self.database.create(
                        {"user_phone": self.user, "block_companies": json.dumps([self.company])}, 'user_block')
                self.logger.warning(f"{self.user} account or password incorrect for {self.company.title()}.")
            # reach limit
            elif result == 99:
                self.logger.warning(f"{self.user} application limited for {self.company.title()}.")
            elif result == 1:
                with self.lock:
                    self.database.create(
                        {"user_id": self.user, "job_id": job_id, "company": self.company},
                        'application_status')
                try:
                    account_balance = \
                        self.database.read("user_balance", f"user_phone = '{self.user_phone}'", "user_balance")[0][0]
                    current_time = \
                        self.database.read("user_end_time", f"user_phone = '{self.user_phone}'", "user_balance")[0][0]
                    self.database.update(f"user_balance = {account_balance - 1}", f"user_phone = '{self.user_phone}'",
                                         "user_balance")
                    current_balance = \
                        self.database.read("user_balance", f"user_phone = '{self.user_phone}'", "user_balance")[0][0]
                    if current_balance <= 0:
                        self.database.update("user_active = 0", f"user_phone = '{self.user_phone}'", "user_info")
                        self.logger.warning(f"User: {self.user_phone}, Balance: {current_balance}")
                    if datetime.datetime.now().date() > current_time:
                        self.database.update("user_active = 0", f"user_phone = '{self.user_phone}'", "user_info")
                        self.logger.warning(f"User: {self.user_phone}, End Time: {current_time}")
                except Exception:
                    pass
        except Exception as e:
            self.logger.error(f"{url}, {self.user_phone}")
        finally:
            try:
                self.wd.get("about:blank")
                WebDriverWait(self.wd, 10).until(
                    lambda driver: driver.execute_script('return document.readyState') == 'complete'
                )
            except Exception as e:
                try:
                    self.wd.close()
                except Exception:
                    self.logger.warning(f"Failed to clean up WebDriver state: {str(e)}")

    def apply_jobs_with_limit(self, company, filtered_jobs_df, wd_instance, custom_company_dict=None):
        self.wd = wd_instance
        self.company = company
        if self.user_company.lower() == company or self.user_company.lower().replace(" ", "") == company:
            return
        if custom_company_dict and company in custom_company_dict:
            custom_account, custom_pwd = custom_company_dict[company]
            self.user_account = custom_account or self.user_account
            self.user_pwd = custom_pwd or self.user_pwd
        jobid_with_link_list = self.filtered_jobs_by_user(company, filtered_jobs_df)
        if jobid_with_link_list:
            for job in jobid_with_link_list:
                self.apply_job_worker(job)

    def debug_link(self, wd: Chrome, link: str, custom_account: str = None, custom_pwd: str = None) -> None:
        self.user_account = custom_account if custom_account else self.user_account
        self.user_pwd = custom_pwd if custom_pwd else self.user_pwd
        filtered_info = self.database.read('company, title, jobid, link', f"link = '{link}';")
        if not filtered_info:
            return
        company, title, job_id, url = filtered_info[0]
        self.company = company
        self.user_resume_path += '.pdf'
        self.user_resume_content = self.get_resume_content(self.user_resume_path)
        try:
            os.path.exists(self.user_resume_path)
        except:
            self.logger.error("Resume Not Found for User: " + self.user_phone)
            return
        ret = self.applyIt(url, wd)
        if ret == 1:
            self.database.create({"user_id": f"{self.user}", "job_id": f"{job_id}", "company": f"{company.lower()}"},
                                 'application_status')
            self.logger.warning(f"Insert {job_id} for {self.user}.")
        elif ret == -3:
            self.logger.error("Workday unavailable.")
        elif ret == -1:
            self.logger.error("User account/pwd incorrect")
        elif ret == -4:
            self.logger.error("Link deleted.")
        elif ret == 99:
            self.logger.warning(f"{self.user} application limited for {self.company.title()}.")
        else:
            try:
                logging.error(f"""{wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text}""")
            except:
                logging.error(f"""{url}, {self.user}""")

    def greenhouse_info(self, wd: Chrome, url: str) -> int:
        try:
            self.expected_shown_element(wd, '[id="flash_pending"]')
            if wd.find_element(By.CSS_SELECTOR, '[id="flash_pending"]').text.startswith(
                    "The job you are looking for is no longer open."):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            if "error=true" in wd.current_url:
                self.database.delete(f"link = '{url}'")
                return -4
        # NG
        try:
            ng = [str(datetime.datetime.now().year + 1), "graduate"]
            if any(k in wd.current_url for k in ng):
                if len(self.database.read("link", f"link='{url}'", 'job_info_ng')) == 0:
                    self.database.create({"company": f"{self.company.lower()}", "link": url}, 'job_info_ng')
                    self.logger.warning(f"Found New Grad position: {url}")
                return -4
        except:
            pass
        try:
            self.expected_shown_element(wd, '[class="error-message font-secondary"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="error-message font-secondary"]').text.startswith(
                    "Page not found"):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        try:
            if wd.find_element(By.XPATH, '//*[@id="main"]/p').text == "Sorry, but we can't find that page.":
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            try:
                self.expected_shown_element(wd, '[class="error-message font-secondary"]')
                if wd.find_element(By.CSS_SELECTOR, '[class="error-message font-secondary"]').text.startswith(
                        "Page not found"):
                    self.database.delete(f"link = '{url}'")
                    return -4
            except:
                pass
        try:
            self.expected_shown_element(wd, '[id="grnhse_iframe"]')
            wd.switch_to.frame(wd.find_element(By.CSS_SELECTOR, '[id="grnhse_iframe"]'))
        except:
            self.expected_shown_element(wd, 'iframe')
            wd.switch_to.frame(wd.find_element(By.CSS_SELECTOR, 'iframe'))
        try:
            self.expected_shown_element(wd, '[id="first_name"]')
        except:
            wd.switch_to.default_content()
            self.expected_shown_element(wd, '[id="first_name"]')
        self.click_ele(wd, '[id="first_name"]')
        first_name = wd.find_element(By.CSS_SELECTOR, '[id="first_name"]')
        first_name.click()
        first_name.send_keys(self.user_name.split()[0])
        last_name = wd.find_element(By.CSS_SELECTOR, '[id="last_name"]')
        last_name.click()
        last_name.send_keys(self.user_name.split()[1])
        wd.find_element(By.CSS_SELECTOR, '[id="email"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="phone"]').send_keys(self.user_phone)
        try:
            self.click_ele(wd, '[name="job_application[location]"]')
            ele = wd.find_element(By.CSS_SELECTOR, '[name="job_application[location]"]')
            ele.send_keys(
                ", ".join([self.user_city, self.user_state, "United States"]))
            self.expected_shown_element(wd, '[id="location_autocomplete-items-popup-option-0"]')
            self.click_ele(wd, '[id="location_autocomplete-items-popup-option-0"]')
        except:
            try:
                self.click_ele(wd, '[id="candidate-location"]')
                ele = wd.find_element(By.CSS_SELECTOR, '[id="candidate-location"]')
                ele.send_keys(
                    ", ".join([self.user_city, self.user_state, "United States"]))
                self.expected_shown_element(wd, '[id="react-select-candidate-location-option-0"]')
                self.click_ele(wd, '[id="react-select-candidate-location-option-0"]')
            except:
                pass
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        return 1

    def greenhouse_demographic_questions(self, wd: Chrome, question_dict: dict) -> None:
        try:
            wd.find_element(By.CSS_SELECTOR, '[id="demographic_questions"]') \
                .find_elements(By.CSS_SELECTOR, '[class="field demographic_question required"]')
        except:
            return
        for question in wd.find_element(By.CSS_SELECTOR, '[id="demographic_questions"]') \
                .find_elements(By.CSS_SELECTOR, '[class="field demographic_question required"]'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="asterisk"]')
            except:
                continue
            question_text = question.text.strip()
            actual_key = next((k for k in question_dict if question_text.startswith(k)), None)
            if actual_key:
                possible_ans_values = question_dict[actual_key]
                if isinstance(possible_ans_values, list):
                    for possible_ans in possible_ans_values:
                        for ans in list(filter(lambda x: x.text.strip().startswith(possible_ans),
                                               question.find_elements(By.CSS_SELECTOR, 'label'))):
                            try:
                                input_ele = ans.find_element(By.CSS_SELECTOR, "input")
                                break
                            except:
                                continue
                else:
                    for ans in list(filter(lambda x: x.text.strip().startswith(possible_ans_values),
                                           question.find_elements(By.CSS_SELECTOR, 'label'))):
                        input_ele = ans.find_element(By.CSS_SELECTOR, "input")
                ele = f'[value="{input_ele.get_attribute("value")}"]'
                try:
                    self.click_ele(wd, ele)
                except:
                    input_ele.find_element(By.XPATH, "..").click()
            else:
                self.logger.error(f"Company: {self.company.title()}, Question: {question_text}")

    def greenhouse_eeoc_questions(self, wd: Chrome, question_dict: dict) -> None:
        try:
            wd.find_element(By.CSS_SELECTOR, '[id="eeoc_fields"]').find_elements(By.CSS_SELECTOR,
                                                                                 '[data-eeoc-question]')
        except:
            return
        for question in wd.find_element(By.CSS_SELECTOR, '[id="eeoc_fields"]').find_elements(By.CSS_SELECTOR,
                                                                                             '[data-eeoc-question]'):
            question_text = question.text.strip()
            try:
                question.find_element(By.CSS_SELECTOR, '[class="asterisk"]')
            except:
                continue
            if any(question_text.startswith(key) for key in question_dict.keys()):
                key = next(filter(lambda x: question_text.startswith(x), question_dict.keys()))
                p_id = question.find_element(By.CSS_SELECTOR, '[class="select2-container"]').get_attribute("id")
                search_id = question.find_element(By.CSS_SELECTOR, '[class="select2-container"]').find_element(
                    By.CSS_SELECTOR, 'label').get_attribute("for")
                search_ele = wd.find_element(By.CSS_SELECTOR, f'input[id="{search_id}_search"]')
                self.click_ele(wd, f'[id="{p_id}"]')
                aria_control_id = search_ele.get_attribute("aria-controls")
                self.wait_x_sec(1)
                possible_ans = None
                if isinstance(question_dict[key], list):
                    for possible_ans in question_dict[key]:
                        try:
                            ans = next(filter(lambda x: x.text.startswith(possible_ans),
                                              wd.find_element(By.CSS_SELECTOR,
                                                              f'[id="{aria_control_id}"]').find_elements(
                                                  By.CSS_SELECTOR, 'li'))).find_element(By.CSS_SELECTOR,
                                                                                        'div').get_attribute("id")
                            break
                        except:
                            pass
                else:
                    possible_ans = question_dict[key]
                    ans = next(filter(lambda x: x.text.startswith(possible_ans),
                                      wd.find_element(By.CSS_SELECTOR,
                                                      f'[id="{aria_control_id}"]').find_elements(
                                          By.CSS_SELECTOR, 'li'))).find_element(By.CSS_SELECTOR,
                                                                                'div').get_attribute("id")
                self.click_ele(wd, f'[id="{ans}"]')

    def expected_shown_element(self, wd: Chrome, ele: str) -> None:
        try:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, ele)))
        except:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.XPATH, ele)))

    def greenhouse_submit(self, wd: Chrome, customized_search_str: str = None) -> int:
        try:
            self.click_ele(wd, '[id="job_application_data_compliance_gdpr_demographic_data_consent_given"]')
        except:
            pass
        try:
            self.click_ele(wd, '[name="gdpr_demographic_data_consent_given"]')
        except:
            pass
        try:
            # old greenhouse submit
            self.expected_shown_element(wd, '[id="submit_app"]')
            self.click_ele(wd, 'input[type="button"][id="submit_app"]')
        except:
            # new greenhouse submit
            self.click_ele(wd, 'button[type="submit"]')
        try:
            try:
                WebDriverWait(wd, 5).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[id="security_code"]')))
            except:
                WebDriverWait(wd, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[id="email-verification"]')))
            if not self.user_email_code:
                raise
            company_name = {
                "Creditkarma": "Credit Karma",
                "Appliedintuition": "Applied Intuition",
                "Hudsonrivertrading": "Hudson River Trading",
                "Gitlab": "GitLab",
                "Mongdb": "MongoDB",
                "Thetradedesk": "The Trade Desk"
            }
            if customized_search_str:
                search_str = customized_search_str
            else:
                search_str = company_name[self.company] if self.company in company_name.keys() else self.company
            verification_code = self.get_verification_from_gmail(
                f"Security code for your application to {search_str}")

            if verification_code:
                try:
                    # new security code
                    security_inbox = wd.find_element(By.CSS_SELECTOR,
                                                     '[class="email-verification__wrapper"]').find_elements(
                        By.CSS_SELECTOR,
                        'input')
                    for code, inputs in zip(verification_code, security_inbox):
                        p_id = inputs.get_attribute('id')
                        self.click_ele(wd, f'[id="{p_id}"]')
                        inputs.send_keys(code)
                    self.click_ele(wd, 'button[type="submit"]')
                except:
                    # old security code
                    security_inbox = wd.find_element(By.CSS_SELECTOR, '[id="security_code"]')
                    self.click_ele(wd, security_inbox)
                    for code in verification_code:
                        security_inbox.send_keys(code)
                        self.wait_x_sec(random.uniform(0.4, 1.3))
                    self.click_ele(wd, 'input[id="submit_app"]')
            else:
                self.logger.error(f"No verification code found for {self.company}")
        except:
            pass
        try:
            try:
                WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '[id="application_confirmation"]')))
            except:
                WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '[class="confirmation"]')))
            return 1
        except:
            return 0

    def greenhouse_question_answer_new(self, wd, question_dict):
        question_dict = {**self.common_dict, **question_dict}
        for question in (wd.find_elements(By.CSS_SELECTOR, '[class="select"]')
                         + wd.find_elements(By.CSS_SELECTOR, '[class="text-input-wrapper"]')
                         + wd.find_elements(By.CSS_SELECTOR, 'fieldset[class="checkbox"]')):
            question_text = question.text.strip()
            if "*" not in question_text or any(
                    question_text.startswith(key) for key in
                    ["First Name", "Last Name", "Email", "Phone", "Location"]): continue
            p_id = question.find_element(By.CSS_SELECTOR, 'label').get_attribute('for')
            question_ans = None
            answer_options = None
            try:
                question_ans = question_dict[
                    list(filter(lambda x: question_text.startswith(x), question_dict.keys()))[0]]
            except:
                self.click_ele(wd, f'[id="{p_id}"]')
                try:
                    self.expected_shown_element(wd, f'[id="react-select-{p_id}-listbox"]')
                    answer_options = [option.text for option in wd.find_element(By.CSS_SELECTOR,
                                                                                f'[id="react-select-{p_id}-listbox"]').find_elements(
                        By.CSS_SELECTOR, '[role="option"]')]
                    self.click_ele(wd, f'[id="{p_id}"]')
                except:
                    try:
                        self.expected_shown_element(wd, f'[id="react-select-{p_id}-placeholder"]')
                        answer_options = [option.text for option in wd.find_element(By.CSS_SELECTOR,
                                                                                    f'[id="react-select-{p_id}-placeholder"]').find_elements(
                            By.CSS_SELECTOR, '[role="option"]')]
                        self.click_ele(wd, f'[id="{p_id}"]')
                    except:
                        pass
                question_ans = self.deepseek.analyze_question(self.user_resume_content, question_text,
                                                              answer_options or None)
                if not question_ans:
                    self.logger.error(f"Company: {self.company.title()}, Question: {question_text}")
                    return
            self.click_ele(wd, f'[id="{p_id}"]')
            if question_ans:
                try:
                    # select
                    self.expected_shown_element(wd, f'[id="react-select-{p_id}-listbox"]')
                    if type(question_ans) is list:
                        for q_ans in question_ans:
                            try:
                                ans = list(filter(lambda x:
                                                  x.text.startswith(q_ans),
                                                  wd.find_element(By.CSS_SELECTOR,
                                                                  f'[id="react-select-{p_id}-listbox"]')
                                                  .find_elements(By.CSS_SELECTOR, '[role="option"]')))[0]
                                if ans in answer_options:
                                    break
                            except:
                                pass
                    else:
                        ans = list(filter(lambda x:
                                          x.text.startswith(question_ans),
                                          wd.find_element(By.CSS_SELECTOR,
                                                          f'[id="react-select-{p_id}-listbox"]')
                                          .find_elements(By.CSS_SELECTOR, '[role="option"]')))[0]
                    a_id = ans.get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                except:
                    try:
                        # placeholder
                        self.expected_shown_element(wd, f'[id="react-select-{p_id}-placeholder"]')
                        question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(question_ans)
                        self.wait_x_sec(2)
                        ans = list(filter(lambda x:
                                          x.text.startswith(question_ans),
                                          wd.find_element(By.CSS_SELECTOR,
                                                          f'[id="react-select-{p_id}-listbox"]')
                                          .find_elements(By.CSS_SELECTOR, '[role="option"]')))[0]
                        a_id = ans.get_attribute('id')
                        self.click_ele(wd, f'[id="{a_id}"]')
                        continue
                    except:
                        pass
                    try:
                        if type(question_ans) is list:
                            question_ans = question_ans[0]
                        try:
                            # input text
                            question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(question_ans)
                        except:
                            question.find_element(By.CSS_SELECTOR, 'input[type="number"]').send_keys(question_ans)
                    except:
                        # textarea
                        ele = wd.find_element(By.CSS_SELECTOR, f'[id="{p_id}"]')
                        if ele.tag_name.lower() == 'textarea':
                            ele.send_keys(question_ans)
            else:
                self.logger.error(f"Company: {self.company.title()}, Question: {question_text}, Link: {wd.current_url}")

    def greenhouse_question_answer_old(self, wd, question_dict):
        # school
        try:
            for q in wd.find_element(By.CSS_SELECTOR, '[class="education"]').find_elements(By.CSS_SELECTOR,
                                                                                           '[class="field"]'):
                try:
                    q.find_element(By.CSS_SELECTOR, '[class="asterisk"]')
                except:
                    continue
                question_text = q.find_element(By.CSS_SELECTOR, 'label').text.strip()
                if question_text.startswith("School"):
                    self.click_ele(wd, '[id="s2id_education_school_name_0"]')
                    self.expected_shown_element(wd, '[for="s2id_autogen1_search"]')
                    wd.find_element(By.CSS_SELECTOR, '[for="s2id_autogen1_search"]').find_element(By.XPATH,
                                                                                                  '..').find_element(
                        By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_school)
                    self.wait_x_sec(1)
                    self.click_ele(wd, '[id="selectedOption"]')
                elif question_text.startswith("Degree"):
                    self.click_ele(wd, '[id="s2id_education_degree_0"]')
                    self.expected_shown_element(wd, '[for="s2id_autogen2_search"]')
                    wd.find_element(By.CSS_SELECTOR, '[for="s2id_autogen2_search"]').find_element(By.XPATH,
                                                                                                  '..').find_element(
                        By.CSS_SELECTOR, 'input[type="text"]').send_keys(
                        self.user_degree if self.user_degree != 'PhD' else 'Doctor of Philosophy (Ph.D.)')
                    self.wait_x_sec(1)
                    self.click_ele(wd, '[id="selectedOption"]')
                elif question_text.startswith("Discipline"):
                    self.click_ele(wd, '[id="s2id_education_discipline_0"]')
                    self.expected_shown_element(wd, '[for="s2id_autogen3_search"]')
                    wd.find_element(By.CSS_SELECTOR, '[for="s2id_autogen3_search"]').find_element(By.XPATH,
                                                                                                  '..').find_element(
                        By.CSS_SELECTOR, 'input[type="text"]').send_keys('Computer Science')
                    self.wait_x_sec(1)
                    self.click_ele(wd, '[id="selectedOption"]')
                elif question_text.startswith("Start Date"):
                    try:
                        education_start_month = wd.find_element(By.CSS_SELECTOR,
                                                                '[name="job_application[educations][][start_date][month]"]')
                        education_start_month.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                        '[class="asterisk"]')
                        education_start_month.send_keys(self.user_school_start_time.split('/')[0])
                    except:
                        pass
                    education_start_year = wd.find_element(By.CSS_SELECTOR,
                                                           '[name="job_application[educations][][start_date][year]"]')
                    education_start_year.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                   '[class="asterisk"]')
                    education_start_year.send_keys(self.user_school_start_time.split('/')[-1])
                elif question_text.startswith("End Date"):
                    try:
                        education_end_month = wd.find_element(By.CSS_SELECTOR,
                                                              '[name="job_application[educations][][end_date][month]"]')
                        education_end_month.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                      '[class="asterisk"]')
                        education_end_month.send_keys(self.user_school_end_time.split('/')[0])
                    except:
                        pass
                    education_end_year = wd.find_element(By.CSS_SELECTOR,
                                                         '[name="job_application[educations][][end_date][year]"]')
                    education_end_year.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                 '[class="asterisk"]')
                    education_end_year.send_keys(self.user_school_end_time.split('/')[-1])
        except:
            pass
        # employee
        try:
            self.expected_shown_element(wd, '[id="employment_section"]')
            company_section = wd.find_element(By.CSS_SELECTOR, '[id="employment_section"]')
            company_section.find_element(By.CSS_SELECTOR, '[id="employment_company_name_0"]').send_keys(
                self.user_company)
            company_section.find_element(By.CSS_SELECTOR, '[id="employment_title_0"]').send_keys(self.user_title)
            company_section.find_element(By.CSS_SELECTOR, '[aria-label="Employment Start Month"]').send_keys(
                self.user_start_time.split("/")[0])
            company_section.find_element(By.CSS_SELECTOR, '[aria-label="Employment Start Year"]').send_keys(
                self.user_start_time.split("/")[1])
            if self.user_end_time == "Present":
                self.click_ele(wd, '[id="employment_current_0"]')
            else:
                company_section.find_element(By.CSS_SELECTOR, '[aria-label="Employment End Month"]').send_keys(
                    self.user_end_time.split("/")[0])
                company_section.find_element(By.CSS_SELECTOR, '[aria-label="Employment End Year"]').send_keys(
                    self.user_end_time.split("/")[1])
        except:
            pass
        question_dict = {**self.common_dict, **question_dict}
        for question in wd.find_element(By.CSS_SELECTOR, '[id="custom_fields"]').find_elements(By.CSS_SELECTOR,
                                                                                               '[class="field"]'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="asterisk"]')
            except:
                continue
            question_text = question.find_element(By.CSS_SELECTOR, 'label').text.strip()
            matched_key = next((key for key in question_dict if question_text.startswith(key)), None)
            if matched_key:
                answer = question_dict[matched_key]
            else:
                # If not found in question_dict, use Deepseek
                answer_options = []
                if question.find_elements(By.CSS_SELECTOR, '[class="select2-container"]'):
                    # Get select2 options
                    ans_click = question.find_element(By.CSS_SELECTOR, '[class="select2-container"]').get_attribute(
                        "id")
                    self.click_ele(wd, f'[id="{ans_click}"]')
                    try:
                        options_list = next(filter(lambda x: x.text, wd.find_elements(By.CSS_SELECTOR,
                                                                                      f'ul[id="select2List{self.greenhouse_list_idx}"]')))
                    except:
                        # dropdown not shown
                        self.click_ele(wd, f'[id="{ans_click}"]')
                    answer_options = [li.text.strip() for li in options_list.find_elements(By.CSS_SELECTOR, 'li')]
                    self.click_ele(wd, f'[id="{ans_click}"]')
                answer = self.deepseek.analyze_question(self.user_resume_content, question_text, answer_options or None)
                self.logger.info(f"LLM answer: {answer} Question text: {question_text}")
                self.wait_x_sec(1.0)
                wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")

            if answer:
                if question.find_elements(By.CSS_SELECTOR, '[class="select2-container"]'):
                    ans_click = question.find_element(By.CSS_SELECTOR, '[class="select2-container"]').get_attribute(
                        "id")
                    self.click_ele(wd, f'[id="{ans_click}"]')
                    try:
                        try:
                            options_list = next(filter(lambda x: x.text, wd.find_elements(By.CSS_SELECTOR,
                                                                                          f'ul[id="select2List{self.greenhouse_list_idx}"]')))
                        except:
                            options_list = [x.text for x in
                                            wd.find_elements(By.CSS_SELECTOR, f'ul[class="select2-results"]')[
                                                -1].find_elements(By.CSS_SELECTOR, 'li')]
                        if type(answer) is list:
                            for a in answer:
                                try:
                                    option = next(filter(lambda x:
                                                         x.text.strip().startswith(a),
                                                         options_list.find_elements(By.CSS_SELECTOR, 'li')))
                                    break
                                except:
                                    pass
                        else:
                            option = next(filter(lambda x: answer in x.text.strip(),
                                                 options_list.find_elements(By.CSS_SELECTOR, 'li')))
                        ans = option.find_element(By.CSS_SELECTOR, 'div').get_attribute("id")
                        self.click_ele(wd, f'[id="{ans}"]')
                    except:
                        self.greenhouse_list_idx += 1
                        potential_list_idx_try_cnt = 5
                        while potential_list_idx_try_cnt > 0:
                            try:
                                options_list = next(filter(lambda x: x.text, wd.find_elements(By.CSS_SELECTOR,
                                                                                              f'ul[id="select2List{self.greenhouse_list_idx}"]')))
                                if type(answer) is list:
                                    for a in answer:
                                        try:
                                            option = next(filter(lambda x: x.text.strip().startswith(a),
                                                                 options_list.find_elements(By.CSS_SELECTOR, 'li')))
                                            break
                                        except:
                                            pass
                                else:
                                    option = next(filter(lambda x: answer in x.text.strip(),
                                                         options_list.find_elements(By.CSS_SELECTOR, 'li')))
                                ans = option.find_element(By.CSS_SELECTOR, 'div').get_attribute("id")
                                self.click_ele(wd, f'[id="{ans}"]')
                                break
                            except:
                                self.greenhouse_list_idx += 1
                            potential_list_idx_try_cnt -= 1
                    self.greenhouse_list_idx += 1
                elif question.find_elements(By.CSS_SELECTOR, 'textarea'):
                    question.find_element(By.CSS_SELECTOR, 'textarea').send_keys(answer)
                elif question.find_elements(By.CSS_SELECTOR, 'input[type="text"]'):
                    if type(answer) is list:
                        for a in answer:
                            question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(a)
                            try:
                                self.expected_shown_element(wd, '[class="select2-results"]')
                                ans = wd.find_elements(By.CSS_SELECTOR, '[class="select2-results"]')[-1].find_element(
                                    By.CSS_SELECTOR, 'li')
                                ans.click()
                            except:
                                break
                            question.find_element(By.CSS_SELECTOR, 'input[type="text"]').clear()
                    else:
                        question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(answer)
                elif question.find_elements(By.CSS_SELECTOR, 'select'):
                    select_ele = question.find_element(By.CSS_SELECTOR, 'select')
                    if type(answer) is list:
                        for q_ans in answer:
                            try:
                                answer = next(filter(lambda x:
                                                     x.startswith(q_ans),
                                                     [y.text for y in Select(select_ele).options]))
                                break
                            except:
                                pass
                    Select(select_ele).select_by_visible_text(
                        next(filter(lambda x: answer in x.text, Select(select_ele).options)).text)
                elif question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]'):
                    for checkbox in question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]'):
                        ans_text = checkbox.find_element(By.XPATH, '..').text.strip()
                        if ans_text.startswith(answer):
                            a_id = checkbox.get_attribute("id")
                            self.click_ele(wd, f'[id="{a_id}"]')
            else:
                self.logger.error(f"Link: {wd.current_url()}, Question: {question_text}, Link: {wd.current_url}")
        # demographic
        d_question_dict = {
            "Gender": "Male" if self.user_gender_male else "Female",
            "How would you describe your racial": "East Asian",
            "How would you describe your sexual": "Heterosexual",
            "Do you identify as transgender": "No",
            "Do you have a disability": "No",
            "Are you a veteran": "No",
            "I identify my gender as": "Man" if self.user_gender_male else "Woman",
            "I identify as": "I don't wish to answer",
            "I identify my sexual orientation as": ["Straight", "I don't"],
            "Veteran Status": ["No", "I am not"],
            "I have a disability": "No",
            "I identify my race/ethnicity as": "I don't wish to answer",
            "What is your gender": ["Male" if self.user_gender_male else "Female",
                                    "Man" if self.user_gender_male else "Woman",
                                    "Cisgender man" if self.user_gender_male else "Cisgender woman"],
            "What is your sexual orientation": "Straight",
            "What race/ethnicity do you identify as": "Asian",
            "Do you identify": "No",
            "What is your race and/or ethnicity?": "Asian",
            "Military Veteran Status": "I am not a Protected Veteran",
            "Please indicate which best describes your race": "Asian",
            "Race": "Asian",
            "Protected Veteran Status": "I am not",
            "Disability": "No",
            "Are you a Canadian Armed Forces": "No",
            "Please note your experience administering 401k": "2",
            "How many participants did the 401k plan": "0",
            "Are you Hispanic": ["Decline", "No", "I don't"],
            "How would you describe your gender": ["I don't wish",
                                                   "Male" if self.user_gender_male else "Female",
                                                   "Man" if self.user_gender_male else "Woman"]
        }
        self.greenhouse_eeoc_questions(wd, d_question_dict)
        self.greenhouse_demographic_questions(wd, d_question_dict)

    def get_verification_from_gmail(self, verfiy_email_subject):
        mail = imaplib.IMAP4_SSL("imap.gmail.com")
        mail.login(self.user_account, self.user_email_code)
        mail.select("inbox")

        def fetch_verification_code():
            status, data = mail.search(None, f'(SUBJECT "{verfiy_email_subject}")')

            if status != 'OK' or not data[0]:
                return None

            latest_date = None
            latest_email = None

            email_ids = data[0].split()

            for email_id in email_ids:
                status, msg_data = mail.fetch(email_id, '(INTERNALDATE)')
                if status != "OK":
                    continue
                response_line = msg_data[0].decode()

                # Extract date using regex
                date_match = re.search(r'INTERNALDATE "([^"]+)"', response_line)
                if date_match:
                    date_str = date_match.group(1)
                    try:
                        email_date = datetime.datetime.strptime(date_str, '%d-%b-%Y %H:%M:%S %z')
                        if not latest_date or email_date > latest_date:
                            latest_date = email_date
                            latest_email = email_id
                    except Exception as e:
                        pass

            if latest_email is None:
                mail.logout()
                return None

            # 获取邮件内容
            status, msg_data = mail.fetch(latest_email, "(RFC822)")
            if status != 'OK':
                return None

            raw_email = msg_data[0][1]
            msg = email.message_from_bytes(raw_email)
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    if content_type == 'text/html':
                        charset = part.get_content_charset()
                        body = part.get_payload(decode=True).decode(charset or 'utf-8')
                        break
            else:
                body = msg.get_payload(decode=True).decode()

            def extract_verification_code(body):
                patterns = [
                    r'<h1>(.*?)</h1>',  # Greenhouse format
                    r'one-time pass code:\s*(\d+)',  # Oracle format
                    r'confirm your identity.*?<b[^>]*>(\d+)',  # Chase format
                    r'<h3><span[^>]*>([A-Z0-9]{5})</span></h3>',  # Waymo format
                    r'(https?://[\w\.-]+\.myworkdayjobs\.com/\w+/activate/[^\s<]+)'  # Workday format
                ]
                for pattern in patterns:
                    match = re.search(pattern, body)
                    if match:
                        return match.group(1)
                return None

            cur_verification_code = extract_verification_code(body)

            try:
                email_id_str = latest_email.decode() if isinstance(latest_email, bytes) else latest_email
                mail.store(email_id_str, '+X-GM-LABELS', '\\Trash')  # Gmail特有的方法
                mail.expunge()
            except Exception:
                pass

            return cur_verification_code

        self.wait_x_sec(5)
        verification_code = fetch_verification_code()

        if not verification_code:
            self.wait_x_sec(5)
            verification_code = fetch_verification_code()

        try:
            mail.logout()
        except Exception:
            pass

        return verification_code

    def get_resume_content(self, file_path: str):
        """Read text from PDF or DOCX resume files"""
        if file_path.endswith('.pdf'):
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = '\n'.join([page.extract_text() for page in reader.pages])
            return text
        elif file_path.endswith('.docx'):
            doc = Document(file_path)
            return '\n'.join([para.text for para in doc.paragraphs])
        else:
            raise ValueError("Unsupported file format. Only PDF and DOCX are supported.")
