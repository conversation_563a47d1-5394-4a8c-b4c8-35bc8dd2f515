import os
from datetime import datetime
from typing import Optional

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from dateutil.relativedelta import relativedelta

from databaseModule import databaseModule

# Set default plotting style
plt.style.use('ggplot')
sns.set_style("whitegrid")
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = [12, 8]
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12


class DatabaseExporter:
    """A class to export and visualize job-related data from the database."""

    def __init__(self):
        """Initialize DatabaseExporter with database connection."""
        self.db = databaseModule()
        self.output_dir = "../exports"
        os.makedirs(self.output_dir, exist_ok=True)
        self.last_month = (datetime.now() - relativedelta(months=1)).strftime(
            "%Y-%m") if datetime.now().day < 15 else datetime.now().strftime("%Y-%m")

    def _execute_query(self, columns: str, where: Optional[str] = None, table: Optional[str] = None,
                       join: Optional[str] = None, group_by: Optional[str] = None,
                       order_by: Optional[str] = None, limit: Optional[int] = None) -> pd.DataFrame:
        """
        Execute SQL query and return results as DataFrame.
        
        Args:
            columns: Columns to select
            where: Optional WHERE clause
            table: Optional table name
            join: Optional JOIN clause
            group_by: Optional GROUP BY clause
            order_by: Optional ORDER BY clause
            limit: Optional LIMIT value
            
        Returns:
            pandas DataFrame containing query results
        """
        # Build the complete SQL query
        sql_parts = []
        sql_parts.append(f"SELECT {columns}")
        sql_parts.append(f"FROM {table or self.db.table}")

        if join:
            sql_parts.append(join)
        if where:
            sql_parts.append(f"WHERE {where}")
        if group_by:
            sql_parts.append(f"GROUP BY {group_by}")
        if order_by:
            sql_parts.append(f"ORDER BY {order_by}")
        if limit:
            sql_parts.append(f"LIMIT {limit}")

        sql = " ".join(sql_parts)

        # Execute the query directly using cursor
        self.db.cursor.execute(sql)
        result = self.db.cursor.fetchall()

        if not result:
            return pd.DataFrame()

        # Get column names from cursor description
        column_names = [desc[0] for desc in self.db.cursor.description]
        return pd.DataFrame(result, columns=column_names)

    def _save_plot(self, filename: str, fig: Optional[plt.Figure] = None, **kwargs):
        """
        Save plot to file with standard parameters.
        
        Args:
            filename: Name of the output file
            fig: Optional matplotlib figure object
            **kwargs: Additional arguments for savefig
        """
        try:
            filepath = os.path.join(self.output_dir, filename)
            (fig or plt.gcf()).savefig(
                filepath,
                dpi=600,
                bbox_inches='tight',
                **kwargs
            )
        except Exception as e:
            raise
        finally:
            plt.close()

    def plot_top_companies(self):
        """Plot top 20 companies by job openings."""
        df = self._execute_query(
            columns="company, COUNT(*) as cnt",
            table="job_info",
            where=f"DATE_FORMAT(retrievetime, '%Y-%m') = '{self.last_month}'",
            group_by="company",
            order_by="cnt DESC",
            limit=20
        )

        if df.empty:
            return

        plt.figure(figsize=(16, 10))
        ax = sns.barplot(data=df, x='cnt', y='company', hue='company', legend=False, palette='viridis')

        # Add value labels on bars
        for i, v in enumerate(df['cnt']):
            ax.text(v, i, f' {v}', va='center')

        plt.title(f'Top 20 Companies by Job Openings ({self.last_month})', pad=20, fontsize=16)
        plt.xlabel('Number of Positions', labelpad=15)
        plt.ylabel('Company', labelpad=15)

        self._save_plot('top_companies.png')

    def plot_user_status(self):
        """Plot user application status distribution."""
        df = self._execute_query(
            columns="""
                CASE 
                    WHEN u.user_active = 1 THEN 'Active Job Search' 
                    ELSE 'Offer Received' 
                END as status, 
                COUNT(*) as cnt
            """,
            table="application_status",
            join="LEFT JOIN user_info u ON application_status.user_id = u.user_phone",
            group_by="status"
        )

        if df.empty:
            return

        plt.figure(figsize=(12, 8))
        colors = ['#FF9999', '#66B2FF']  # Soft red and blue

        # Create pie chart
        wedges, texts, autotexts = plt.pie(
            df['cnt'],
            labels=df['status'],
            autopct='%1.1f%%',
            startangle=90,
            colors=colors,
            explode=[0.05] * len(df),
            shadow=True,
            textprops={'fontsize': 14},
            wedgeprops={'edgecolor': 'white', 'linewidth': 2}
        )

        # Beautify percentage text
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(12)
            autotext.set_fontweight('bold')

        # Add legend
        plt.legend(
            wedges,
            df['status'],
            title="Application Status",
            loc="center left",
            bbox_to_anchor=(1, 0, 0.5, 1),
            fontsize=12
        )

        plt.title('Job Application Status Distribution', pad=20, fontsize=16, fontweight='bold')

        self._save_plot('user_status.png')

    def plot_monthly_trend(self):
        """Plot monthly job posting trend."""
        df = self._execute_query(
            columns="CONCAT(YEAR(retrievetime), '-', LPAD(MONTH(retrievetime), 2, '0')) as month, COUNT(*) as cnt",
            table="job_info",
            group_by="CONCAT(YEAR(retrievetime), '-', LPAD(MONTH(retrievetime), 2, '0'))",
            order_by="month"
        )

        if df.empty:
            return

        plt.figure(figsize=(16, 8))
        ax = sns.lineplot(
            x='month',
            y='cnt',
            data=df,
            marker='o',
            linewidth=2.5,
            markersize=10
        )

        # Add value labels
        for x, y in zip(df['month'], df['cnt']):
            ax.text(x, y, str(y), ha='center', va='bottom')

        plt.title('Monthly Job Posting Trend', pad=20, fontsize=16)
        plt.xlabel('Month', labelpad=15)
        plt.ylabel('Number of Postings', labelpad=15)
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        self._save_plot('monthly_trend.png')

    def plot_title_analysis(self):
        """Analyze and plot job title distributions."""
        categories = {
            'software_engineering': [
                'software', 'application', 'backend', 'cloud', 'development engineer',
                'python', 'java', 'system engineer', 'sre', 'SW', 'qa engineer',
                'solutions architect', 'platform engineer', 'back end', 'back-end',
                'azure', 'aws', 'gcp', 'infrastructure engineer', 'full stack',
                'frontend', 'front end', 'front-end', 'developer'
            ],
            'data_engineering': [
                'data engineer', 'etl', 'data pipeline', 'data infrastructure',
                'data platform', 'data architect', 'data warehouse'
            ],
            'machine_learning': [
                'machine learning', 'ml engineer', 'ai engineer', 'deep learning',
                'nlp', 'computer vision', 'mle', 'machine learning engineer',
                'artificial intelligence', 'ai/ml'
            ],
            'data_science': [
                'data scientist', 'research scientist', 'applied scientist',
                'quantitative', 'statistical', 'analytics engineer', 'decision science'
            ],
            'data_analysis': [
                'data analyst', 'business intelligence', 'bi developer',
                'analytics', 'reporting analyst', 'bi analyst', 'sql'
            ]
        }

        # Pretty names for display
        category_display_names = {
            'software_engineering': 'Software Engineering',
            'data_engineering': 'Data Engineering',
            'machine_learning': 'Machine Learning',
            'data_science': 'Data Science',
            'data_analysis': 'Data Analysis'
        }

        # Build dynamic query parts for categories
        category_cases = []
        for cat, keywords in categories.items():
            conditions = " OR ".join([f"LOWER(title) LIKE '%{kw}%'" for kw in keywords])
            category_cases.append(f"SUM(CASE WHEN ({conditions}) THEN 1 ELSE 0 END) as {cat}")

        # Trend analysis
        trend_df = self._execute_query(
            columns=f"CONCAT(YEAR(retrievetime), '-', LPAD(MONTH(retrievetime), 2, '0')) as month, {', '.join(category_cases)}",
            table="job_info",
            group_by="CONCAT(YEAR(retrievetime), '-', LPAD(MONTH(retrievetime), 2, '0'))",
            order_by="month"
        )

        if not trend_df.empty:
            plt.figure(figsize=(16, 8))
            colors = ['#FF9999', '#66B2FF', '#99FF99', '#FFCC99', '#FF99CC']  # Custom colors

            for i, cat in enumerate(categories.keys()):
                sns.lineplot(
                    x='month',
                    y=cat,
                    data=trend_df,
                    label=category_display_names[cat],
                    linewidth=2.5,
                    marker='o',
                    color=colors[i]
                )

            plt.title('Monthly Job Category Trends', pad=20, fontsize=16, fontweight='bold')
            plt.xlabel('Month', labelpad=15)
            plt.ylabel('Number of Positions', labelpad=15)
            plt.xticks(rotation=45)
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=12)
            plt.grid(True, alpha=0.3)

            self._save_plot('category_trend.png')

        # Current month distribution
        current_df = self._execute_query(
            columns=", ".join(category_cases),
            table="job_info",
            where=f"DATE_FORMAT(retrievetime, '%Y-%m') = '{self.last_month}'"
        )

        if not current_df.empty:
            plt.figure(figsize=(12, 8))

            # Rename columns for display
            current_df.columns = [category_display_names[col] for col in current_df.columns]

            # Calculate total for sorting
            total = current_df.iloc[0].sum()
            # Convert to percentages and sort
            data_sorted = (current_df.iloc[0] / total * 100).sort_values(ascending=True)

            # Create pie chart
            wedges, texts, autotexts = plt.pie(
                data_sorted,
                labels=data_sorted.index,
                autopct='%1.1f%%',
                startangle=90,
                colors=colors,
                explode=[0.05] * len(current_df.columns),
                shadow=True,
                textprops={'fontsize': 12},
                wedgeprops={'edgecolor': 'white', 'linewidth': 2}
            )

            # Beautify percentage text
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontsize(10)
                autotext.set_fontweight('bold')

            plt.title(f'Job Category Distribution ({self.last_month})', pad=20, fontsize=16, fontweight='bold')

            # Add legend
            plt.legend(
                wedges,
                data_sorted.index,
                title="Job Categories",
                loc="center left",
                bbox_to_anchor=(1, 0, 0.5, 1),
                fontsize=12
            )

            self._save_plot('current_category.png')

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.db.close_connection()


if __name__ == "__main__":
    with DatabaseExporter() as exporter:
        exporter.plot_top_companies()
        exporter.plot_user_status()
        exporter.plot_monthly_trend()
        exporter.plot_title_analysis()
