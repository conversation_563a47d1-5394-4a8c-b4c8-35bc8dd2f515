import smtplib
from collections import defaultdict
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

import pandas as pd

from module.databaseModule import databaseModule as DM


class EmailSender:
    def __init__(self):
        self.gmail_user = "<EMAIL>"
        self.gmail_password = "eztd cxja dkpg rtbu"
        self.smtp_server = 'smtp.gmail.com'
        self.smtp_port = 465
        self.db = DM()

    def send_email(self, subject, to_email, email_body, part=None):
        msg = MIMEMultipart()
        msg['From'] = self.gmail_user
        msg['To'] = to_email
        msg['Subject'] = subject
        msg.attach(MIMEText(email_body, 'html'))
        if part:
            msg.attach(part)
        with smtplib.SMTP_SSL('smtp.gmail.com', 465) as smtp_server:
            smtp_server.login(self.gmail_user, self.gmail_password)
            smtp_server.sendmail(self.gmail_user, to_email, msg.as_string())

    def send_subscription_expired_email(self, user_email):
        select_part = """
                DISTINCT
                    CONCAT(UPPER(LEFT(application_status.company, 1)), LOWER(SUBSTRING(application_status.company, 2))) AS company_name,
                    job_id,
                    job_info.link
            """
        join_part = """
                LEFT JOIN
                    job_info ON job_info.jobid = application_status.job_id
                JOIN
                    user_info ON user_info.user_phone = application_status.user_id
            """
        where_conditions = f"""
                user_info.user_account = '{user_email}'
                    AND user_info.user_active = 1
                    AND application_status.apply_time >= (
                        SELECT DATE_SUB(user_end_time, INTERVAL 1 MONTH) AS month_ago
                        FROM user_balance
                        WHERE user_phone = user_info.user_phone
                )
            """
        user_applied_df = pd.DataFrame(
            self.db.read(columns=select_part, join=join_part, table='application_status', where=where_conditions))
        csv_string = user_applied_df.to_csv(index=False, header=False)
        user_query = """
            user_balance.user_end_time,
            user_block.block_companies
        """

        where_conditions = f"""
            user_info.user_phone = user_balance.user_phone
            AND user_block.user_phone = user_balance.user_phone
            AND user_info.user_account = '{user_email}'
        """
        user_sub_end_time, user_block_companies = self.db.read(columns=user_query,
                                                               join="JOIN user_info ON user_info.user_phone = "
                                                                    "user_balance.user_phone JOIN user_block ON "
                                                                    "user_block.user_phone = user_balance.user_phone",
                                                               where=where_conditions, table="user_balance")[0]
        subject = "So Fast!"
        to_email = user_email
        email_body = f"""\
        <html>
        <body>
          <div>
            <p>Hello!</p>
            <p>This is to inform you that your time ends on: {user_sub_end_time.strftime('%Y-%m-%d')}.</p>
            <p>You have reached total number of <b>{len(user_applied_df)}</b> during your subscription.</p>
            <p>You have following companies needs to be fixed: {user_block_companies.replace("[", "").replace("]", "")}</p>
            <p>Best regards and hope not seeing you :)<br><br>The Pirate</p>
          </div>
        </body>
        </html>
        """
        part = MIMEApplication(csv_string, Name="applied_results.csv")
        part['Content-Disposition'] = 'attachment; filename="applied_results.csv"'
        self.send_email(subject, to_email, email_body, part)

    def send_daily_opening_email(self, user_account, data_df):
        subject = "Special Delivery!"
        categorized_jobs = defaultdict(list)
        for idx, job in data_df.iterrows():
            title = job['title'].lower()
            if 'software' in title or "application" in title or "reliability" in title or \
                    "development" in title or 'python' in title or 'sre' in title:
                categorized_jobs['Software Related'].append(job)
            elif 'analyst' in title or "business" in title or 'bi' in title:
                categorized_jobs['Analyst Related'].append(job)
            elif 'data' in title:
                categorized_jobs['Data Related'].append(job)
            elif 'research' in title:
                categorized_jobs['Research Related'].append(job)
            else:
                categorized_jobs['Others'].append(job)

        email_body = '<h2>Please find today\'s opening below:</h2>\n'
        for direction, job_details in categorized_jobs.items():
            sorted_jobs = sorted(job_details, key=lambda x: (
                ('Senior' not in x['title'], 'Sr' not in x['title'], 'Staff' not in x['title'], x['title']),
                x['company']),
                                 reverse=True)
            email_body += f"<h2>{direction.title()}: {len(job_details)}</h2>"
            email_body += "<table style='border-collapse: collapse;'>"
            table_header = "<tr style='border: 2px solid black;'><th style='border: 1px solid black;'>Company</th><th style='border: 1px solid black;'>Job Title</th><th style='border: 1px solid black;'>Apply Link</th></tr>"
            table_content = "".join([
                "<tr style='border: 1px solid black;'><td style='border: 1px solid black;'>{}</td><td style='border: 1px solid black;'>{}</td><td style='border: 1px solid black;'>{}</td></tr>".format(
                    job['company'], job['title'], job['link']) for job in sorted_jobs])
            email_body += f"{table_header}{table_content}</table>"

        self.send_email(subject, user_account, email_body)
