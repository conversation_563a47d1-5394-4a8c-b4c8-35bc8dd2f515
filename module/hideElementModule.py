# -*- coding: utf-8 -*-
# @Time  : 2023/2/18 下午10:20
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: hideElementModule.py
# @Software: PyCharm
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.remote.webdriver import WebDriver


class hideElementModule:

    def hideHeadAndFoot(self, wd: WebDriver) -> None:
        try:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, "header")))
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, "footer")))
            wd.execute_script("arguments[0].parentNode.removeChild(arguments[0]);",
                              wd.find_element(By.CSS_SELECTOR, "footer"))
            wd.execute_script("arguments[0].parentNode.removeChild(arguments[0]);",
                              wd.find_element(By.CSS_SELECTOR, "header"))
        except:
            pass

    def hideElement(self, wd: WebDriver, CSS_SELECTOR: str) -> None:
        try:
            WebDriverWait(wd, 10).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, CSS_SELECTOR)))
            wd.execute_script("arguments[0].parentNode.removeChild(arguments[0]);",
                              wd.find_element(By.CSS_SELECTOR, CSS_SELECTOR))
        except:
            pass