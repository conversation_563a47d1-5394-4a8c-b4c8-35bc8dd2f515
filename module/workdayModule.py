import datetime
import re
from abc import ABC

from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from undetected_chromedriver import Chrome

from module.applicationInterfaceModule import applicationInterface


class workdayModule(applicationInterface, ABC):
    def click_next_page(self, wd):
        try:
            self.click_ele(wd, '[data-automation-id="bottom-navigation-next-button"]')
        except:
            self.click_ele(wd, '[data-automation-id="pageFooterNextButton"]')

    def how_you_hear_us(self, wd: Chrome):
        try:
            self.hide.hideElement(wd, '[data-automation-id="header"]')
        except:
            pass
        # country
        try:
            self.expected_shown_element(wd, '[data-automation-id="countryDropdown"]')
            country_ele = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="countryDropdown"]')
        except:
            country_ele = wd.find_element(By.CSS_SELECTOR, '[id="country--country"]')
        if not country_ele.text.startswith("United States of America"):
            before_ul_box = wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')
            self.click_ele(wd, country_ele)
            self.wait_x_sec(2)
            after_ul_box = next(
                filter(lambda x: x not in before_ul_box, wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')))
            usa = next(filter(lambda x: x.text.startswith("United States of America"),
                              after_ul_box.find_elements(By.CSS_SELECTOR, 'li')))
            self.click_ele(wd, f'[id="{usa.get_attribute("id")}"]')
        # source input
        try:
            try:
                self.expected_shown_element(wd, '[data-automation-id="sourceSection"]')
            except:
                self.expected_shown_element(wd, '[id="source--source"]')
            if not wd.find_element(By.CSS_SELECTOR, '[placeholder="Search"]').find_element(By.XPATH,
                                                                                           '../..').text.startswith(
                "United States of America"):
                self.click_ele(wd, '[placeholder="Search"]')
            try:
                self.expected_shown_element(wd, '[data-automation-id="activeListContainer"]')
                try:
                    job_board_id = \
                        list(filter(lambda x: x.text.startswith('Job Board'),
                                    wd.find_element(By.CSS_SELECTOR,
                                                    '[data-automation-id="activeListContainer"]').find_elements(
                                        By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0].get_attribute("id")
                except:
                    job_board_id = \
                        list(filter(lambda x: x.text.startswith('External'),
                                    wd.find_element(By.CSS_SELECTOR,
                                                    '[data-automation-id="activeListContainer"]').find_elements(
                                        By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0].get_attribute("id")
                self.click_ele(wd, f'[id="{job_board_id}"]')
            except:
                job_board_id = \
                    wd.find_element(By.CSS_SELECTOR, '[data-automation-id="activeListContainer"]').find_elements(
                        By.CSS_SELECTOR, '[data-automation-id="menuItem"]')[0].get_attribute("id")
                self.click_ele(wd, f'[id="{job_board_id}"]')
            job_board = \
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="activeListContainer"]').find_elements(
                    By.CSS_SELECTOR, '[data-automation-id="menuItem"]')[0].get_attribute("id")
            self.click_ele(wd, f'[id="{job_board}"]')
            try:
                self.click_ele(wd, '[data-automation-id="legalNameSection_firstName"]')
                self.click_ele(wd, '[data-automation-id="legalNameSection_firstName"]')
            except:
                self.click_ele(wd, '[id="name--legalName--firstName"]')
                self.click_ele(wd, '[id="name--legalName--firstName"]')
        except:
            # drop down
            try:
                source_drop_down = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="sourceSection"]') \
                    .find_element(By.CSS_SELECTOR, '[data-automation-id="sourceDropdown"]')
            except:
                source_drop_down = wd.find_element(By.CSS_SELECTOR, '[id="source--source"]')
            if "select one" in source_drop_down.get_attribute("aria-label").lower():
                try:
                    self.click_ele(wd, '[data-automation-id="sourceDropdown"]')
                except:
                    self.click_ele(wd, '[id="source--source"]')
                ans_choices = \
                    list(filter(lambda x: not x.text.startswith("United"),
                                wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')))[0].find_elements(
                        By.CSS_SELECTOR, 'li')
                try:
                    ans = next(filter(lambda x: "LinkedIn" in x.text, ans_choices))
                except:
                    try:
                        ans = next(filter(lambda x: "Job Board" in x.text, ans_choices))
                    except:
                        ans = next(filter(lambda x: x.text.lower().strip() != "select one", ans_choices))
                self.click_ele(wd, f'[id="{ans.get_attribute("id")}"]')

    def my_info_page(self, wd: Chrome):
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'My Information')]")))
        # alert heading
        try:
            self.expected_shown_element(wd, '[data-automation-id="alertHeading"]')
            alert_text = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertHeading"]').text
            if "The application didn't autofill" in alert_text:
                wd.get(wd.current_url)
        except:
            pass
        try:
            self.how_you_hear_us(wd)
        except:
            pass
        try:
            self.click_ele(wd, '[id="2"]')
        except:
            try:
                self.click_ele(wd, '[name="candidateIsPreviousWorker"][value="false"]')
            except:
                pass
        d = {
            "legalNameSection_firstName": self.user_name.split()[0],
            "legalNameSection_lastName": self.user_name.split()[1],
            "addressSection_addressLine1": self.user_address,
            "addressSection_city": self.user_city,
            "addressSection_countryRegion": self.user_state,
            "addressSection_postalCode": self.user_zipcode,
            "phone-device-type": "Mobile",
            "email": self.user_account,
            "emailAddress--emailAddress": self.user_account,
            "phone-number": self.user_phone,
            "addressSection_regionSubdivision1": "N/A",
            "name--legalName--firstName": self.user_name.split()[0],
            "name--legalName--lastName": self.user_name.split()[1],
            "address--addressLine1": self.user_address,
            "address--countryRegion": self.user_state,
            "address--city": self.user_city,
            "address--postalCode": self.user_zipcode,
            "phoneNumber--phoneType": "Mobile",
            "phoneNumber--phoneNumber": self.user_phone,
        }
        for k, v in d.items():
            ele = f'[data-automation-id="{k}"]'
            try:
                try:
                    question = wd.find_element(By.CSS_SELECTOR, ele)
                except:
                    ele = f'[id="{k}"]'
                    question = wd.find_element(By.CSS_SELECTOR, ele)
                try:
                    question.find_element(By.XPATH, '../../..').find_element(By.CSS_SELECTOR, 'abbr')
                except:
                    try:
                        question.find_element(By.XPATH, '../../../..').find_element(By.CSS_SELECTOR, 'abbr')
                    except:
                        continue
            except:
                continue
            if question.get_attribute('type') == 'button':
                if question.text == v:
                    continue
                self.click_ele(wd, ele)
                try:
                    ans = list(filter(lambda x: x.text.startswith(v),
                                      list(filter(lambda x: x.text.lower().startswith("select"),
                                                  wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')))[0]
                                      .find_elements(By.CSS_SELECTOR, 'li')))[0]
                except:
                    try:
                        ans = \
                            list(filter(
                                lambda x: any(
                                    keyword in x.text for keyword in
                                    ["Home Cellular", "Personal", "Mobile", "Phone", "Personal"]),
                                list(filter(lambda x: x.text.lower().startswith("select"),
                                            wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')))[0]
                                .find_elements(By.CSS_SELECTOR, 'li')))[0]
                    except:
                        ans = list(filter(lambda x: x.text.lower().startswith("select"),
                                          wd.find_elements(By.CSS_SELECTOR, 'ul[role="listbox"]')))[0].find_elements(
                            By.CSS_SELECTOR, 'li')[-1]
                self.click_ele(wd, f'[id="{ans.get_attribute("""id""")}"]')
            elif question.get_attribute('type') == "text":
                value = question.get_attribute("value")
                if not value or value != v:
                    if question.get_attribute("data-automation-id") and question.get_attribute(
                            "data-automation-id") == 'phone-number':
                        if re.sub(r'\D', '', value) == v:
                            continue
                    if question.get_attribute("name") == "phoneNumber":
                        if re.sub(r'\D', '', value) == v:
                            continue
                    question.clear()
                    self.wait_x_sec(1)
                    wd.find_element(By.CSS_SELECTOR, ele).send_keys(d[k])
        self.click_next_page(wd)
        try:
            self.expected_shown_element(wd, '[data-automation-id="phone-terms-and-condition-link"]')
            self.click_next_page(wd)
        except:
            pass

    def question_select(self, wd: Chrome, question_dict: dict):
        try:
            self.expected_shown_element(wd, '[class="css-1j9bnzb"]')
            question_area = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        except:
            self.expected_shown_element(wd, '[class="css-1ylcaf3"]')
            question_area = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        for question in list(
                filter(lambda x: x.get_attribute('aria-label') and "select one required" in x.get_attribute(
                    'aria-label').lower().strip(), wd.find_elements(By.CSS_SELECTOR, 'button'))):
            q_id = question.get_attribute("id")
            value = question.get_attribute('aria-label')
            if value.lower().strip().startswith("select one"):
                value = wd.find_element(By.CSS_SELECTOR, f'''[data-fkit-id="{question.get_attribute('id')}"]''').text
            if any(value.startswith(key) for key in question_dict.keys()):
                self.click_ele(wd, f'[id="{q_id}"]')
                possible_ans = question_dict[list(filter(lambda y: value.startswith(y), question_dict.keys()))[0]]
                try:
                    if isinstance(possible_ans, list):
                        for ele in possible_ans:
                            try:
                                ans = \
                                    list(filter(lambda x: x.text.startswith(ele) or x.text.startswith(ele.upper()),
                                                wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')[0].find_elements(
                                                    By.CSS_SELECTOR, 'li')))[0]
                            except:
                                pass
                    else:
                        ans = \
                            list(filter(
                                lambda x: x.text.startswith(possible_ans) or x.text.startswith(possible_ans.upper()),
                                wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')[0].find_elements(
                                    By.CSS_SELECTOR, 'li')))[0]
                except:
                    answer_options = list(filter(lambda y: y.title() != 'Select One',
                                                 [x.text for x in wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')[
                                                     0].find_elements(By.CSS_SELECTOR, 'li')]))
                    llm_answer = self.deepseek.analyze_question(resume_content=self.user_resume_content, question=value,
                                                                answer_options=answer_options or None)
                    answer = list(
                        filter(lambda x: x.text.startswith(llm_answer) or x.text.startswith(llm_answer.upper()),
                               wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')[0].find_elements(
                                   By.CSS_SELECTOR, 'li')))[0].text
                    ans = \
                        list(
                            filter(lambda x: x.text.startswith(answer) or x.text.startswith(answer.upper()),
                                   wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')[0].find_elements(
                                       By.CSS_SELECTOR, 'li')))[0]
                    self.logger.info(
                        f"LLM Answer: {llm_answer}, Company: {self.company}, Question Area: {question_area}, Question: {value}, Answers: {answer_options}")
                self.click_ele(wd, f"""[id="{ans.get_attribute('id')}"]""")
            else:
                try:
                    q_id = question.get_attribute("id")
                    self.click_ele(wd, f'[id="{q_id}"]')
                    self.wait_x_sec(1)
                    ans = list(filter(lambda x: x.text != 'United States of America (+1)',
                                      wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')))[0].find_elements(
                        By.CSS_SELECTOR, 'li')
                    ans = list(filter(lambda x: x.text.lower() != 'select one', ans))
                    if "relocating" in value:
                        ans = list(filter(lambda x: x.text.startswith("Yes"), ans))
                        self.click_ele(wd, f"""[id="{ans[0].get_attribute('id')}"]""")
                    elif "years of experience" in value:
                        ans = list(filter(lambda x: x.text.startswith("3"), ans))
                        self.click_ele(wd, f"""[id="{ans[0].get_attribute('id')}"]""")
                    elif "require sponsorship" in value or "need sponsorship" in value or "require visa sponsorship" in value:
                        ans = list(filter(lambda x: x.text.startswith(self.user_visa_ans), ans))
                        self.click_ele(wd, f"""[id="{ans[0].get_attribute('id')}"]""")
                    else:
                        answer_options = [x.text for x in ans]
                        llm_answer = self.deepseek.analyze_question(resume_content=self.user_resume_content,
                                                                    question=value,
                                                                    answer_options=answer_options or None)
                        ans = list(
                            filter(lambda x: x.text.startswith(llm_answer) or x.text.startswith(llm_answer.upper()),
                                   ans))
                        self.click_ele(wd, f"""[id="{ans[0].get_attribute('id')}"]""")
                except:
                    ans_text = ",".join([x.text for x in ans])
                    self.logger.error(
                        f"Company: {self.company}, Question Area: {question_area}, Question: {value}, Answer: {ans_text}")
        # input checkbox ans
        for question in list(
                filter(lambda x: x.get_attribute('aria-label') and "true" in x.get_attribute('aria-required'),
                       wd.find_elements(By.CSS_SELECTOR, 'fieldset'))):
            value = question.get_attribute('aria-label')
            if any(value.startswith(key) for key in question_dict.keys()):
                possible_ans = question_dict[list(filter(lambda y: value.startswith(y), question_dict.keys()))[0]]
                check_box_id = \
                    list(filter(lambda x: x.text.startswith(possible_ans),
                                question.find_elements(By.CSS_SELECTOR, 'label')))[0].get_attribute("for")
                if wd.find_element(By.CSS_SELECTOR, f'[id="{check_box_id}"]').get_attribute("aria-checked") == 'false':
                    self.click_ele(wd, f'[id="{check_box_id}"]')
            else:
                answer = ", ".join([x.text for x in question.find_elements(By.CSS_SELECTOR, 'label')])
                self.logger.error(
                    f"Company: {self.company}, Question Area: {question_area}, Question: {value}, Answer: {answer}")
        # input text ans
        for question in list(
                filter(lambda x: x.get_attribute('aria-required') and "true" in x.get_attribute('aria-required'),
                       wd.find_elements(By.CSS_SELECTOR, 'input[type="text"]'))):
            p_id = question.get_attribute('id')
            try:
                value = wd.find_element(By.CSS_SELECTOR, f'label[for="{p_id}"]').find_element(By.XPATH, '..').text
            except:
                value = wd.find_element(By.CSS_SELECTOR, f'[data-fkit-id="{p_id}"]').text
            if any(value.startswith(key) for key in question_dict.keys()):
                possible_ans = question_dict[list(filter(lambda y: value.startswith(y), question_dict.keys()))[0]]
                if not question.get_attribute('value'):
                    question.send_keys(possible_ans)
            else:
                self.logger.error(
                    f"Company: {self.company}, Question Area: {question_area}, Question: {value}")
        # textarea ans
        for question in wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="formField-"]') + wd.find_elements(
                By.CSS_SELECTOR, 'fieldset'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="requiredAsterisk"]')
            except:
                continue
            value = question.find_element(By.CSS_SELECTOR, '[data-automation-id="richText"]').text
            try:
                ans = question.find_element(By.CSS_SELECTOR, 'textarea')
            except:
                continue
            if ans.text: continue
            if any(value.startswith(key) for key in question_dict.keys()):
                possible_ans = question_dict[list(filter(lambda y: value.startswith(y), question_dict.keys()))[0]]
                ans.send_keys(possible_ans)
                self.click_next_page(wd)
            else:
                llm_answer = self.deepseek.analyze_question(resume_content=self.user_resume_content, question=value,
                                                            answer_options=None)
                ans.send_keys(llm_answer)
                self.logger.info(
                    f"LLM Answer: {llm_answer}, Company: {self.company}, Question Area: {question_area}, Question: {value}")
        # input checkbox
        for question in wd.find_elements(By.CSS_SELECTOR, 'fieldset'):
            try:
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="requiredAsterisk"]')
                except:
                    question.find_element(By.CSS_SELECTOR, 'abbr')
            except:
                continue
            try:
                question.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            except:
                continue
            try:
                value = question.find_element(By.CSS_SELECTOR, '[data-automation-id="richText"]').text
            except:
                value = question.find_element(By.CSS_SELECTOR, 'legend').text
            if any(value.startswith(key) for key in question_dict.keys()):
                possible_ans = question_dict[list(filter(lambda y: value.startswith(y), question_dict.keys()))[0]]
                input_checkbox_id = next(
                    filter(lambda x: x.find_element(By.XPATH, '../..').text.startswith(possible_ans),
                           question.find_elements(By.CSS_SELECTOR,
                                                  'input[type="checkbox"]'))).get_attribute('id')
                if wd.find_element(By.CSS_SELECTOR, f'[id="{input_checkbox_id}"]').get_attribute(
                        "aria-checked") != 'true':
                    self.click_ele(wd, f'[id="{input_checkbox_id}"]')
            else:
                answers = list(x.find_element(By.XPATH, '../..').text for x in question.find_elements(By.CSS_SELECTOR,
                                                                                                      'input[type="checkbox"]'))
                llm_answer = self.deepseek.analyze_question(resume_content=self.user_resume_content, question=value,
                                                            answer_options=answers or None)
                input_checkbox_id = next(
                    filter(lambda x: llm_answer in x.find_element(By.XPATH, '../..').text,
                           question.find_elements(By.CSS_SELECTOR,
                                                  'input[type="checkbox"]'))).get_attribute('id')
                if wd.find_element(By.CSS_SELECTOR, f'[id="{input_checkbox_id}"]').get_attribute(
                        "aria-checked") != 'true':
                    self.click_ele(wd, f'[id="{input_checkbox_id}"]')
        # hispanic
        for question in wd.find_elements(By.CSS_SELECTOR, '[id="personalInfoUS--hispanicOrLatino"]'):
            self.click_ele(wd, '[id="personalInfoUS--hispanicOrLatino"]')
            a_id = next(filter(lambda x: x.text.startswith("No"), wd.find_element(By.CSS_SELECTOR,
                                                                                  f'[id="{question.get_attribute("aria-controls")}"]').find_elements(
                By.CSS_SELECTOR, 'li'))).get_attribute("id")
            self.click_ele(wd, f'[id="{a_id}"')

    def my_exp_page(self, wd: Chrome):
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'My Experience')]")))
        try:
            self.expected_shown_element(wd, '[data-automation-id="delete-file"]')
            for _ in wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="delete-file"]'):
                self.click_ele(wd, '[data-automation-id="delete-file"]')
        except:
            pass
        self.expected_shown_element(wd, '[data-automation-id="file-upload-input-ref"]')
        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="file-upload-input-ref"]').send_keys(
            self.user_resume_path)
        # Working Experience
        try:
            try:
                work_experience_questions = wd.find_element(By.CSS_SELECTOR,
                                                            '[data-automation-id="myExperiencePage"]').find_element(
                    By.CSS_SELECTOR, 'fieldset').find_elements(By.CSS_SELECTOR, '[data-automation-id]')
            except:
                try:
                    work_experience_questions = wd.find_element(By.CSS_SELECTOR,
                                                                '[data-automation-id="workExperienceSection"]').find_elements(
                        By.XPATH,
                        '//*[starts-with(@data-automation-id, "formField-")]')
                except:
                    work_experience_questions = wd.find_element(By.CSS_SELECTOR,
                                                                '[aria-labelledby="Work-Experience-1-panel"]').find_elements(
                        By.XPATH,
                        '//*[starts-with(@data-automation-id, "formField-")]')
            for question in list(filter(lambda x: x.text, work_experience_questions)):
                try:
                    try:
                        question.find_element(By.CSS_SELECTOR, '[title="required"]')
                    except:
                        question.find_element(By.CSS_SELECTOR, 'abbr')
                except:
                    continue
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                if question_text.startswith("Job Title"):
                    ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    if not ans.get_attribute('value'):
                        ans.send_keys(self.user_title)
                elif question_text.startswith("Company"):
                    ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    if not ans.get_attribute('value'):
                        ans.send_keys(self.user_company)
                elif question_text.startswith("From"):
                    from_month = question.find_element(By.CSS_SELECTOR, '[data-automation-id="dateSectionMonth-input"]')
                    if not from_month.get_attribute('value'):
                        from_month.send_keys(self.user_start_time.split("/")[0])
                    from_year = question.find_element(By.CSS_SELECTOR,
                                                      '[data-automation-id="dateSectionYear-input"]')
                    if not from_year.get_attribute('value'):
                        from_year.send_keys(self.user_start_time.split("/")[1])
                elif question_text.startswith("To"):
                    if self.user_end_time == 'Present':
                        try:
                            self.click_ele(wd, '[data-automation-id="currentlyWorkHere"]')
                        except:
                            self.click_ele(wd, '[name="currentlyWorkHere"]')
                    else:
                        to_month = question.find_element(By.CSS_SELECTOR,
                                                         '[data-automation-id="dateSectionMonth-input"]')
                        if not to_month.get_attribute('value'):
                            to_month.send_keys(self.user_end_time.split("/")[0])
                        to_year = question.find_element(By.CSS_SELECTOR,
                                                        '[data-automation-id="dateSectionYear-input"]')
                        if not to_year.get_attribute('value'):
                            to_year.send_keys(self.user_end_time.split("/")[1])
                    for calender in wd.find_elements(By.CSS_SELECTOR, '[aria-label="Calendar"]')[1:]:
                        self.click_ele(wd, calender)
                        self.expected_shown_element(wd, '[data-automation-id="monthPickerGrid"]')
                        months = wd.find_element(By.CSS_SELECTOR,
                                                 '[data-automation-id="monthPickerGrid"]').find_elements(
                            By.CSS_SELECTOR, '[data-automation-id="monthPickerTileLabel"]')
                        selected_month = next(filter(
                            lambda x: x.get_attribute('aria-label') and x.get_attribute('aria-label').startswith(
                                "Selected"), months))
                        self.click_ele(wd, selected_month)
                elif question_text.startswith("Role Description"):
                    ans = question.find_element(By.CSS_SELECTOR, 'textarea')
                    if not ans.text:
                        ans.send_keys(self.user_role_desc)
                elif question_text.startswith("Location"):
                    ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    if not ans.get_attribute('value'):
                        ans.send_keys(self.user_state)
        except:
            pass
        # School Experience
        degree_mapping = {
            "Master": "M.S",
            "Bachelor": "B.S",
            "PhD": "Doctorate"
        }
        try:
            education_section = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="educationSection"]')
            try:
                school = education_section.find_element(By.CSS_SELECTOR, '[data-automation-id="school"]')
                if not school.get_attribute('value'):
                    school.send_keys(self.user_school)
            except:
                school = education_section.find_element(By.CSS_SELECTOR, 'input[placeholder="Search"]')
                school.click()
                self.expected_shown_element(wd, '[data-automation-id="activeListContainer"]')
                school.send_keys(self.user_school)
                school.send_keys(Keys.ENTER)
                self.wait_x_sec(1)
                try:
                    school_option = list(
                        filter(lambda x: x.text == self.user_school,
                               wd.find_element(By.CSS_SELECTOR,
                                               '[data-automation-id="activeListContainer"]').find_elements(
                                   By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0]
                    self.click_ele(wd, f'[id={school_option.get_attribute("id")}]')
                except:
                    try:
                        school_option = list(
                            filter(lambda x: x.text,
                                   wd.find_element(By.CSS_SELECTOR,
                                                   '[data-automation-id="activeListContainer"]').find_elements(
                                       By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0]
                        self.click_ele(wd, f'[id={school_option.get_attribute("id")}]')
                    except:
                        pass
            degree = education_section.find_element(By.CSS_SELECTOR, '[data-automation-id="degree"]')
            if not degree.text or degree.text == "select one":
                degree.click()
                if len(wd.find_elements(By.CSS_SELECTOR, '[role="listbox"]')) <= 1:
                    degree.click()
                degree_pop_up_id = wd.find_element(By.CSS_SELECTOR,
                                                   '[aria-label="Degree select one required"]').get_attribute(
                    "aria-controls")
                self.wait_x_sec(3)
                try:
                    ans_id = \
                        list(filter(lambda x: self.user_degree in x.text or degree_mapping[self.user_degree] in x.text,
                                    wd.find_element(By.CSS_SELECTOR, f'[id="{degree_pop_up_id}"]').find_elements(
                                        By.CSS_SELECTOR, 'li')))[0].get_attribute("id")
                except:
                    try:
                        degree_mapping_addition = {
                            "Master": "M.S",
                            "Bachelor": "B.S",
                            "PhD": "Doctoral"
                        }
                        ans_id = list(filter(lambda x: degree_mapping_addition[self.user_degree] in x.text,
                                             wd.find_element(By.CSS_SELECTOR,
                                                             f'[id="{degree_pop_up_id}"]').find_elements(
                                                 By.CSS_SELECTOR, 'li')))[0].get_attribute("id")
                    except:
                        degree_mapping_addition = {
                            "Bachelor": "Bachelors",
                            "Master": "Masters",
                            "PhD": "Doctoral"
                        }
                        ans_id = list(filter(lambda x: degree_mapping_addition[self.user_degree] in x.text,
                                             wd.find_element(By.CSS_SELECTOR,
                                                             f'[id="{degree_pop_up_id}"]').find_elements(
                                                 By.CSS_SELECTOR, 'li')))[0].get_attribute("id")
                self.click_ele(wd, f'[id="{ans_id}"]')
            # field of study
            try:
                filed_of_study = education_section.find_element(By.CSS_SELECTOR,
                                                                '[data-automation-id="formField-field-of-study"]')
                try:
                    filed_of_study.find_element(By.CSS_SELECTOR, '[title="required"]')
                except:
                    filed_of_study.find_element(By.CSS_SELECTOR, 'abbr')
                filed_of_study = filed_of_study.find_element(
                    By.CSS_SELECTOR, '[data-automation-id="multiSelectContainer"]')
                if not filed_of_study.text:
                    filed_of_study.click()
                    filed_of_study.find_element(By.CSS_SELECTOR, 'input').send_keys("Computer")
                    self.wait_x_sec(2)
                    filed_of_study.find_element(By.CSS_SELECTOR, 'input').send_keys(Keys.ENTER)
                    self.wait_x_sec(2)
                    self.expected_shown_element(wd, '[data-automation-id="activeListContainer"]')
                    study_option = list(
                        filter(lambda x: x.text.startswith("Computer Science") or x.text.startswith(
                            "Computer Information") or x.text.startswith("Computer"),
                               wd.find_element(By.CSS_SELECTOR,
                                               '[data-automation-id="activeListContainer"]').find_elements(
                                   By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0]
                    self.click_ele(wd, f'[id={study_option.get_attribute("id")}]')
            except:
                pass
            # GPA
            try:
                gpa = education_section.find_element(By.CSS_SELECTOR, '[data-automation-id="gpa"]')
                gpa.find_element(By.XPATH, '../../..').find_element(By.CSS_SELECTOR, 'label').find_element(
                    By.CSS_SELECTOR, 'abbr')
                if not gpa.get_attribute("value"):
                    gpa.send_keys(4)
            except:
                pass
        except:
            pass
        # new education section
        try:
            self.expected_shown_element(wd, '[aria-labelledby="Education-section"]')
            education_section = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Education-section"]')
            for question in education_section.find_elements(By.XPATH,
                                                            './/div[starts-with(@data-automation-id, "formField-")]'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[title="required"]')
                except:
                    try:
                        question.find_element(By.CSS_SELECTOR, 'abbr')
                    except:
                        continue
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                if question_text.startswith("School"):
                    ans = question.find_element(By.CSS_SELECTOR, 'input')
                    if not ans.get_attribute('value'):
                        ans.send_keys(self.user_school)
                    try:
                        ans.send_keys(Keys.ENTER)
                        self.expected_shown_element(wd, '[data-uxi-widget-type="multiselectlistitem"]')
                        user_school_option = next(filter(lambda x: self.user_school in x.text,
                                                         wd.find_elements(By.CSS_SELECTOR,
                                                                          '[data-automation-id="promptOption"]')))
                        self.click_ele(wd, f'[id="{user_school_option.get_attribute("id")}"]')
                    except:
                        pass
                elif question_text.startswith("Degree"):
                    ans = question.find_element(By.CSS_SELECTOR, '[name="degree"]')
                    if not ans.text or ans.text == "select one" or ans.text == "Select One":
                        ans.click()
                        list_box_value = ans.get_attribute('aria-controls')
                        self.expected_shown_element(wd, f'[id="{list_box_value}"]')
                        degree_pop_up_id = list_box_value
                        try:
                            degree_option = list(filter(
                                lambda x: self.user_degree in x.text or degree_mapping.get(self.user_degree,
                                                                                           "") in x.text,
                                wd.find_element(By.CSS_SELECTOR,
                                                f'ul[id="{degree_pop_up_id}"]').find_elements(
                                    By.CSS_SELECTOR, 'li')))[0]
                            self.click_ele(wd, f'[id="{degree_option.get_attribute("id")}"]')
                        except IndexError:
                            try:
                                first_option = wd.find_element(By.CSS_SELECTOR,
                                                               '[data-automation-id="activeListContainer"]').find_element(
                                    By.CSS_SELECTOR, 'li')
                                self.click_ele(wd, f'[id="{first_option.get_attribute("id")}"]')
                            except:
                                pass
                elif question_text.startswith("Field of Study"):
                    try:
                        filed_of_study = education_section.find_element(By.CSS_SELECTOR,
                                                                        '[data-automation-id="formField-fieldOfStudy"]')
                        filed_of_study = filed_of_study.find_element(
                            By.CSS_SELECTOR, '[data-automation-id="multiSelectContainer"]')
                        input_search_id = filed_of_study.find_element(By.CSS_SELECTOR, 'input').get_attribute("id")
                        if not filed_of_study.text:
                            self.click_ele(wd, f'[id="{input_search_id}"]')
                            filed_of_study.find_element(By.CSS_SELECTOR, 'input').send_keys("Computer")
                            filed_of_study.find_element(By.CSS_SELECTOR, 'input').send_keys(Keys.ENTER)
                            self.wait_x_sec(2)
                            self.expected_shown_element(wd, '[data-automation-id="activeListContainer"]')
                            study_option = list(
                                filter(lambda x: x.text.startswith("Computer Science") or x.text.startswith(
                                    "Computer Information") or x.text.startswith(
                                    "Computer and Information Science") or x.text.startswith("Computer"),
                                       wd.find_element(By.CSS_SELECTOR,
                                                       '[data-automation-id="activeListContainer"]').find_elements(
                                           By.CSS_SELECTOR, '[data-automation-id="menuItem"]')))[0]
                            self.click_ele(wd, f'[id={study_option.get_attribute("id")}]')
                    except:
                        pass
                elif question_text.startswith("Overall Result") or "GPA" in question_text:
                    try:
                        ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                        if not ans.get_attribute('value'):
                            llm_answer = self.deepseek.analyze_question(resume_content=self.user_resume_content,
                                                                        question=question_text)
                            ans.send_keys(llm_answer)
                    except:
                        pass
        except:
            pass
        try:
            # Languages
            language_dict = {
                "I am willing and able to use this skill set in this language to serve our customers.": "Yes",
                "Language": "Chinese",
                "Overall": "Fluent",
                "Reading": "Fluent",
                "Speaking": "Fluent",
                "Writing": "Fluent",
                "Read": "Fluent",
                "Speak": "Fluent",
                "Write": "Fluent",
                "Comprehension": "Fluent",
            }
            for k, v in language_dict.items():
                try:
                    ele = f"{k} select one required"
                    try:
                        self.click_ele(wd, f'[aria-label={ele}"]')
                    except:
                        try:
                            ele = f"{k} Select One Required"
                            self.click_ele(wd, f'[aria-label="{ele}"]')
                        except:
                            pass
                except:
                    continue
                q_id = wd.find_element(By.CSS_SELECTOR,
                                       f'[aria-label="{ele}"]').get_attribute("aria-controls")
                self.expected_shown_element(wd, f'[id="{q_id}"]')
                ans_id = list(filter(lambda x: v in x.text or "Mandarin" in x.text,
                                     wd.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]').find_elements(
                                         By.CSS_SELECTOR, 'li')))[0].get_attribute("id")
                self.click_ele(wd, f'[id="{ans_id}"]')
        except:
            pass
        try:
            try:
                skills = education_section.find_element(By.CSS_SELECTOR,
                                                        '[data-automation-id="formField-skillsPrompt"]')
            except:
                skills = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="formField-skills"]')
            try:
                skills.find_element(By.CSS_SELECTOR, '[title="required"]')
            except:
                skills.find_element(By.CSS_SELECTOR, 'abbr')
            skill_input = skills.find_element(By.CSS_SELECTOR, 'input[placeholder="Search"]')
            skill_input.click()
            self.wait_x_sec(1)
            skill_input.send_keys("Chinese")
            skill_input.send_keys(Keys.ENTER)
            try:
                wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="promptOption"]')[0].click()
            except:
                pass
            self.click_next_page(wd)
        except:
            pass
        try:
            for expect_year in education_section.find_elements(By.CSS_SELECTOR,
                                                               '[data-automation-id="dateSectionYear-input"]'):
                try:
                    expect_year.find_element(By.XPATH, '../../../../..').find_element(By.CSS_SELECTOR,
                                                                                      'label').find_element(
                        By.CSS_SELECTOR, 'abbr')
                except:
                    continue
                expect_year.send_keys(datetime.date.today().year - 1)
        except:
            pass
        self.click_next_page(wd)
        try:
            WebDriverWait(wd, 3).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[data-automation-id="errorBanner"]')))
            self.logger.error(f"{wd.current_url.replace('/apply', '')}, {self.user}")
        except:
            pass

    def apply_success(self, wd: Chrome):
        try:
            WebDriverWait(wd, 5).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[data-automation-id="congratulationsPopup"]')))
        except:
            wd.refresh()
        try:
            WebDriverWait(wd, 15).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[data-automation-id="congratulationsPopup"]')))
            return 1
        except:
            try:
                if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="signInContent"]').text.startswith(
                        "Congratulations!"):
                    return 1
            except:
                if wd.find_element(By.CSS_SELECTOR, '[class="css-1k8t6c1"]').text.startswith("Application Submitted"):
                    return 1
            return 0

    def self_identify(self, wd: Chrome):
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Self Identify')]")))
            try:
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="name"]').send_keys(self.user_name)
            except:
                wd.find_element(By.CSS_SELECTOR, '[id="selfIdentifiedDisabilityData--name"]').send_keys(self.user_name)
            self.click_calender(wd)
            label = next(filter(lambda x: x.text == "No, I do not have a disability and have not had one in the past",
                                wd.find_elements(By.CSS_SELECTOR, 'label')))
            p_id = label.get_attribute('for')
            self.click_ele(wd, f'[id="{p_id}"]')
            self.click_next_page(wd)
        except:
            pass

    def check_condition(self, wd: Chrome, url: str):
        try:
            self.expected_shown_element(wd, '[data-automation-id="jobPostingHeader"]')
            new_year = str(datetime.datetime.now().year + 1)
            header_element = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="jobPostingHeader"]').text
            description_element_text = wd.find_element(By.CSS_SELECTOR,
                                                       '[data-automation-id="jobPostingDescription"]').text
            keywords = ["GRADS", "College Grad", "Currently pursuing", "University Recruiting", "grads",
                        "Salesforce interns", "Grade Point", "Early Talent", "Graduation Date",
                        "summer internship"]
            if (((any(keyword in description_element_text for keyword in keywords) or any(
                    keyword in header_element for keyword in
                    keywords)) or any(keyword in description_element_text for keyword in keywords))
                    or (new_year in header_element)):
                if len(self.database.read("link", f"link='{url}'", 'job_info_ng')) == 0:
                    self.database.create({"company": f"{self.company.lower()}", "link": url}, 'job_info_ng')
                    self.logger.warning(f"Found New Grad position: {url}")
                self.database.delete(f"link='{url}'")
                return -4
            elif "Grads" in wd.current_url or "Graduate" in wd.current_url:
                if len(self.database.read("link", f"link='{url}'", 'job_info_ng')) == 0:
                    self.database.create({"company": f"{self.company.lower()}", "link": url}, 'job_info_ng')
                    self.logger.warning(f"Found New Grad position: {url}")
                self.database.delete(f"link='{url}'")
                return -4
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="page-title"]').text == 'Workday is currently unavailable.':
                return -3
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR,
                               '[data-automation-id="errorMessage"]').text == "The page you are looking for doesn't exist.":
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        try:
            # job updated
            try:
                if wd.find_element(By.CSS_SELECTOR, '[class="css-ipzqjw"]').text.startswith(
                        "This job has been updated"):
                    return 1
            except:
                pass
            try:
                WebDriverWait(wd, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-automation-id="adventureButton"]')))
                wd.get(wd.find_element(By.CSS_SELECTOR, '[data-automation-id="adventureButton"]').get_attribute('href'))
            except:
                try:
                    wd.get(wd.find_element(By.CSS_SELECTOR, '[data-automation-id="continueButton"]').get_attribute(
                        'href'))
                except:
                    wd.find_element(By.CSS_SELECTOR, '[data-automation-id="viewButton"]')
                    return 1
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[data-automation-id="email"]')))
            self.hide.hideElement(wd, '[data-automation-id="header"]')
            wd.find_element(By.CSS_SELECTOR, '[data-automation-id="email"]').send_keys(self.user_account)
            wd.find_element(By.CSS_SELECTOR, '[data-automation-id="password"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[aria-label="Sign In"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')))
            if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="errorMessage"]'):
                self.click_ele(wd, '[data-automation-id="createAccountLink"]')
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="email"]').send_keys(self.user_account)
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="password"]').send_keys(self.user_pwd)
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="verifyPassword"]').send_keys(self.user_pwd)
                try:
                    self.click_ele(wd, '[data-automation-id="createAccountCheckbox"]')
                except:
                    pass
                self.click_ele(wd, '[aria-label="Create Account"]')
                try:
                    self.expected_shown_element(wd, '[data-automation-id="inputError"]')
                    return -1
                except:
                    pass
                try:
                    WebDriverWait(wd, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')))
                    error = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')
                    if error.text.startswith("Sign in to this account"):
                        return -1
                except:
                    wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertMessage"]')
                    return -2
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="css-1beb3x3"]').text.startswith(
                    "The page you are looking for doesn't exist"):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        try:
            self.click_ele(wd, '[data-automation-id="useMyLastApplication"]')
        except:
            pass
        try:
            self.expected_shown_element(wd, '[id="authViewTitle"]')
            if wd.find_element(By.CSS_SELECTOR, '[id="authViewTitle"]').text.startswith("Create Account"):
                # sign-in first
                self.click_ele(wd, '[data-automation-id="signInLink"]')
                self.expected_shown_element(wd, '[data-automation-id="email"]')
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="email"]').send_keys(self.user_account)
                wd.find_element(By.CSS_SELECTOR, '[data-automation-id="password"]').send_keys(self.user_pwd)
                self.click_ele(wd, '[aria-label="Sign In"]')
                try:
                    wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertMessage"]')
                except:
                    WebDriverWait(wd, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')))
                    error_msg = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')
                    if error_msg.text.startswith("You may have entered the wrong email address or password"):
                        self.click_ele(wd, '[data-automation-id="createAccountLink"]')
                        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="email"]').send_keys(self.user_account)
                        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="password"]').send_keys(self.user_pwd)
                        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="verifyPassword"]').send_keys(
                            self.user_pwd)
                        try:
                            self.click_ele(wd, '[data-automation-id="createAccountCheckbox"]')
                        except:
                            pass
                        self.click_ele(wd, '[aria-label="Create Account"]')
                try:
                    try:
                        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertMessage"]')
                        return -2
                    except:
                        wd.find_element(By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')
                        return -2
                except:
                    wd.find_element(By.CSS_SELECTOR, '[data-automation-id="inputError"]')
                    return -1
        except:
            pass
        # account activate verification
        try:
            try:
                self.expected_shown_element(wd, '[data-automation-id="alertMessage"]')
                ele = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertMessage"]')
                activate_text = ele.text
            except:
                ele = wd.find_element(By.CSS_SELECTOR, '[data-automation-id="errorMessage"]')
                activate_text = ele.text
            if "verify your account" in activate_text.lower():
                try:
                    self.expected_shown_element(wd, '[data-automation-id="informationalBlurbButton"]')
                    if wd.find_element(By.CSS_SELECTOR,
                                       '[data-automation-id="informationalBlurbButton"]').text.startswith(
                        "Resend Account Verification"):
                        self.click_ele(wd, '[data-automation-id="informationalBlurbButton"]')
                except:
                    pass
                verification_link = self.get_verification_from_gmail("Verify your candidate account")
                if verification_link:
                    wd.get(verification_link)
                    try:
                        self.expected_shown_element(wd, '[data-automation-id="email"]')
                        self.expected_shown_element(wd, '[data-automation-id="alertMessage"]')
                        if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alertMessage"]').text.startswith(
                                "Account Activated"):
                            wd.find_element(By.CSS_SELECTOR, '[data-automation-id="email"]').send_keys(
                                self.user_account)
                            wd.find_element(By.CSS_SELECTOR, '[data-automation-id="password"]').send_keys(self.user_pwd)
                            self.click_ele(wd, '[aria-label="Sign In"]')
                        else:
                            return -2
                    except Exception as e:
                        self.logger.error(f"Process verification link error: {url}, {self.user}, {e.with_traceback()}")
                        return -2
        except:
            pass
        # something went wrong
        try:
            self.expected_shown_element(wd, '[class="css-qaj8w6"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="css-qaj8w6"]').text.startswith("Something"):
                wd.refresh()
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alreadyApplied"]'):
                return 1
        except:
            try:
                if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="alreadyAppliedPage"]'):
                    return 1
            except:
                pass
        return 0

    def click_calender(self, wd):
        try:
            self.click_ele(wd, '[aria-label="Calendar"]')
            self.click_ele(wd, '[data-automation-id="datePickerSelectedToday"]')
        except:
            pass

    def submit(self, wd):
        self.self_identify(wd)
        WebDriverWait(wd, 15).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Review')]")))
        self.click_next_page(wd)

    def click_agreement(self, wd):
        try:
            self.click_ele(wd, '[data-automation-id="agreementCheckbox"]')
        except:
            self.click_ele(wd, '[id="termsAndConditions--acceptTermsAndAgreements"]')
