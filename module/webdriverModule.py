import hashlib
import os
import random
import shutil
import signal
import sys
import tempfile
import threading
import time

from fake_useragent import UserAgent
from loguru import logger
from selenium.webdriver.common.action_chains import ActionChains
from undetected_chromedriver import Chrome, ChromeOptions, patcher
from webdriver_manager.core.os_manager import ChromeType, OperationSystemManager


class WebdriverModule:
    _chromedriver_lock = threading.Lock()
    _chromedriver_checked = False

    def __init__(self):
        self.referer_urls = [
            "https://www.google.com",
            "https://www.glassdoor.com",
            "https://www.linkedin.com",
            "https://www.indeed.com",
            "https://www.bing.com",
            "https://www.yahoo.com",
            "https://www.twitter.com",
        ]
        self.pid = None
        self.wd = None
        self.version = int(
            OperationSystemManager().get_browser_version_from_os(ChromeType.GOOGLE).split('.')[0]
        )

        self.user_data_dir = os.path.expanduser("~") + "/Library/Application Support/Google/Chrome"
        self.profile_directory = self._get_chrome_profiles()

        # Ensure chromedriver is present
        self._ensure_chromedriver()

    def _get_chrome_profiles(self):
        """
        Dynamically finds all Chrome profile directories.
        """
        profiles = []
        if not os.path.exists(self.user_data_dir):
            logger.warning(f"Chrome user data directory not found at: {self.user_data_dir}")
            return profiles

        for item in os.listdir(self.user_data_dir):
            item_path = os.path.join(self.user_data_dir, item)
            if os.path.isdir(item_path) and (item == "Default" or item.startswith("Profile ")):
                profiles.append(item)

        return profiles

    def _ensure_chromedriver(self):
        """
        Ensures that the chromedriver binary is present. If not, downloads it.
        This method is thread-safe and handles cases where the binary exists but may need re-downloading.
        """
        with WebdriverModule._chromedriver_lock:
            chromedriver_path = self._get_chromedriver_path()

            # Always check if chromedriver is actually present and functional
            if not self._is_chromedriver_present(chromedriver_path):
                logger.info("Chromedriver is not present. Downloading...")
                self.download_chromedriver()
                logger.info("Chromedriver downloaded successfully.")
                WebdriverModule._chromedriver_checked = True
            elif not WebdriverModule._chromedriver_checked:
                # Binary exists and is executable, just mark as checked
                WebdriverModule._chromedriver_checked = True

    def _get_chromedriver_path(self):
        """
        Constructs the path to the chromedriver binary.
        """
        data_path = patcher.Patcher.data_path
        chromedriver_filename = 'undetected_chromedriver'
        chromedriver_path = os.path.join(data_path, chromedriver_filename)
        return chromedriver_path

    def _is_chromedriver_present(self, chromedriver_path):
        """
        Checks if the chromedriver binary is present and executable.
        Also validates that it's a valid chromedriver binary.
        """
        if not os.path.exists(chromedriver_path):
            return False

        if not os.access(chromedriver_path, os.X_OK):
            return False

        return True

    def download_chromedriver(self):
        """
        Downloads the chromedriver binary using the Patcher class.
        """
        try:
            patcher_instance = patcher.Patcher()
            patcher_instance.auto(version_main=self.version)
        except Exception as e:
            logger.error(f"Failed to download chromedriver: {e}")
            sys.exit(-1)

    @classmethod
    def reset_chromedriver_check(cls):
        """
        Reset the chromedriver check flag. Useful when Chrome version changes
        or when chromedriver needs to be re-downloaded.
        """
        with cls._chromedriver_lock:
            cls._chromedriver_checked = False
            logger.info("Chromedriver check flag reset - will re-verify on next instance creation")

    @classmethod
    def get_chromedriver_status(cls):
        """
        Get the current status of chromedriver for debugging purposes.
        """
        with cls._chromedriver_lock:
            instance = cls.__new__(cls)  # Create temporary instance without calling __init__
            chromedriver_path = instance._get_chromedriver_path()

            status = {
                'checked_flag': cls._chromedriver_checked,
                'path': chromedriver_path,
                'exists': os.path.exists(chromedriver_path),
                'executable': os.access(chromedriver_path, os.X_OK) if os.path.exists(chromedriver_path) else False,
                'file_size': os.path.getsize(chromedriver_path) if os.path.exists(chromedriver_path) else 0
            }

            return status

    def _human_interaction(self):
        """基础人类行为模拟"""
        # 随机滚动
        self.wd.execute_script(f"window.scrollBy(0, {random.randint(200, 800)})")
        time.sleep(random.uniform(0.5, 1.5))

        # 鼠标移动
        ActionChains(self.wd).move_by_offset(
            random.randint(10, 50),
            random.randint(10, 50)
        ).perform()

    def driver_chrome_seen(self) -> Chrome:
        options = ChromeOptions()
        # 保持原有扩展加载
        options.add_argument(
            f'--load-extension={os.path.join(os.path.dirname(os.path.abspath(__file__)), "extension")}'
        )
        prefs = {
            "profile.default_content_setting_values.geolocation": 2  # 1: Allow, 2: Block
        }
        options.add_experimental_option("prefs", prefs)

        # random user agent
        selected_user_agent = UserAgent(browsers=['chrome'], os=['windows', 'macos', 'linux']).getChrome
        options.add_argument(f"--user-agent={selected_user_agent}")

        # Enhanced browser configuration
        self.wd = Chrome(
            options=options,
            version_main=self.version,
            driver_executable_keep_alive=True,
            use_subprocess=True,  # Use subprocess for better stability
            browser_language="en-US,en",
        )

        self._setup_driver()
        self._human_interaction()  # 初始人类行为
        return self.wd

    def driver_chrome_hide(self) -> Chrome:
        options = ChromeOptions()

        # 保持原有扩展加载
        options.add_argument(
            f'--load-extension={os.path.join(os.path.dirname(os.path.abspath(__file__)), "extension")}'
        )

        prefs = {
            "profile.default_content_setting_values.geolocation": 2  # 1: Allow, 2: Block
        }
        options.add_experimental_option("prefs", prefs)

        self.wd = Chrome(
            headless=True,
            options=options,
            version_main=self.version,
            driver_executable_keep_alive=True,
            use_subprocess=True  # Use subprocess for better stability
        )

        self._setup_driver()
        return self.wd

    def driver_chrome_with_profile(self, user: str) -> Chrome:
        """
        Specifically for given profile compatibility with Chrome 137.
        This method creates a temporary profile. If existing profiles are configured,
        it bases the temporary profile on a deterministically assigned existing profile.
        Otherwise, it uses a clean, 'mocked' profile.
        """

        # Create a working profile directory
        temp_dir = tempfile.mkdtemp(prefix="chrome_profile_")
        working_profile = os.path.join(temp_dir, "working_profile")
        os.makedirs(working_profile, exist_ok=True)

        # If profiles are configured, copy from a deterministically selected one
        if self.profile_directory:
            user_hash = int(hashlib.md5(user.encode('utf-8')).hexdigest(), 16)
            profile_index = user_hash % len(self.profile_directory)
            selected_profile_name = self.profile_directory[profile_index]
            source_profile = os.path.join(self.user_data_dir, selected_profile_name)

            # Copy key profile files that are safe to copy
            safe_files = [
                "Preferences", "Local State", "Bookmarks", "History",
                "Login Data", "Web Data", "Cookies", "Extensions"
            ]

            for filename in safe_files:
                src_file = os.path.join(source_profile, filename)
                if os.path.exists(src_file):
                    if os.path.isdir(src_file):
                        try:
                            shutil.copytree(src_file, os.path.join(working_profile, filename))
                        except Exception as e:
                            logger.debug(f"Could not copy directory {filename}: {e}")
                    else:
                        try:
                            shutil.copy2(src_file, working_profile)
                        except Exception as e:
                            logger.debug(f"Could not copy file {filename}: {e}")
        else:
            logger.warning(f"No Chrome profiles configured. Using a temporary, clean profile for user '{user}'.")

        # Configure Chrome options for Chrome 138 compatibility
        options = ChromeOptions()
        options.add_argument(f"--user-data-dir={temp_dir}")
        options.add_argument("--profile-directory=working_profile")

        prefs = {
            "profile.default_content_setting_values.geolocation": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.images": 1
        }
        options.add_experimental_option("prefs", prefs)

        chrome_kwargs = {
            "options": options,
            "version_main": self.version,
            "driver_executable_keep_alive": True,
            "browser_language": "en-US,en",
            "use_subprocess": True,
        }

        self.wd = Chrome(**chrome_kwargs)
        self.pid = self.wd.browser_pid
        self.wd.maximize_window()

        # Store temp directory for cleanup
        self.temp_profile_dir = temp_dir
        return self.wd

    def _setup_driver(self):
        """增强浏览器环境配置"""
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chromedriver", "undetection.js")
        with open(script_path, "r", encoding='utf-8') as f:
            self.wd.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument",
                                    {"source": f.read()})

        # 随机HTTP头
        headers = {
            'Referer': random.choice(self.referer_urls),
        }
        self.wd.execute_cdp_cmd('Network.setExtraHTTPHeaders',
                                {'headers': headers})
        # Store the browser PID
        self.pid = self.wd.browser_pid

        # maximum window
        self.wd.maximize_window()

    def __del__(self):
        # 保持原有清理逻辑
        if hasattr(self, 'pid') and self.pid:
            try:
                os.kill(self.pid, signal.SIGTERM)
            except Exception:
                pass
        if hasattr(self, 'wd') and self.wd:
            try:
                self.wd.quit()
            except Exception:
                pass
        # Clean up temporary profile directory if it exists
        if hasattr(self, 'temp_profile_dir') and os.path.exists(self.temp_profile_dir):
            try:
                shutil.rmtree(self.temp_profile_dir, ignore_errors=True)
            except Exception:
                pass
