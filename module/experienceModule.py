import re


class experienceModule:
    def __init__(self):
        self.unwanted_languages = [
            'Android', 'Front', 'iOS', 'React'
        ]

        self.unwanted_experiences = [
            'Principal', 'Intern', 'VP', 'Lead', 'Director', 'Distinguished', 'CTJ', 'Vice President',
            'Consultant', 'Mgr', 'Environment', 'Battery', 'Mechanical', 'Technician', 'Cybersecurity',
            'Wireless', 'Manufacturing', 'Summer', 'Princ', 'Embedded', 'Expert', 'Overnight', 'Electrical',
            'Hardware', 'Security', 'Silicon', 'Network', 'Head', 'Cellular', 'CPU', 'CAD', 'Government',
            'Representative', 'Professional', 'Specialist', 'Construction', 'Quantum', 'ASIC', 'TS/SCI',
            'Executive', 'Chief', 'Federal', 'Account', 'PCB', 'Licensing', 'Part Time', 'Compliance', 'Vehicle',
            'Integration', 'Ultrasonic', 'SRAM', 'Power', 'Circuit', 'Signal', 'AV', 'Optical', 'Aerodynamics',
            'Avionics', 'Thermal', 'Industrial', 'Flight', 'Cyber', 'Telecom', 'HTTPS', 'Health', 'GPU', 'HID',
            'HW', 'Graphics', '3D', 'RF', 'Telephony', 'Vision', 'Verification', 'Validation', 'Physical',
            'Firmware', 'Electrician', 'Camera', 'Quality', 'Energy', 'Factory', 'Building', 'Co-Op', '5G',
            'Sales', 'Structural', 'Semiconductor', 'Design', 'Designer', 'Materials', 'Trainer',
            'Equipment', 'Display', 'Modem', 'Co-op', 'Source', 'Sourcer', 'Codec', 'University', 'Watch',
            'Cellsite', 'Writer', 'Military', 'Clinical', 'Acceleration Program', 'Category', 'Field', 'CAPA',
            'Shift', 'Counsel', 'Survivability', 'Compact', 'Automotive', 'Assembly', 'Layout', 'Emulation',
            'LTD', 'Facilities', 'Cognitive', 'Analog', 'Gas', 'Facility', 'Capacity', 'Customer Engineer',
            'Margin', 'Governance', 'Product Owner', 'Operations Engineer', 'Mobile Engineer', 'Curriculum Developer',
            'Value Engineer', 'Vulnerability', 'Hospitality', 'Technical', 'Temporary', 'Lab Engineer',
            'Partnerships Manager', 'Engineering Manager', 'Project Manager', 'Program Manager', 'Technical Manager',
            'Team Manager', 'IT Manager', 'System Manager',
            'Infrastructure Manager', 'Operations Manager', 'Support Manager', 'Service Manager',
            'Quality Manager', 'Test Manager', 'QA Manager', 'Resource Manager', 'Account Manager',
            'Sales Manager', 'Marketing Manager', 'Data Science Manager', 'Certification Manager',
            'Senior Manager', 'Sr Manager', 'Manager, Engineering', 'Staff Manager', 'Sr. Manager',
            'Store Manager', 'Portfolio Manager', 'Risk Manager', 'Finance Manager', 'Business Development Manager',
            'Counter Manager', 'Advisory Manager', 'Group Manager', 'Economist', 'Media Manager', 'Service Associate',
            'Solutions Manager', 'Retail Banker'
        ]

        self.wanted_titles = [
            'Software', 'Cloud', 'Analyst', 'Data', 'Site Reliability', 'Application', 'Engineer', 'Java',
            'Full Stack', 'AWS', 'Python', 'BI', 'MLE', 'Azure', 'GCP', 'Google Cloud', 'SRE',
            'Product Manager', 'PM'
        ]

        self.compile_patterns()

    def compile_patterns(self):
        self.unwanted_lang_pattern = re.compile(
            r'(?i)(?:' + '|'.join(map(re.escape, self.unwanted_languages)) + r')'
        )
        self.unwanted_experience_pattern = re.compile(
            r'(?i)(?:' + '|'.join(map(re.escape, self.unwanted_experiences)) + r')'
        )
        self.wanted_title_pattern = re.compile(
            r'(?i)(?:' + '|'.join(map(re.escape, self.wanted_titles)) + r')'
        )

    def whatIWant(self, job_title: str) -> bool:
        if not job_title:
            return False
        return bool(
            not self.unwanted_lang_pattern.search(job_title) and
            self.wanted_title_pattern.search(job_title) and
            not self.unwanted_experience_pattern.search(job_title)
        )
