import json
import os
import re
from base64 import b64decode
from datetime import datetime
from urllib.parse import parse_qsl, unquote, urlparse, urlsplit

import loguru
import requests
import yaml

logger = loguru.logger

new_clash_conf = {
    'mixed-port': 7890,
    'allow-lan': False,
    'mode': 'Rule',
    'log-level': 'info',
    'tun': {
        'enable': True,
        'stack': 'system',
        'device': 'utun',
        'auto-route': False,
        'auto-detect-interface': False,
        'dns-hijack': [
            'tcp://any:53'
        ]
    },
    "dns": {
        "enable": True,
        "listen": '127.0.0.1:5334',
        "default-nameserver": ['***********', '************', '*********'],
        "enhanced-mode": "fake-ip",
        "fake-ip-range": "**********/16",
        "use-hosts": True,
        "fallback": [
            "https://dns.cloudflare.com/dns-query",
            "https://public.dns.iij.jp/dns-query",
            "tls://***************:853",
            "https://***************/dns-query",
            "https://public.dns.iij.jp/dns-query",
            "https://**************/dns-query"
        ],
        "fallback-filter": {
            "domain": [
                "+.google.com",
                "+.facebook.com",
                "+.twitter.com",
                "+.youtube.com",
                "+.xn--ngstr-lra8j.com",
                "+.google.cn",
                "+.googleapis.cn",
                "+.googleapis.com",
                "+.gvt1.com",
                "+.paoluz.com",
                "+.paoluz.link",
                "+.paoluz.xyz",
                "+.sodacity-funk.xyz",
                "+.nloli.xyz",
                "+.jsdelivr.net",
                "+.proton.me"
            ],
            "geoip": True,
            "ipcidr": [
                "240.0.0.0/4",
                "0.0.0.0/32",
                "127.0.0.1/32"
            ]
        },
        "ipv6": True,
        "nameserver": [
            "***************",
            "************",
            "*********",
            "https://doh.pub/dns-query",
            "https://*********/dns-query",
            "tls://*********:853",
            "https://*********/dns-query",
            "https://************/dns-query"
        ]
    },
    'proxies': [
        {'name': 'warp_proxy', 'server': '127.0.0.1', 'port': 40000, 'type': 'socks5'},
        {'ip': '**********', 'mtu': 1280, 'name': '🇳🇱 NL-CF-WARP-1', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '**************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇳🇱 NL-CF-WARP-2', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '*************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇺🇸 US-CF-WARP-3', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '***************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇺🇸 US-CF-WARP-4', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '***************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇳🇱 NL-CF-WARP-5', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '**************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇺🇸 US-CF-WARP-6', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '**************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇺🇸 US-CF-WARP-7', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '**************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇳🇱 NL-CF-WARP-8', 'port': 8886,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '*************', 'type': 'wireguard', 'udp': True},
        {'ip': '**********', 'mtu': 1280, 'name': '🇳🇱 NL-CF-WARP-9', 'port': 3854,
         'private-key': 'KEeuUuY+SnmMzlogzyOVH+50jUtjlcro3HyafPHIKHk=',
         'public-key': 'bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=', 'remote-dns-resolve': True,
         'server': '**************', 'type': 'wireguard', 'udp': True}
    ],
    'proxy-groups': [
        {'name': 'warp', 'type': 'select', 'proxies': []},
        {'name': '不选择', 'type': 'select', 'proxies': ['DIRECT']},
        {'name': 'tesla-lb', 'type': 'load-balance', 'strategy': 'round-robin',
         'url': 'http://www.gstatic.com/generate_204', 'interval': 300,
         'proxies': ['warp_proxy', '🇳🇱 NL-CF-WARP-1', '🇳🇱 NL-CF-WARP-2', '🇺🇸 US-CF-WARP-3', '🇺🇸 US-CF-WARP-4',
                     '🇳🇱 NL-CF-WARP-5', '🇺🇸 US-CF-WARP-6', '🇺🇸 US-CF-WARP-7', '🇳🇱 NL-CF-WARP-8', '🇳🇱 NL-CF-WARP-9']},
        {'name': '自动选择', 'type': 'load-balance', 'strategy': 'round-robin',
         'url': 'http://www.gstatic.com/generate_204', 'interval': 300, 'tolerance': 100, 'proxies': []}
    ],
    'rules': [
        'DOMAIN-SUFFIX,linkedin.com,不选择',
        'DOMAIN-SUFFIX,rustdesk.com,不选择',
        'DOMAIN-SUFFIX,github.com,不选择',
        'DOMAIN-SUFFIX,openai.com,不选择',
        'DOMAIN-SUFFIX,qq.com,warp',
        'DOMAIN-SUFFIX,amazon.jobs,warp',
        'DOMAIN-SUFFIX,leetcode.com,warp',
        'DOMAIN-SUFFIX,myworkdaysite.com,warp',
        'DOMAIN-SUFFIX,myworkdayjobs.com,warp',
        'DOMAIN-SUFFIX,myworkdaycdn.com.cn,warp',
        'DOMAIN-SUFFIX,myworkdaycdn.com,warp',
        'DOMAIN-SUFFIX,myworkday.com,warp',
        'DOMAIN-SUFFIX,microsoft.com,warp',
        'DOMAIN-SUFFIX,taleo.net,warp',
        'DOMAIN-SUFFIX,tiktok.com,warp',
        'DOMAIN-SUFFIX,metacareers.com,warp',
        'DOMAIN-SUFFIX,rippling.com,warp',
        'DOMAIN-SUFFIX,ripplingcdn.com,warp',
        'DOMAIN-SUFFIX,coinbase.com,warp',
        'DOMAIN-SUFFIX,bloomberg.com,不选择',
        'DOMAIN-SUFFIX,brassring.com,warp',
        'DOMAIN-SUFFIX,byteoversea.com,warp',
        'DOMAIN-SUFFIX,tiktokcdn.com,warp',
        'DOMAIN-SUFFIX,ibytedtos.com,warp',
        'DOMAIN-SUFFIX,capsolver.com,warp',
        'DOMAIN-SUFFIX,snowflake.com,warp',
        'DOMAIN-SUFFIX,dropbox.com,warp',
        'DOMAIN-SUFFIX,ashbyhq.com,warp',
        'DOMAIN-SUFFIX,recaptcha.net,warp',
        'DOMAIN-SUFFIX,oraclecloud.com,warp',
        'DOMAIN-SUFFIX,tesla.com,tesla-lb',
        'DOMAIN-SUFFIX,*************,不选择',
        'DOMAIN-SUFFIX,github.io,不选择',
        'DOMAIN-KEYWORD,*,自动选择',
        'MATCH,自动选择'
    ]
}


def make_yaml():
    current_time = str(datetime.now().strftime('%Y%m%d'))
    year = str(datetime.now().year)
    month = str(datetime.now().month).zfill(2)
    day = str(datetime.now().day)
    node_free_urls = []
    for d in range(max(1, int(day), 1)):
        url = f"https://nodefree.githubrowcontent.com/{year}/{month}/{year}{month}{str(d).zfill(2)}.yaml"
        node_free_urls.append(url)

    for d in range(max(1, int(day), 1)):
        url = f"https://banyunxiaoxi.icu/{year}/{month}/{year}{month}{str(d).zfill(2)}"
        node_free_urls.append(url)

    clash_sub_urls = [
        "https://raw.githubusercontent.com/MrMohebi/xray-proxy-grabber-telegram/master/collected-proxies/clash-meta/all.yaml",
        "https://raw.githubusercontent.com/peasoft/NoMoreWalls/master/list.meta.yml",
        "https://raw.githubusercontent.com/vxiaov/free_proxies/refs/heads/main/clash/clash.provider.yaml",
        "https://subs.zeabur.app/clash",
        "https://raw.githubusercontent.com/mfuu/v2ray/master/clash.yaml",
        "https://raw.githubusercontent.com/chengaopan/AutoMergePublicNodes/master/list.yml",
        "https://www.xrayvip.com/free.yaml",
        "https://raw.githubusercontent.com/a2470982985/getNode/main/clash.yaml",
        "https://raw.githubusercontent.com/aiboboxx/clashfree/main/clash.yml",
        "https://sub.xeton.dev/sub?target=clash&new_name=true&url=https%3A%2F%2Fproxy.v2gh.com%2Fhttps%3A%2F%2Fraw.githubusercontent.com%2FPawdroid%2FFree-servers%2Fmain%2Fsub&insert=false&config=https%3A%2F%2Fraw.githubusercontent.com%2FACL4SSR%2FACL4SSR%2Fmaster%2FClash%2Fconfig%2FACL4SSR_Online.ini",
        "https://raw.githubusercontent.com/ermaozi/get_subscribe/main/subscribe/clash.yml",
        "https://chromego-sub.netlify.app/sub/merged_proxies_new.yaml",
        "https://fastly.jsdelivr.net/gh/freenodes/freenodes@main/ClashPremiumFree.yaml",
        "https://raw.githubusercontent.com/free18/v2ray/refs/heads/main/c.yaml",
        "https://raw.githubusercontent.com/ermaozi01/free_clash_vpn/main/subscribe/clash.yml",
        "https://raw.githubusercontent.com/ripaojiedian/freenode/main/clash"
    ]
    clash_sub_urls += node_free_urls
    seen_proxy = set()
    for i, url in enumerate(clash_sub_urls):
        response = requests.get(url)
        if response.status_code == 200:
            content = response.text
            try:
                clash_conf = yaml.load(content, Loader=yaml.CFullLoader)
                try:
                    proxies = clash_conf.get("proxies")
                    for proxy in proxies:
                        try:
                            if not proxy.get('cipher') or proxy.get('cipher') == 'none':
                                continue
                            if proxy.get('cipher') in ['ss', '2022-blake3-chacha20-poly1305', '2022-blake3-aes-256-gcm',
                                                       'chacha20-poly1305']:
                                continue
                            proxy_name = proxy.get('name')
                            if proxy_name in seen_proxy or 'CloudFlare' in proxy_name or 'CN' in proxy_name: continue
                            if proxy.get('type') not in ['vless', 'hysteria', 'hysteria2'] and proxy.get(
                                    'type') and proxy.get('network') != 'grpc':
                                new_clash_conf['proxies'].append(proxy)
                            seen_proxy.add(proxy_name)
                        except:
                            logger.error(proxy)
                except:
                    share_links = b64decode(content).decode('utf-8').splitlines()
                    for share_link in share_links:
                        if share_link.startswith("vmess"):
                            url = urlsplit(share_link)
                            item = json.loads(b64decode(url.netloc))
                            if not item.get('ps') or item.get('net') == 'grpc': continue
                            if item['ps'] in seen_proxy or item['ps']: continue
                            obj = {
                                'name': item.get('ps'),
                                'type': 'vmess',
                                'server': item.get('add'),
                                'port': item.get('port'),
                                'uuid': item.get('id'),
                                'alterId': item.get('aid'),
                                'cipher': 'auto' if item.get('type') == 'none' else None,
                                'ucp': True,
                                'network': item.get('net'),
                                'ws-path': item.get('path'),
                                'ws-headers': {
                                    'Host': item.get('host')
                                },
                                'tls': True if item.get('tls') == 'tls' else None,
                            }
                            new_clash_conf['proxies'].append(obj)
                        elif share_link.startswith("trojan"):
                            parsed = urlparse(share_link)
                            query = dict(parse_qsl(parsed.query))
                            if not unquote(parsed.fragment.encode('utf-8')): continue
                            if unquote(parsed.fragment.encode('utf-8')) in seen_proxy: continue
                            settings = {
                                'name': unquote(parsed.fragment.encode('utf-8')),
                                'password': parsed.username,
                                'host': parsed.hostname,
                                'port': parsed.port,
                                'allowInsecure': query.get('allowInsecure', '1'),
                                'peer': query.get('peer'),
                                'sni': query.get('sni')
                            }
                            new_clash_conf['proxies'].append(settings)
                print("Finished: ", i)
            except Exception as e:
                try:
                    vmess_urls = re.findall(r'vmess://(.*)', content)
                    for vmess_url in vmess_urls:
                        url = urlsplit(vmess_url)
                        item = json.loads(b64decode(url.netloc))
                        if not item.get('ps') or item.get('net') == 'grpc': continue
                        if item['ps'] in seen_proxy or item['ps']: continue
                        obj = {
                            'name': item.get('ps'),
                            'type': 'vmess',
                            'server': item.get('add'),
                            'port': item.get('port'),
                        }
                        new_clash_conf['proxies'].append(obj)
                except:
                    pass
                logger.exception(e)
    dedup = set()
    for proxy in new_clash_conf['proxies']:
        if proxy.get('cipher') and proxy.get('cipher') == '2022-blake3-aes-256-gcm':
            continue
        if proxy.get('port') and "/" in str(proxy.get('port')) and not proxy.get('name').startswith('warp'):
            proxy['port'] = int(proxy.get('port').split("/")[0])
        if proxy.get('name') in dedup:
            continue
        new_clash_conf['proxy-groups'][-1]['proxies'].append(proxy.get('name'))
        dedup.add(proxy.get('name'))
    new_clash_conf['proxy-groups'][0]['proxies'].append('warp_proxy')
    dst_path = os.path.join(os.path.expanduser("~"), ".config", "clash")
    stream = open(f'{dst_path}/my_proxy.yaml', 'w', encoding="utf-8")
    yaml.safe_dump(new_clash_conf, stream, allow_unicode=True)
    stream.close()


make_yaml()
