# -*- coding: utf-8 -*-
# @Time  : 10/1/23 00:23
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: ups.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class upsApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you under 18 years of age": "No",
                "Do you have a relative currently or previously employed by UPS?": "No",
                "Do you have a Bachelor's Degree": "Yes",
                "Will you now or in the future need UPS employment sponsorship": self.user_visa_ans,
                "Are you willing to work flexible shifts if applicable": "Yes",
                "Are you a current UPS employee?": "No",
                "Do you require sponsorship to work in the US": self.user_visa_ans,
                "Have you informed your supervisor/manager that you are applying to this role": "Yes",
                "Have you been in your role for the past 12 months?": "Yes",
                "Are you currently on a Performance Improvement Plan": "No",
                "Do you acknowledge": "Yes",
                "What is your Bachelor's Degree": self.user_degree + " in Computer Science at " + self.user_school,
                "What is your highest education": self.user_degree
            }
            self.question_select(wd, question_dict)
            addition_question_dict = {
                "Will you now or in the future require UPS to sponsor": self.user_visa_ans,
                "What is your Bachelor's Degree": self.user_degree + " in Computer Science at " + self.user_school
            }
            self.question_select(wd, addition_question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Are you comfortable with working overtime including on Weekends": "Yes",
                "Do you possess": "Yes",
                "Do you have": "Yes",
                "How would you rate": "Excellent",
                "How many years": "3-5 years",
                "How much": "3-5 years",
                "Are you proficient": "Yes",
                "What is your highest education": self.user_degree
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
