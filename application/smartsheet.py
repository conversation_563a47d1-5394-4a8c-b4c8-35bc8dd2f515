# -*- coding: utf-8 -*-
# @Time  : 9/27/23 23:09
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: smartsheet.py
# @Software: PyCharm
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class smartsheetApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you hear about": "LinkedIn",
            "Are you currently lawfully": "Yes",
            "Applicant Privacy Notice": "I acknowledge"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
