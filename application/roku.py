from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class rokuApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[id="toggleApply"]')
        except:
            self.expected_shown_element(wd, '[class="page-block"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="page-block"]').find_element(By.CSS_SELECTOR,
                                                                                     'h1').text == "Oops!":
                self.database.delete(f"link='{url}'")
                return -4
        try:
            self.click_ele(wd, '[id="consent_reject"]')
        except:
            pass
        self.hide.hideElement(wd, 'nav')
        self.click_ele(wd, '[id="toggleApply"]')
        wd.find_element(By.CSS_SELECTOR, '[placeholder="First Name"]').send_keys(self.user_name.split()[0])
        wd.find_element(By.CSS_SELECTOR, '[placeholder="Last Name"]').send_keys(self.user_name.split()[1])
        wd.find_element(By.CSS_SELECTOR, '[placeholder="Email"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[data-candidate-field="candidate_phone"]').send_keys(self.user_phone)
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        try:
            wd.find_element(By.CSS_SELECTOR, '[placeholder="LinkedIn Profile"]').send_keys(self.user_linkedin)
        except:
            pass
        for select_question in wd.find_elements(By.CSS_SELECTOR, 'select[class="form-control"]'):
            select_question_text = select_question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                             'label').text
            if select_question_text.startswith('How did you hear about us'):
                Select(select_question).select_by_visible_text("LinkedIn")
            elif select_question_text.startswith('Are you authorized to lawfully work'):
                Select(select_question).select_by_visible_text("Yes")
            elif select_question_text.startswith('Do you now, or will you in the future'):
                Select(select_question).select_by_visible_text(self.user_visa_ans)
            elif select_question_text.startswith('Which of the following best describes your gender'):
                Select(select_question).select_by_visible_text("Male" if self.user_gender_male else "Female")
            elif select_question_text.startswith('Have you ever served in the military'):
                Select(select_question).select_by_visible_text("No")
        agree = wd.find_element(By.CSS_SELECTOR,
                                '[class="form-group form-template-field-consent consent"]').find_element(
            By.CSS_SELECTOR, 'input[type="checkbox"]')
        self.click_ele(wd, agree)
        self.click_ele(wd, '[class="submit-state submit-start"]')
        try:
            self.expected_shown_element(wd, "//p[starts-with(text(), 'Thank you for')]")
            return 1
        except:
            return 0
