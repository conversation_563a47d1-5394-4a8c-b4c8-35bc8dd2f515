from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class anthropicApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you open to working in-person": "Yes",
            "AI Policy for Application": "Yes",
            "Are you open to relocation": "Yes",
            "In one paragraph, provide an example": "N/A",
            "Additional Information": "N/A",
            "LinkedIn Profile": self.user_linkedin,
            "Will you now or will you in the future require": self.user_visa_ans,
            "What is your preferred programming language": "Python",
            "Coding Language Preference": "Python",
            "(Optional) Personal Preferences": self.user_name.split()[0],
            "What is your prior experience": "N/A",
            "Of the locations listed on": "San Francisco, CA",
            "Walk us through a recruiting data project": "N/A",
            "Our team is in": "Yes",
            "This role is based in": "Yes",
            "Have you ever interviewed at Anthropic": "No",
            "Have you built": "Yes",
            "Do you have experience developing": "Yes",
            "Have you worked with": "Yes",
            "Have you contributed to projects": "Yes",
            "Have you made significant contributions": "No",
            "Have you maintained an open": "No",
            "Have you designed and implemented APIs that are used by external": "No"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
