from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class caterpillarApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you authorized to work in the United States?": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "The Caterpillar Nepotism Policy exists to prevent nepotism at all": "No",
                "If relocation is required for this opportunity,": "Yes"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title in ['Application Questions 2 of 2',
                     'Application Questions 2 of 3']:
            question_2_d = {
                "Please indicate if you are a full-time": "Yes",
                "Please indicate if your overall Grade Point Average": "Yes",
                "Please indicate how many": "3 Years - Less than 5 Years",
                "Please indicate the highest level": self.user_degree,
                "Please indicate your level": "Intermediate",
                "Please indicate if you have": "Yes",
                "Please indicate whether you have ever signed": "No"
            }
            self.question_select(wd, question_2_d)
            try:
                checkbox = wd.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')[-1]
                self.click_ele(wd, checkbox)
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select the gender": "Male" if self.user_gender_male else "Female",
                "Please select the ethnicity": "Asian",
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
