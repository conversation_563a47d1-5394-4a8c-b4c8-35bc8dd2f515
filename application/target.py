from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class targetApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally authorized for employment in the United States": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Currently, or in the future, does your authorization": "No",
                "Are you at least 18 years old": "Yes",
                "Do you have an employment agreement": "No",
                "Are you currently, or have you been a contractor with Target in the past 12 months": "No",
                "By selecting YES you are": "NO"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            try:
                self.click_ele(wd, '[id="585ff4f3103c0134a1ca21d5fe08de36"]')
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Race": "Asian (United States of America)",
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
