# -*- coding: utf-8 -*-
# @Time  : 12/1/23 21:14
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: RTX.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class RTXApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you either located in or willing to": "Yes",
                "Do you hold or have you held in the last 24 months a security clearance": "No",
                "Did you previously work for": "No",
                "Are you a": "No",
                "Are you currently an employee": "No",
                "Are you presently authorized to work": "Yes",
                "Are you subject to any legal or regulatory employment restrictions": "No",
                "Are you related": "No",
                'Are you in "good standing"': "Yes",
                "Have you been in your current": "Yes",
                "If an interview is scheduled": "Yes",
                "How did you hear about": "Internal Job Board",
                "Are you currently an RTX Talent Match participant": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            mapping_d = {
                "Bachelor": "Bachelors",
                "Master": "Master",
                "PhD": "Ph.D"
            }
            question_dict = {
                "What is your major": mapping_d[self.user_degree],
                "Did you previously work for": "No",
                "Are you a": "No",
                "Are you willing to travel": "Yes",
                "Are you currently enrolled": "No",
                'If you answered "Yes"': "N/A",
                "Do you now, or will you in the future require sponsorship": self.user_visa_ans,
                "Do you now, or will you in the future, require company sponsorship": self.user_visa_ans,
                "Are you a U.S. Person": "No",
                "Are you a citizen of": "No",
                "Do you hold or have you": "No",
                "Are you a student or an otherwise eligible": "Yes",
                "Are you either located in or willing to relocate": "Yes",
                "Do you possess an existing and active security clearance": "No",
                "Are you a CURRENT": "No",
                "Section": "No",
                "Please indicate the country where you were born": "China"
            }
            self.question_select(wd, question_dict)
            try:
                for question_2 in wd.find_elements(By.CSS_SELECTOR, '[class="css-7t35fz"]'):
                    question_2_text = question_2.find_element(By.CSS_SELECTOR, 'label').text
                    answer = wd.find_element(By.CSS_SELECTOR,
                                             f"""[id="{question_2.find_element(By.CSS_SELECTOR, 'label').get_attribute("for")}"]""")
                    if "N/A" in question_2_text and not answer.text:
                        answer.send_keys("N/A")
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Are you a protected veteran": "I AM NOT A VETERAN",
                "What is your Gender": "Male" if self.user_gender_male else "Female",
                "What is your race": "Asian",
                "What is your Sex": "Male" if self.user_gender_male else "Female",
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
