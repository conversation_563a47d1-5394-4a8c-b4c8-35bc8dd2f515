from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class tradedeskApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "PREFERRED NAME": self.user_name,
            "PREFERRED FIRST": self.user_name.split()[0],
            "PREFERRED LAST": self.user_name.split()[1],
            "ARE YOU ELIGIBLE TO WORK IN": "Yes",
            "ARE YOU AT LEAST 18": "Yes",
            "HAVE YOU BEEN EMPLOYED": "N/A",
            "HOW DID YOU HEAR ABOUT": ["LinkedIn", "Third Party Job Board"],
            "ARE YOU OPEN TO": "Yes",
            "Are you eligible to work": "Yes",
            "Are you at least 18": "Yes",
            "WILL YOU NEED A VISA SPONSORSHIP": self.user_visa_ans,
            "Have you been employed with The Trade Desk": "N/A",
            "How did you hear about": ["LinkedIn", "Third Party Job Board"],
            "Have you been employed": "N/A"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
