from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait

from module.applicationInterfaceModule import applicationInterface as applyInterface


class chewyApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            if wd.find_element(By.CSS_SELECTOR, '[data-ph-at-id="job-completion-info"]').text.strip().startswith(
                    "We're sorry"):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        self.expected_shown_element(wd, '[data-ph-at-id="apply-link"]')
        self.hide.hideElement(wd, '[class="ph-header"]')
        self.click_ele(wd, '[data-ph-at-id="apply-link"]')
        self.expected_shown_element(wd, '[class="upload-resume-btn btn primary-button"]')
        self.click_ele(wd, '[class="upload-resume-btn btn primary-button"]')
        self.expected_shown_element(wd, 'input[type="file"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        WebDriverWait(wd, 20).until(EC.alert_is_present())
        wd.switch_to.alert.accept()
        first_name = wd.find_element(By.CSS_SELECTOR, '[id="firstName"]')
        first_name.clear()
        first_name.send_keys(self.user_name.split()[0])
        last_name = wd.find_element(By.CSS_SELECTOR, '[id="lastName"]')
        last_name.clear()
        last_name.send_keys(self.user_name.split()[1])
        email = wd.find_element(By.CSS_SELECTOR, '[id="email"]')
        email.clear()
        email.send_keys(self.user_account)
        try:
            phone = wd.find_element(By.CSS_SELECTOR, '[id="phoneNumber"]')
            phone.clear()
            phone.send_keys(self.user_phone)
        except:
            pass
        try:
            location = wd.find_element(By.CSS_SELECTOR, '[aria-label="Location"]')
            location.clear()
            location.send_keys(", ".join([self.user_city, self.user_state, "United States"]))
            self.wait_x_sec(1)
            self.click_ele(wd, '[id="location-item-0"]')
        except:
            pass
        Select(wd.find_element(By.CSS_SELECTOR, '[id="country"]')).select_by_visible_text('United States')
        self.expected_shown_element(wd, '[id="state"]')
        self.wait_x_sec(5)
        Select(wd.find_element(By.CSS_SELECTOR, '[id="state"]')).select_by_visible_text(self.user_state)
        wd.find_element(By.CSS_SELECTOR, '[id="region"]').send_keys(self.user_city)
        try:
            wd.find_element(By.CSS_SELECTOR, '[id="location"]').send_keys("-119.417931")
        except:
            pass
        self.click_ele(wd, '[id="next"]')
        # Second page
        WebDriverWait(wd, 10).until(EC.presence_of_all_elements_located(
            (By.CSS_SELECTOR, '[id="previous"]')))
        question_dict = {
            "Based on our internal hiring policy: If you are an exempt Team Member": "Yes",
            "Would you now or in the future require sponsorship with Chewy?*": self.user_visa_ans,
            "How did you hear about Chewy": "LinkedIn",
            "Have you been employed by Chewy in the past?*": "No",
            "Are you open to relocation?*": "Yes",
            "What is your expected compensation range for this position?*": "$100,000 - $109,999",
            "What is your highest level of education obtained?*": "Master's Degree",
            "Are you legally authorized to work": "Yes",
            "Will you require sponsorship*": self.user_visa_ans,
            "Are you willing to": "Yes",
            "Are you 18 years of age or older?*": "Yes",
            "Will you now or in the future require sponsorship": self.user_visa_ans,
            "If you were hired at Chewy, what shift would you prefer to work": "Day",
            "Do you live within a commutable distance from the site?*": "No",
            "Deloitte is Chewy’s": "No",
            "Close family ties to a Deloitte": "No",
            "Are you a current employee at Chewy?*": "No",
            "Do you have": "Yes",
            "Will you require sponsorship": self.user_visa_ans
        }
        questions = wd.find_element(By.CSS_SELECTOR, 'fieldset').find_elements(By.CSS_SELECTOR,
                                                                               '[class="form-group field field-number"]')
        for question in questions:
            label = question.find_element(By.CSS_SELECTOR, "label")
            try:
                label.find_element(By.CSS_SELECTOR, '[class="required"]')
            except:
                continue
            question_text = label.text
            select_ele = question.find_element(By.CSS_SELECTOR, 'select')
            if any(question_text.startswith(x) for x in question_dict.keys()):
                Select(select_ele).select_by_visible_text(
                    question_dict[next(filter(lambda x: question_text.startswith(x), question_dict.keys()))])
            else:
                if question_text:
                    self.logger.error(f"Company: {self.company}, Question: {question_text}")
        try:
            self.click_ele(wd, 'input[type="checkbox"]')
        except:
            pass
        self.click_ele(wd, '[id="next"]')
        # Third page
        self.expected_shown_element(wd, '[aria-label="APPLICANT/EMPLOYEE TYPED SIGNATURE:"]')
        self.click_ele(wd, '[id="agreementSection.acceptAgreement"]')
        wd.find_element(By.CSS_SELECTOR, '[aria-label="APPLICANT/EMPLOYEE TYPED SIGNATURE:"]').send_keys(self.user_name)
        self.click_ele(wd, '[class="btn primary-button btn-submit"]')
        self.expected_shown_element(wd, '[class="content"]')
        if wd.find_element(By.CSS_SELECTOR, '[class="content"]').text.startswith("One More Step!"):
            return 1
        return 0
