from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class jnjApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[id="js-apply-external"]')
        except:
            self.expected_shown_element(wd, '[class="hero-heading"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="hero-heading"]').text.startswith("Page Not Found"):
                self.database.delete(f"link = '{url}'")
                return -4
        wd.get(wd.find_element(By.CSS_SELECTOR, '[id="js-apply-external"]').get_attribute('href'))
        self.expected_shown_element(wd,
                                    '[id="dialogTemplate-dialogForm-StatementBeforeAuthentificationContent-ContinueButton"]')
        self.click_ele(wd, '[id="dialogTemplate-dialogForm-StatementBeforeAuthentificationContent-ContinueButton"]')
        self.expected_shown_element(wd, '[id="dialogTemplate-dialogForm-login-name1"]')
        wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-login-name1"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-login-password"]').send_keys(self.user_pwd)
        try:
            self.click_ele(wd, '[id="dialogTemplate-dialogForm-login-defaultCmd"]')
        except:
            pass
        try:
            self.expected_shown_element(wd, '[id="errorIAM"]')
            return -1
        except:
            pass
        # Applied
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Review and Submit":
                return 1
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'Resume Upload')]")))
            wd.find_element(By.CSS_SELECTOR, '[class="attachment-list-column"]') \
                .find_element(By.CSS_SELECTOR, 'input').click()
            self.click_ele(wd, '[id="editTemplateMultipart-editForm-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'Personal Information')]")))
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'Questions')]")))
            for question in wd.find_element(By.CSS_SELECTOR, '[class="questionnaire"]').find_elements(By.CSS_SELECTOR,
                                                                                                      'fieldset'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="requiredimg"]')
                except:
                    continue
                question_text = question.find_element(By.CSS_SELECTOR, '[class="description"]').text.strip()
                answer_choices = question.find_elements(By.CSS_SELECTOR, '[class="possibleanswers"]')
                if not answer_choices:
                    answer_choices = question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if any(x.get_attribute("checked") == 'true' for x in answer_choices):
                    continue
                if question_text.startswith("Through which of the channels in the following list did you"):
                    a_id = answer_choices[1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith(
                        "Do you have any agreement that would restrict") or question_text.startswith(
                    "Due to the United States") or question_text.startswith("As part of our Re-Ignite"):
                    a_id = answer_choices[1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Education Level"):
                    if self.user_degree == 'PhD':
                        self.user_degree = 'Doctorate'
                    answer_choice = next(
                        filter(lambda x: x.find_element(By.XPATH, '..').text.startswith(self.user_degree),
                               answer_choices))
                    a_id = answer_choice.get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("How many years"):
                    answer_choice = next(
                        filter(lambda x: x.find_element(By.XPATH, '..').text.startswith("3"),
                               answer_choices))
                    a_id = answer_choice.get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif ("position" in question_text or question_text.startswith(
                        "Are you legally authorized to work in the United States") or "location requirements" in question_text or question_text.startswith(
                    "Are you able to") or question_text.startswith("Do you have") or question_text.startswith(
                    "This position requires") or question_text.startswith(
                    "Are you willing") or question_text.startswith("This position") or question_text.startswith(
                    "In what range") or question_text.startswith("Are you familiar with") or question_text.startswith(
                    "Do you possess proven effectiveness") or question_text.startswith(
                    "Are you proficient") or question_text.startswith("Are you amenable") or question_text.startswith(
                    "Did you complete") or question_text.startswith("Are you business savvy")
                      or question_text.startswith("Are you legally authorized to work")
                      or question_text.startswith("Is your degree focused in Engineering or Science") or
                      "meet these requirements" in question_text) or question_text.startswith(
                    "Are you fluent") or question_text.startswith(
                    "This role") or question_text.startswith("Are you skilled") or question_text.startswith(
                    "Are you open to ") or question_text.startswith("Are able to") or question_text.startswith(
                    "Based on your qualification") or question_text.startswith(
                    "Please confirm") or "proficient" in question_text or "flexible" in question_text or question_text.startswith(
                    "Can you travel") or question_text.startswith("Are you knowledgeable") or question_text.startswith(
                    "Have you led projects") or question_text.startswith(
                    "Have you been involved in implementing") or question_text.startswith(
                    "The preferred location") or question_text.startswith(
                    "Have you crafted") or question_text.startswith("Can you work") or question_text.startswith(
                    "Are you ok") or question_text.startswith("Doo you have a B.S. degree") or question_text.startswith(
                    "Can you be on-site") or question_text.startswith("Have you attained") or question_text.startswith(
                    "Will you be available") or question_text.startswith("Do you live") or question_text.startswith(
                    "Do you possess knowledge") or question_text.startswith(
                    "Do you possess a minimum of") or question_text.startswith(
                    "Travel may be required, are you willing to") or question_text.startswith(
                    "Please indicate which of the following skills") or question_text.startswith(
                    "Would you be able") or question_text.startswith("Do you possess") or question_text.startswith(
                    "Is your B.S. or higher education"):
                    a_id = answer_choices[0].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Based on your ability"):
                    a_id = answer_choices[-2].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Education: Please select the option"):
                    a_id = next(filter(
                        lambda x: x.text.startswith(self.user_degree if self.user_degree != 'PhD' else "Doctorate"),
                        answer_choices)).get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif (question_text.startswith("Are you an Abiomed employee") or "European" in question_text or
                      question_text.startswith(
                          " Are you currently or were previously employed by")) or question_text.startswith(
                    "Have you worked with any of the") or question_text.startswith("Ireland"):
                    a_id = answer_choices[-1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Will you now or in the future require sponsorship"):
                    a_id = answer_choices[0 if self.user_visa == 'H1b' else 1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Indicate your command of the English"):
                    a_id = answer_choices[1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Indicate your command of the German") or question_text.startswith(
                        "Indicate your command of the Dutch") or question_text.startswith(
                    "What are your base salary expectations") or question_text.startswith(
                    "Please indicate which of the following best describes your situation") or question_text.startswith(
                    "Please indicate your areas of knowledge (Select ALL that apply).") or question_text.startswith(
                    "Which of the following do you have") or question_text.startswith(
                    "Have you had any type of relationship with a public/government") or question_text.startswith(
                    "Have you ever worked with any of the Johnson & Johnson") or question_text.startswith(
                    "Do you possess Anaplan") or question_text.startswith(
                    "Are you currently or were previously employed by") or question_text.startswith(
                    "Have you worked in a Medical") or question_text.startswith(
                    "1. Do you have any agreement that would restrict"):
                    a_id = answer_choices[-1].get_attribute('id')
                    self.click_ele(wd, f'[id="{a_id}"]')
                elif question_text.startswith("Please select") or question_text.startswith(
                        "From the list below") or question_text.startswith(
                    "Based on your education") or question_text.startswith("What certifications have you attained"):
                    for ans in list(filter(lambda x: not x.find_element(By.XPATH, '..').text.strip().startswith("None"),
                                           answer_choices)):
                        self.click_ele(wd, f'[value="{ans.get_attribute("value")}"]')
                elif question_text.startswith("According to the following scenarios, please select the one"):
                    for ans in list(filter(lambda x: not x.find_element(By.XPATH, '..').text.strip().startswith(
                            "I'm a foreign national, but I'm authorized to work"),
                                           answer_choices)):
                        self.click_ele(wd, f'[value="{ans.get_attribute("value")}"]')

                elif question_text.startswith("What will be your education"):
                    for ans in list(filter(lambda x: not x.find_element(By.XPATH, '..').text.strip().startswith(
                            self.user_degree),
                                           answer_choices)):
                        self.click_ele(wd, f'[value="{ans.get_attribute("value")}"]')
                else:
                    self.logger.error(
                        f"Company: {self.company.title()}, Url: {url}, User: {self.user},"
                        f" Question: {question_text}, Ans: {' '.join([ans.find_element(By.XPATH, '..').text for ans in answer_choices])}")
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'EEO Information')]")))
            for question, ans in zip(wd.find_elements(By.CSS_SELECTOR, '[title="This field is mandatory"]'),
                                     wd.find_elements(By.CSS_SELECTOR, '[class="textindentpanel"]')):
                question_text = question.text
                if "Ethnicity" in question_text:
                    Select(ans.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text("Not Hispanic or Latino")
                elif "Race" in question_text:
                    Select(ans.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text("Asian")
                elif "Gender" in question_text:
                    Select(ans.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text(
                        "Male" if self.user_gender_male else "Female")
                elif "veteran" in question_text:
                    Select(ans.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text("I am not a veteran")
                else:
                    self.logger.error(
                        f"Company: {self.company.title()}, Question: {question_text}, User: {self.user}, URL: {url}")

            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//span[starts-with(text(), 'Voluntary Self-Identification of Disability')]")))
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'eSign your Application')]")))
            wd.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_name)
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        except:
            pass
        try:
            WebDriverWait(wd, 5).until(
                EC.presence_of_element_located((By.XPATH, "//span[starts-with(text(), 'Submit your Resume/CV')]")))
            self.click_ele(wd, '[id="et-ef-content-ftf-submitCmdBottom"]')
        except:
            pass
        try:
            self.expected_shown_element(wd, '[class="titlepage"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Thank You":
                return 1
        except:
            return 0
