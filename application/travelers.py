# -*- coding: utf-8 -*-
# @Time  : 4/21/24 17:01
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: travelers.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class travelersApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you currently employed by Travelers": "No",
                "Are you at least 18 years of age or older": "Yes",
                "Are you legally authorized to work in the country": "Yes",
                "Will you now or in the future require support": self.user_visa_ans,
                "Are you currently subject to any agreements": "No",
                "Have you ever been involuntarily discharged": "No",
                "List the Other College/University": self.user_school,
                "Please indicate your cumulative overall GPA": "4.0"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you have a High School Diploma": "Yes"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            d = {
                "Gender": "Male" if self.user_gender_male else "Female",
                "Please select the race": "Asian",
                "Veteran Status": "not"
            }
            self.question_select(wd, d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
