import datetime
import random
from abc import ABC

from selenium.webdriver import <PERSON><PERSON>hai<PERSON>
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.ui import WebDriverWait

from module.applicationInterfaceModule import applicationInterface as applyInterface


class metaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.expected_shown_element(wd, '[id="careersContentContainer"]')
        apply_url = wd.find_element(By.CSS_SELECTOR, '[id="careersContentContainer"]') \
            .find_element(By.CSS_SELECTOR, 'a[role="button"]').get_attribute("href")
        wd.get(apply_url)
        # link expire
        try:
            self.expected_shown_element(wd, '[id="fileUploader"]')
        except:
            if wd.current_url.startswith("https://www.metacareers.com/jobs/position-not-available/"):
                self.database.delete(f"link = '{url}'")
                return -4
        # Personal Information
        try:
            start_date = wd.find_element(By.CSS_SELECTOR, '[name="availableStartMonth"]')
            start_date.send_keys(datetime.datetime.now().strftime("%m/%Y"))
        except:
            pass
        try:
            work_yes = wd.find_element(By.XPATH, "//*[contains(@class, 'workEligibleUS')]").find_element(
                By.CSS_SELECTOR, '[id="0"]')
            self.click_ele(wd, work_yes)
            sponsor = (wd.find_element(By.XPATH, "//*[contains(@class, 'requiresSponsorship')]")
                       .find_element(By.CSS_SELECTOR, '[id="0"]' if self.user_visa == 'H1b' else '[id="1"]'))
            self.click_ele(wd, sponsor)
        except:
            pass
        resume = wd.find_element(By.CSS_SELECTOR, '[id="fileUploader"]') \
            .find_element(By.CSS_SELECTOR, 'a').get_attribute('class')
        self.click_ele(wd, f'[class="{resume}"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        wd.find_element(By.CSS_SELECTOR, '[name="candidateName"]').send_keys(self.user_name)
        self.plcae_holder(wd, '[placeholder="Location"]', self.user_city + ", " + self.user_state)
        wd.find_element(By.CSS_SELECTOR, '[name="candidateEmail"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[name="candidatePhone"]').send_keys(self.user_phone)
        try:
            no = \
                wd.find_element(By.XPATH, "//*[contains(@class, 'candidateResumeFile')]").find_elements(By.CSS_SELECTOR,
                                                                                                        'div')[-1]
            self.click_ele(wd, no)
        except:
            pass
        self.click_next_button(wd, 'education')
        # Education
        self.expected_shown_element(wd, '[placeholder="College/University Name"]')
        self.plcae_holder(wd, '[placeholder="College/University Name"]', self.user_school)
        Select(wd.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text(
            self.user_degree + "s" if self.user_degree != "PhD" else "PHD")
        self.click_next_button(wd, 'experience')
        # Experience
        self.plcae_holder(wd, '[placeholder="Employer Name"]', self.user_company)
        self.plcae_holder(wd, '[placeholder="Position"]', self.user_title)
        wd.find_element(By.CSS_SELECTOR, '[name="workStartMonths[0]"]').send_keys(self.user_start_time)
        if self.user_end_time == 'Present':
            self.click_ele(wd, '[id="workIsCurrent[0]"]')
        else:
            wd.find_element(By.CSS_SELECTOR, '[name="workEndMonths[0]"]').send_keys(self.user_end_time)
        # wd.find_element(By.CSS_SELECTOR, '[name="workDescriptions[0]"]').send_keys(self.user_role_desc)
        self.click_next_button(wd, 'voluntary')
        # voluntary
        self.wait_x_sec(4)
        self.indicate_option(wd, 'Indicate your gender:', 'Male' if self.user_gender_male else "Female")
        self.indicate_option(wd, 'Indicate your race/ethnicity:', 'Asian')
        try:
            self.indicate_option(wd, 'Indicate your Protected Veteran Status:', 'I am not a protected veteran')
        except:
            self.indicate_option(wd, 'Indicate your protected veteran status:', 'I am not a protected veteran')
        self.indicate_option(wd, 'Please select one of the choices below:',
                             'No, I do not have a disability and have not had one in the past')
        self.click_next_button(wd, 'review')
        self.expected_shown_element(wd, '[id="appSubmitButton"]')
        self.click_ele(wd, '[id="appSubmitButton"]')
        try:
            WebDriverWait(wd, 50).until(
                EC.presence_of_element_located((By.XPATH, f"//*[starts-with(text(), 'Thanks for applying')]")))
            return 1
        except:
            return 0

    def plcae_holder(self, wd, place_holder, ans):
        self.expected_shown_element(wd, place_holder)
        ele = wd.find_element(By.CSS_SELECTOR, place_holder).find_element(
            By.CSS_SELECTOR, 'input[type="text"]')
        id_ele = ele.find_element(By.XPATH, '..').get_attribute("id")
        s = f'[id="{id_ele}"]'
        element = wd.find_element(By.CSS_SELECTOR, s)
        wd.execute_script("arguments[0].scrollIntoView({ behavior: 'smooth', block: 'center' });", element)
        ele.send_keys(ans)
        self.wait_x_sec(random.uniform(0.3, 1.0))
        try:
            self.expected_shown_element(wd, f'[title="{ans}"]')
            self.click_ele(wd, f'[title="{ans}"]')
        except:
            ActionChains(wd).move_to_element(ele).move_by_offset(0, 35).double_click().perform()

    def click_next_button(self, wd, identifier):
        next_button = list(
            filter(
                lambda x: x.get_attribute('href').startswith(f"https://www.metacareers.com/resume/?page={identifier}"),
                wd.find_elements(By.CSS_SELECTOR, 'a')))[0]
        for button in wd.find_elements(By.CSS_SELECTOR, f'[class="{next_button.get_attribute("class")}"]'):
            if button.text == "Next":
                ActionChains(wd).move_to_element(button).click().perform()
                break

    def indicate_option(self, wd, option, value):
        eles = wd.find_element(By.XPATH,
                               f"//*[text()='{option}']").find_element(By.XPATH,
                                                                       './following-sibling::div')
        ele = list(filter(lambda x: x.text == value, eles.find_elements(By.CSS_SELECTOR, 'div')))[0].find_element(
            By.CSS_SELECTOR, 'input')
        self.click_ele(wd, ele)
