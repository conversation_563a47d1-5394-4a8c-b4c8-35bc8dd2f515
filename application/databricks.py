from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class databricksApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        if "embed" not in url:
            token = url.split("=")[-1]
            url = f"https://boards.greenhouse.io/embed/job_app?for=databricks&token={token}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Do you currently or have you previously worked": "No"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
