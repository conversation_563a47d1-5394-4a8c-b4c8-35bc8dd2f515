from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class pelotonApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideHeadAndFoot(wd)
        try:
            self.expected_shown_element(wd, '[id="results"]')
            self.database.delete(f"link='{url}'")
            return -4
        except:
            pass
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you legally authorized to work": "Yes",
            "Do you now or could you in the future require Peloton to sponsor": self.user_visa_ans,
            "This is a hybrid role": "Yes",
            "Have you previously been employed by": 'No'
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
