from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class cvsApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "To ensure the safety of our customers": "Yes",
                "Are you a current employee of CVS/Pharmacy": "No",
                "Have you ever been employed by CVS Health": "No",
                "I authorize CVS Health": "No",
                "Do you now, or will you in the future, need immigration sponsorship": self.user_visa_ans,
                "If hired, can you provide proof that you are legally able to work in the United States?": "Yes",
                "What is your base pay expectation for this position?": "$100,000 - $120,000",
                "CVS Health participates in a Federal tax credit program for employers known as the Work Opportunity Tax Credit (WOTC).": "No",
                "Are you currently, have you ever been": "No",
                "Are you at least 18": "Yes",
                "As a government contractor, CVS Health must abide by all laws": "No",
                "What is your hourly pay expectation": "$30+",
                "Are you interested in a full time": "Full Time",
                "Are you currently a Pharmacist": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Application question 1 of 2
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you currently hold an active clinical license?": "No",
                "Is your license limited or restricted?": "Yes",
                "Have you ever had, or do you anticipate receiving": "No",
                "What is your base pay": "$100,000 - $120,000",
                "In what states or providences do you currently hold an active clinical": "N/A",
                "Are you actively licensed": "Yes"
            }
            self.question_select(wd, question_dict)
            try:
                wd.find_element(By.CSS_SELECTOR, 'textarea').send_keys("N/A")
            except:
                pass
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            d = {"Please select your gender": "Male" if self.user_gender_male else "Female",
                 "Please select the ethnicity": "Asian",
                 "Please select the veteran status": "I am not a veteran",
                 "Are you Hispanic or Latino": "No"}
            self.question_select(wd, d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
