from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class autodeskApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you eligible to work in the country in which this position is located?": "Yes",
                "Will you now or in the future require sponsorship for employment visa status?": self.user_visa_ans,
                "I would like to be part of the Autodesk, Inc. Talent Community, and receive information about opportunities with Autodesk": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Application question 2 of 2
        self.wait_x_sec(6)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(6)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_dict = {
                "Please identify your veteran status": "I AM NOT A VETERAN",
                "Hispanic or Latino?": "No",
                "Please select your ethnicity.": "Asian (Not Hispanic or Latino) (United States of America)",
                "Please select your gender": "Male" if self.user_gender_male else "Female"
            }
            self.question_select(wd, v_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
