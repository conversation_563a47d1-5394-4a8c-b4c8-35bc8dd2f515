from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class affirmApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Please indicate your preferred": "San Francisco",
            "Which U.S. State or Canadian Province do you reside in": self.user_state,
            'Pronouns': "He/him/his" if self.user_gender_male else "She/her/hers",
            "How did you first learn about Affirm": "LinkedIn",
            "Do you have experience modeling": "Yes",
            "What is your experience": "N/A",
            "This role requires someone to work remotely from Canada. Which U.S. State or Canadian Province do you reside in": self.user_state,
            "Have you used SQL": "Yes",
            "Have you previously been employed": "I have not"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
