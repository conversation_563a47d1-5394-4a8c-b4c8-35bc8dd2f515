from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class discoverApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="col-md-12"]').text.strip().startswith("Page Not Found"):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Was this job referred to you": "No",
                "Will you now or in the future require visa sponsorship": self.user_visa_ans,
                "After reviewing the internal movement eligibility": "Yes",
                "Have you been known by any other names": "No",
                "Have you worked for": "No",
                "Do you currently work for Deloitte": "No",
                "Are you, or an immediate member of your family": "No",
                "Was this job referred to you by anyone considered ": "No",
                "Are you eligible to work": "Yes",
                "Are you currently at least 18 years of age or older?": "Yes",
                "Do you have any relatives currently": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict_2_2 = {
                "Do you have a GPA": "Yes",
                "Will you now or in the future require visa": "Yes"
            }
            self.question_select(wd, question_dict_2_2)
            self.click_next_page(wd)
        # Voluntary disclosure "Voluntary Disclosures"
        self.wait_x_sec(3)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        # self identify
        self.self_identify(wd)
        # task assessment
        self.wait_x_sec(3)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith("Take Assessment"):
            self.click_ele(wd, '[data-automation-id="inlineAssessmentButton"]')
            window_before = wd.window_handles[0]
            window_after = wd.window_handles[1]
            wd.switch_to.window(window_after)
            try:
                self.expected_shown_element(wd, '[id="ctl00_ContentHolder_btnRedirectWorkDay"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_btnRedirectWorkDay"]')
            except:
                self.click_ele(wd, '[id="ctl00_ContentHolder_btnTakeSurvey"]')
                self.expected_shown_element(wd, '[name="ctl00$ContentHolder$txtZipCode"]')
                wd.find_element(By.CSS_SELECTOR, '[name="ctl00$ContentHolder$txtZipCode"]').send_keys(self.user_zipcode)
                self.click_ele(wd, '[value="Continue"]')
                self.expected_shown_element(wd, '[value="REHIREN"]')
                self.click_ele(wd, '[value="REHIREN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCUNEMPLOYED27N"]')
                self.click_ele(wd, '[value="WOTCUNEMPLOYED27N"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCTANF2YRN"]')
                self.click_ele(wd, '[value="WOTCTANF2YRN"]')
                self.click_ele(wd, '[value="WOTCTANFN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCFOODSTAMPSNAPN"]')
                self.click_ele(wd, '[value="WOTCFOODSTAMPSNAPN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPSNAPFAMILYN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPFAMILYN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCVETERANN"]')
                self.click_ele(wd, '[value="WOTCVETERANN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCVOCREHABN"]')
                self.click_ele(wd, '[value="WOTCVOCREHABN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCSSIN"]')
                self.click_ele(wd, '[value="WOTCSSIN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.wait_x_sec(2)
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
            wd.switch_to.window(window_before)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
