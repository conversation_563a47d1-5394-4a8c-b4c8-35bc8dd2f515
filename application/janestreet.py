# -*- coding: utf-8 -*-
# @Time  : 11/19/23 17:41
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: janestreet.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class janestreetApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideHeadAndFoot(wd)
        self.expected_shown_element(wd, '[class="primary-button apply-button"]')
        self.click_ele(wd, '[class="primary-button apply-button"]')
        self.expected_shown_element(wd, '[id="first_name"]')
        self.hide.hideHeadAndFoot(wd)
        self.hide.hideElement(wd, '[id="cookie-consent-banner"]')
        wd.find_element(By.CSS_SELECTOR, '[id="first_name"]').send_keys(self.user_name.split()[0])
        wd.find_element(By.CSS_SELECTOR, '[id="last_name"]').send_keys(self.user_name.split()[0])
        wd.find_element(By.CSS_SELECTOR, '[id="email"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="phone"]').send_keys(self.user_phone)
        self.click_ele(wd, '[class="nested-dropdown  application-source required "]')
        self.wait_x_sec(1)
        job_board = list(filter(lambda x: x.text.startswith("Online job board"),
                                wd.find_elements(By.CSS_SELECTOR, '[class="dropdown-category-container"]')))[0]
        ActionChains(wd).move_to_element(job_board).click().perform()
        self.click_ele(wd, '[data-dropdown-selection="Bright Network"]')
        self.click_ele(wd, '[for="interviewed-false"]')
        self.click_ele(wd, '[for="student-false"]')
        wd.find_element(By.CSS_SELECTOR, '[id="current_position"]').send_keys(self.user_company)
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.click_ele(wd, '[type="submit"]')
        try:
            self.expected_shown_element(wd, '[id="thanks-modal"]')
            return 1
        except:
            return 0
