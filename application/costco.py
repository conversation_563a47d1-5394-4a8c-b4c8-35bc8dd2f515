from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait

from module.applicationInterfaceModule import applicationInterface as applyInterface


class costcoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url + '&email=' + self.user_account)
        try:
            self.expected_shown_element(wd, '[name="cwsPassword"]')
        except:
            if wd.find_element(By.CSS_SELECTOR,
                               'body').text == "This job is no longer available. Please search our current job openings.":
                self.database.delete(f"link = 'f{url}'")
                return -4
        # Login
        try:
            wd.find_element(By.CSS_SELECTOR, '[name="cwsPassword"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[class="btn btn-primary oracletaleocwsv2-btn-block-mobile"]')
        except:
            try:
                self.expected_shown_element(wd, '[id="cwsPassword_2"]')
                return -1
            except:
                pass
        try:
            applied = wd.find_element(By.CSS_SELECTOR, '[class="oracletaleocwsv2-accordion-group-head"]')
            if applied.text == "Positions to which you have applied:":
                return 1
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR,
                               '[role="alert"]').text == "Email address and/or password you've specified do not match our records.":
                return -1
        except:
            pass
        # 1. Your Information
        self.expected_shown_element(wd,
                                    '[class="btn btn-primary btn-lg oracletaleocwsv2-btn-arrow-nav oracletaleocwsv2-arrow-nav-next"]')
        self.click_ele(wd,
                       '[class="btn btn-primary btn-lg oracletaleocwsv2-btn-arrow-nav oracletaleocwsv2-arrow-nav-next"]')
        # 2. Resume and Questions
        self.expected_shown_element(wd, '[class="oracletaleocwsv2-required form-label"]')
        self.expected_shown_element(wd, '[class="oracletaleocwsv2-head-title"]')
        self.click_ele(wd, '[class="oracletaleocwsv2-head-title"]')
        self.click_ele(wd, '[title="Are you sure you want to remove this?"]')
        WebDriverWait(wd, 10).until(EC.alert_is_present())
        wd.switch_to.alert.accept()
        self.click_ele(wd, '[aria-label="Add Resume"]')
        self.click_ele(wd, '[name="uploadButtonResume"]')
        self.click_ele(wd,
                       '[class="btn oracletaleocwsv2-custom-file-upload uploadBtn oracletaleocwsv2-btn-fa fa-folder-open oracletaleocwsv2-file-trigger"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.click_ele(wd, '[aria-label="Save"]')
        for question_wrap in list(filter(lambda x: x.text, wd.find_elements(By.CSS_SELECTOR,
                                                                            '[class="col-xs-12"]'))):
            try:
                question = question_wrap.find_element(By.CSS_SELECTOR, '[class="oracletaleocwsv2-required form-label"]')
            except:
                continue
            question_text = question.text
            if question_text.startswith("Salary"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.text: answer.send_keys("140000")
            elif question_text.startswith("Availability"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.text: answer.send_keys("ASAP")
            elif question_text.startswith("Willingness to work/relocate") or question_text.startswith(
                    "Are you willing to work") or question_text.startswith(
                "Are you authorized to work lawfully") or question_text.startswith(
                "Our Hybrid Remote Work program will") or "Do you have" in question_text:
                try:
                    try:
                        answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                    except:
                        answer = question_wrap.find_element(By.CSS_SELECTOR, 'select')
                        Select(answer).select_by_visible_text("Yes")
                        continue
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.get_attribute('value'): answer.send_keys("Yes")
            elif question_text.startswith(
                    "Will you now, or in the future, require"):
                answer = question_wrap.find_element(By.CSS_SELECTOR, 'select')
                Select(answer).select_by_visible_text(self.user_visa_ans)
            elif "What is your work authorization" in question_text:
                visa = question_wrap.find_element(By.CSS_SELECTOR, """input[type="text"]""")
                if visa.get_attribute("value"): continue
                self.click_ele(wd,
                               f'[id="{visa.get_attribute("id")}"]')
                ans = list(filter(lambda x: x.text.strip() == "H-1B" if self.user_visa == "H1b" else "Green Card",
                                  wd.find_elements(By.CSS_SELECTOR, '[class="oracletaleocwsv2-checkbox-wrapper"]')))[0]
                ans.click()
                self.click_ele(wd, '[class="btn btn-primary btn-lg oracletaleocwsv2-close-popup-trigger"]')
            elif "years" in question_text:
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.text: answer.send_keys("3 YOE")
            elif question_text.startswith("Please rate your expertise"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.text: answer.send_keys("Proficient")
            elif question_text.startswith("Please provide your work authorization"):
                answer = question_wrap.find_element(By.CSS_SELECTOR, 'select')
                if answer.get_attribute('value') != -1:
                    Select(answer).select_by_visible_text("H-1B" if self.user_visa == 'H1b' else 'Green Card')
            elif question_text.startswith("Rate"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.get_attribute('value'): answer.send_keys("8")
            elif question_text.startswith("Detail"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.get_attribute('value'): answer.send_keys("N/A")
            elif question_text.startswith("What does Full Stack Engineer mean to you"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                if not answer.get_attribute('value'): answer.send_keys(
                    "A full-stack web developer is a person who can develop both client and server software.")
            elif question_text.startswith(
                    "What is the largest scale app that you've worked on in terms of total transactions") or question_text.startswith(
                "What specific tools") or "Please provide" in question_text or "Tell us about" in question_text or "please provide" in question_text or question_text.startswith(
                "What was") or question_text.startswith("How have you handled"):
                try:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'textarea')
                except:
                    answer = question_wrap.find_element(By.CSS_SELECTOR, 'input')
                answer.send_keys("N/A")
            else:
                self.logger.error(f"Company: {self.company}, Question: {question_text}")
        self.wait_x_sec(3)
        self.click_ele(wd, '[aria-label="next"]')
        # 3. Review and Submit
        self.wait_x_sec(3)
        self.click_ele(wd, '[class="btn btn-primary btn-lg"]')
        # Voluntary Equal Opportunity Questionnaire
        self.wait_x_sec(3)
        self.click_ele(wd, '[class="btn btn-primary btn-lg oracletaleocwsv2-btn-block-mobile"]')
        try:
            self.expected_shown_element(wd, '[aria-label="Application Complete"]')
            return 1
        except:
            return 0
