from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class mongodbApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split("=")[-1]
        url = f"https://job-boards.greenhouse.io/embed/job_app?for=mongodb&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you ever worked at MongoDB before": "No",
            "What vertical is your experience": "Financial Services",
            "Are you located in NYC or willing to relocate": "Yes",
            "Are you able to work": "Yes",
            "Are you eligible to work": "Yes",
            "Do you have experience": "Yes",
            "Where do you live": "United States",
            "This position": "Yes, I confirmed.",
            "Internal Mobility Policy Acknowledgement": "No",
            "Please select one of the following": "I am not a Mexican National"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
