import random
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class coinbaseApply(applyInterface, ABC):
    def generate_explanation(self):
        # Definitions
        definitions = [
            "Fact tables are all about the numbers; they store measurable data like sales figures or quantities.",
            "Dimension tables give context to those numbers, containing descriptive information like product names or customer details.",
        ]

        # Characteristics of Fact Tables
        fact_characteristics = [
            "They usually have foreign keys linking to dimension tables.",
            "Think of them as the heavyweights, often filled with lots of transaction data.",
            "You'll find metrics that are crucial for analysis, like revenue and counts."
        ]

        # Characteristics of Dimension Tables
        dimension_characteristics = [
            "These tables are like the backstory; they include attributes that explain the facts.",
            "They're generally smaller and help categorize and filter the data.",
            "Dimension tables often contain rich details, such as demographics or product categories."
        ]

        # Randomly selecting elements
        fact_definition = random.choice(definitions)
        fact_char = random.sample(fact_characteristics, 2)
        dimension_definition = random.choice(definitions)
        dimension_char = random.sample(dimension_characteristics, 2)

        # Constructing the explanation
        explanation = (
            f"{fact_definition} {', '.join(fact_char)}\n"
            f"{dimension_definition} {', '.join(dimension_char)}\n"
        )

        return explanation

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "What is the difference between fact and dimension": self.generate_explanation(),
            "Do you have 4-5 years": "Yes",
            "Do you have 3-4 years": "Yes",
            "How many years of experience": "3 YOE",
            "Have you ever used any Coinbase products": "Yes",
            "Have you previously been employed": "No",
            "Please confirm receipt": "Confirmed",
            "Linkedin Profile URL": self.user_linkedin,
            "Do you currently have an active CPA license": "No",
            "What are the key trends": "N/A",
            "Do you have": "Yes",
            "Which of the following languages do you use": "Python",
            "Which of the following data warehousing": "On prem solutions",
            "Which of these platforms": "AWS",
            "How often do you contribute code": "Daily",
            "Have you worked on": "Yes",
            "Have you managed an enterprise": "Yes",
            "I understand that Coinbase may use AI tools to assist in the application": "Yes",
            "Are you interested in interviewing for a frontend or backend opportunity": "Backend",
            "Are you a current government official": "No",
            "Are you a close relative of a government": "No",
            "Most roles at Coinbase are remote but require an employee to be based in the country where the role is posted. What’s the zip code": self.user_zipcode
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        customized_search_str = self.company.title() + " Careers Page"
        return self.greenhouse_submit(wd, customized_search_str)
