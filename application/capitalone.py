from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class capitaloneApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        # Application Questions
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you eligible to work in the United States?": "Yes",
                "Will you, now or in the future, require": self.user_visa_ans,
                "Are you age 18 or over?": "Yes",
                "Are you able to perform the essential functions of the job, with or without reasonable accommodations?": "Yes",
                "Do you meet all of the Basic Qualifications for this role as specified in the job description?": "Yes",
                "Do you meet the Preferred Qualifications for this role as specified in the job description?": "Yes, All",
                "Have you previously worked at Capital One or a company acquired by Capital One?": "No",
                "Do you have any relatives employed by Capital One?": "No",
                "Do you have any close personal friends employed by Capital One with whom your relationship could create a real or perceived conflict of interest?": "No",
                "Are you currently, or have you ever been, on assignment with Capital One as a contractor, consultant or temp?": "No",
                "Will you be engaged or involved in any other business or employment other than with Capital One?": "No",
                "Do you currently work for": "No",
                "Are you currently under any form of agreement or policy,": "No",
                "Do you have any close or intimate personal relationships": "No",
                "Do you have a second job": "No",
                "Do you have at least": "Yes",
                "Are you eligible to work in the Philippines": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Voluntary Disclosures
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.hide.hideElement(wd, '[data-automation-widget="externalHeader"]')
            v_dict = {
                "Are you a protected Veteran?": "No",
                "Gender": "Male" if self.user_gender_male else "Female",
                "Race/Ethnicity": "Asian (United States of America)"
            }
            self.question_select(wd, v_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
