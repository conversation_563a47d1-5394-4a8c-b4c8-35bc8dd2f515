from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class geicoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question 1 of 2
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you 18 years or older?": "Yes",
                "Do you have a high school diploma or equivalent?": "Yes",
                "Have you ever been convicted, pleaded guilty or no contest": "No",
                "Do you currently hold or have you ever held a professional state issued license in the legal": "No",
                "Any offer of employment made by GEICO Companies (the Companies) is contingent upon": "I have read and acknowledge",
                "Were you referred by a GEICO associate or recruiter?": "No",
                "Have you worked previously as a GEICO associate?": "No",
                "Do you have authorization to work in the United States?": "Yes",
                "Do you currently hold or have you ever held a professional state issued insurance license?": "No",
                "Are you a current or former contractor of GEICO?": "No",
                "Will you now or in the future require sponsorship for employment visa status?": self.user_visa_ans,
                "Please select all days": "Friday",
                "Please select all shifts": "Mid Day",
                "List your reasons for leaving your last": "Seeking career growth",
                "What is your desired salary": "100K - 120K",
                "What month/year": "ASAP"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
