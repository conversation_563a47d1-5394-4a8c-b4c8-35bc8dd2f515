from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class hpeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question 1 of 2
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "I have United States government": "No",
                "For any of the countries listed below": "Yes",
                "HPE Policy prohibits employees from engaging": "No",
                "Have you signed or accepted any agreement": "No",
                "Certain Legal and ethical restrictions": "No",
                "Do you possess a valid work permit": "Yes",
                "Do you possess a valid residency permit": "No" if self.user_visa == 'H1b' else "Yes",
                "For those locations or countries in which you are seeking": "United States"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            self.question_select(wd, {"Certain HPE roles require in-person": "Yes"})
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Gender": "Male" if self.user_gender_male else "Female",
                "Hispanic or Latino": "No",
                "Ethnicity": "Asian",
                "Veterans Status": "I AM NOT"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
