from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class stripeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split('/')[-1]
        url = f"https://job-boards.greenhouse.io/embed/job_app?for=stripe&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Who is your current employer?": self.user_company,
            "Please select the country where you currently reside.": "US",
            "Where do you plan to work from": "Willing to relocate - I am not in any one of <PERSON>e's offices, but I am willing to relocate and work in one of the options for a Stripe office",
            "Please select the country": "US",
            "Please select the location you anticipate working in": "US",
            "Are you authorized to work": "Yes",
            "Will you require <PERSON><PERSON> to sponsor": self.user_visa_ans,
            "Have you ever been employed by <PERSON>e": "No",
            "Do you opt-in to receive": "No",
            "If this role offers the option to work from a remote location, do you plan to work remotely?": "Yes, I intend to work remotely.",
            "What city and state": ", ".join([self.user_city, self.user_state]),
            "Which state": self.user_state,
            "Where are you currently located": ", ".join([self.user_city, self.user_state]),
            "If you do not currently reside in": "Yes",
            "What is your current or previous job title?": self.user_title,
            "Who is your current or previous employer": self.user_company,
            "What is the most recent degree": self.user_degree,
            "What is the most recent school": self.user_school,
            "If located in the US": ", ".join([self.user_city, self.user_state]),
            "How many years of experience": "1",
            "This role is based in": "Yes",
            "Where are you currently based": ", ".join([self.user_city, self.user_state]),
            "This role is based out": "Ues"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
