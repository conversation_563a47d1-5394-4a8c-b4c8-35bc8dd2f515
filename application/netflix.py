import random
from abc import ABC

from selenium.webdriver import <PERSON><PERSON>hains
from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class netflixApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="heading"]').text.startswith(
                    "The job you are looking for may have closed"):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        self.hide.hideElement(wd, '[id="onetrust-consent-sdk"]')
        self.hide.hideHeadAndFoot(wd)
        self.expected_shown_element(wd, '[data-test-id="apply-button"]')
        self.click_ele(wd, '[data-test-id="apply-button"]')
        self.expected_shown_element(wd, '[aria-label="Select file - Upload resume"]')
        self.click_ele(wd, '[aria-label="Select file - Upload resume"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.expected_shown_element(wd, '[data-test-id="confirm-upload-resume"]')
        self.click_ele(wd, '[data-test-id="confirm-upload-resume"]')
        self.wait_x_sec(10)
        # basic information
        email = wd.find_element(By.CSS_SELECTOR, '[id="Contact_Information_email"]')
        if email.get_attribute("value") != self.user_account:
            for _ in range(4):
                email.clear()
            email.send_keys(self.user_account)
        first_name = wd.find_element(By.CSS_SELECTOR, '[id="Contact_Information_firstname"]')
        if first_name.get_attribute("value") != self.user_name.split()[0]:
            for _ in range(4):
                first_name.clear()
            first_name.send_keys(self.user_name.split()[0])
        last_name = wd.find_element(By.CSS_SELECTOR, '[id="Contact_Information_lastname"]')
        if last_name.get_attribute("value") != self.user_name.split()[1]:
            for _ in range(4):
                last_name.clear()
            last_name.send_keys(self.user_name.split()[1])
        country_code = wd.find_element(By.CSS_SELECTOR, '[aria-label="Country code"]')
        if "United States of America" not in country_code.get_attribute("value"):
            self.click_ele(wd, country_code)
            control_id = country_code.get_attribute("aria-controls")
            self.expected_shown_element(wd, f'[id="{control_id}"]')
            self.click_ele(wd, f'[id="🇺🇸 (+1) United States of America-236"]')
        phone = wd.find_element(By.CSS_SELECTOR, '[id="Contact_Information_phone"]')
        if phone.get_attribute("value") != self.user_phone:
            for _ in range(4):
                phone.clear()
            phone.send_keys(self.user_phone)
        # location information
        country = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Contact_Information_country_label"]')
        if country.get_attribute("value") != "United States":
            self.click_ele(wd, country)
            control_id = country.get_attribute("aria-controls")
            self.expected_shown_element(wd, f'[id="{control_id}"]')
            self.click_ele(wd, f'[title="United States"]')
        state = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Contact_Information_state_label"]')
        if state.get_attribute("value") != self.user_state:
            self.click_ele(wd, state)
            control_id = state.get_attribute("aria-controls")
            self.expected_shown_element(wd, f'[id="{control_id}"]')
            self.click_ele(wd, f'[title="{self.user_state}"]')
        city = wd.find_element(By.CSS_SELECTOR, '[id="Contact_Information_city"]')
        if city.get_attribute("value") != self.user_city:
            for _ in range(4):
                city.clear()
            city.send_keys(self.user_city)
        # self-ID question
        disability = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Self_ID_Questions_US_disability_label"]')
        self.click_ele(wd, '[aria-labelledby="Self_ID_Questions_US_disability_label"]')
        area_control_id = disability.get_attribute("aria-controls")
        self.expected_shown_element(wd, f'[id="{area_control_id}"]')
        self.click_ele(wd, f'[id="No-1"]')
        trans = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Self_ID_Questions_US_transgender_label"]')
        self.click_ele(wd, '[aria-labelledby="Self_ID_Questions_US_transgender_label"]')
        area_control_id = trans.get_attribute("aria-controls")
        self.expected_shown_element(wd, f'[id="{area_control_id}"]')
        self.click_ele(wd, f'[id="No-1"]')
        if self.user_gender_male:
            self.click_ele(wd, '[id="Self_ID_Questions_US_genderIdentity-Man"]')
        else:
            self.click_ele(wd, '[id="Self_ID_Questions_US_genderIdentity-Woman"]')
        self.click_ele(wd, '[id="Self_ID_Questions_US_sexualOrientation-Heterosexual"]')
        self.click_ele(wd, '[id="Self_ID_Questions_US_raceEthnicity-I choose not to disclose"]')
        veteran = wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="Self_ID_Questions_US_military_label"]')
        self.click_ele(wd, '[aria-labelledby="Self_ID_Questions_US_military_label"]')
        area_control_id = veteran.get_attribute("aria-controls")
        self.expected_shown_element(wd, f'[id="{area_control_id}"]')
        self.click_ele(wd, f'[title="I choose not to disclose"]')
        # application question
        try:
            question_list = wd.find_element(By.CSS_SELECTOR,
                                            '[data-form-section-id="Application Questions"]').find_element(
                By.XPATH, 'div').find_elements(By.XPATH, './div')
        except:
            question_list = []
        for question in question_list:
            question_text = question.find_element(By.CSS_SELECTOR, 'label').text
            span_css_selector = f'label[id="{question.find_element(By.CSS_SELECTOR, "label").get_attribute("id")}"] span'
            script_exists = f"return window.getComputedStyle(document.querySelector('{span_css_selector}'), '::after').content !== 'none';"
            is_present = wd.execute_script(script_exists)
            if not is_present:
                continue
            p_id = question.find_element(By.CSS_SELECTOR, 'label').get_attribute("id")
            answer_area = question.find_element(By.CSS_SELECTOR, f'input[aria-labelledby="{p_id}"]')
            area_control_id = answer_area.get_attribute("aria-controls")
            if question_text.startswith("Are you currently working for Netflix") or question_text.startswith(
                    "Have you worked for Netflix"):
                self.click_ele(wd, f'[aria-labelledby="{p_id}"]')
                self.expected_shown_element(wd, f'[id="{area_control_id}"]')
                self.click_ele(wd, f'[id="No-1"]')
            elif question_text.startswith("Do you require sponsorship"):
                self.click_ele(wd, f'[aria-labelledby="{p_id}"]')
                self.expected_shown_element(wd, f'[id="{area_control_id}"]')
                if self.user_visa != 'H1b':
                    self.click_ele(wd, f'[id="No-1"]')
                else:
                    self.click_ele(wd, f'[id="Yes-0"]')
            else:
                self.logger.error("Company%s, Question%s", self.company, question_text)
        # submit
        self.click_ele(wd, '[data-test-id="submitApplicationButton"]')
        try:
            self.expected_shown_element(wd,
                                        "//div[@role='alert' and contains(., 'You have already applied to this position.')]")
            error_msg = wd.find_element(By.XPATH,
                                        "//div[@role='alert' and contains(., 'You have already applied to this position.')]").text
            if "You have already applied to this position" in error_msg:
                return 1
            elif "try again" in error_msg:
                self.wait_x_sec(random.randint(1, 3))
                random_offset = random.choice([-2, 2])
                actions = ActionChains(wd)
                check_input = wd.find_element(By.CSS_SELECTOR, '[data-test-id="submitApplicationButton"]')
                actions.move_to_element_with_offset(check_input, random_offset, 0).click().perform()
                self.click_ele(wd, '[data-test-id="submitApplicationButton"]')
        except:
            pass
        try:
            self.expected_shown_element(wd, 'h2[role="heading"]')
            apply_text = wd.find_element(By.CSS_SELECTOR, "h2").text
            if apply_text.startswith("Thank you for your interest in Netflix") or apply_text.startswith(
                    "It looks like you've"):
                return 1
        except:
            return 0
