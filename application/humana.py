from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class humanaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally authorized to work": "Yes",
                "Will you now or in the future require": self.user_visa_ans,
                "Our Department of Defense": "No" if self.user_visa == 'H1b' else "Yes",
                "If offered a position": "Yes",
                "If offered the job you may need to provide a copy of a Birth Certificate": "Yes",
                "Do you have dual citizenship": "No",
                "Have you ever worked for Humana Inc": "No",
                "Does your lawful employment now or in the future": self.user_visa_ans,
                "Do you reside in the location": "No",
                "Do you have": "Yes",
                "Have you ever been employed by": "No",
                "If no, would you be open to relocation": "Yes",
                "If yes, please explain": self.user_visa,
                "What are your salary expectations": "120K",
                "Do you have a completed Bachelor's degree": "Yes",
                "Are you proficient": "Yes",
                "Have you worked with": "Yes",
                "Based on your prior experience and career interests": "N/A"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you have": "Yes",
                "Are you proficient": "Yes",
                "Do you possess": "Yes",
                "Are you skilled in": "Yes",
                "Can you explain": "N/A",
                "In what city, state and zip code": ", ".join([self.user_city, self.user_state, self.user_zipcode]),
                "The position requires": 'Yes',
                "How many years": ["3", "0-5"],
                "What makes you": "N/A",
                "Have you been involved in systems analysis": "Yes",
                "Based on your experience": "N/A",
                "Describe your experience in compiling": "N/A",
                "Have you worked with Microsoft Azure": "Yes",
                "Have you had at least": "Yes",
                "Can you describe": "N/A",
                "Do you have previous experience": "Yes",
                "Are you proficient in": "Yes",
                "Are you experienced with": "Yes- Python",
                "Based on your prior experience": "N/A",
                "In which IAM platform do you have experience": "AWS",
                "Can you confirm": "Yes",
                "Which procurement systems": "Salesforce",
                "Do you meet": "Yes",
                "On a scale of 1 to 5": "4",
                "What state do you live in": self.user_state
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 3'):
            question_dict = {
                "Do you have": "Yes",
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Please select your Veterans Status": "I am not a veteran",
                "Please select the ethnicity": "Asian",
                "Do you identify as": "I am not a veteran"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
