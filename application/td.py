from abc import ABC
from datetime import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class tdApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "A requirement of this position is to have": "Yes",
                "Are you legally": "Yes",
                "Regardless of your remote": "Yes",
                "Do you currently": "No",
                "Accounting": "No",
                "Have you been employed by": "No",
                "Do you, or anyone you have": "No",
                "Are you currently authorized to work": "Yes",
                "Will you now, or in the future": self.user_visa_ans,
                "During the past": "No",
                "Employment Preferences": "Full"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you currently work for TD": "No",
                "What is your anticipated/ most recent graduation month": "Jan",
                "What is your anticipated/most recent graduation year": str(datetime.now().year - 2),
                "A requirement of this position is to be enrolled in full time bachelors degree": "Yes",
                "What is your current year": "5",
                "Will you be returning to school": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Sex": "Male" if self.user_gender_male else "Female",
                "Race": "Asian",
                "Veteran": "I AM NOT"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
