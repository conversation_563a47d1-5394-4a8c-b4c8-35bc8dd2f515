from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class tmobileApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My Information
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application Questions
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "In order to be employed at T-Mobile, you must be at least 18": 'Yes',
                "Are you legally authorized to work in the United States? select one required": 'Yes',
                "Do you now or will you in the future require sponsorship": 'Yes' if self.user_visa else 'No',
                "Are you currently employed ": 'No',
                "In addition to our obligations ": 'No',
                "Has your spouse or domestic partner ever served": 'No',
                "Do you have any relatives employed": 'No',
                "Have you been employed": 'No',
                "Have you previously": 'No',
                "Are you interested in relocating": "Anywhere"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Voluntary Disclosures
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Please identify your Veteran": "I AM NOT",
                "Please select your Gender": "Male" if self.user_gender_male else "Female",
                "Please select the Race": "Asian"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # Submit
        self.submit(wd)
        return self.apply_success(wd)
