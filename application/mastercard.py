from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class mastercardApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Gender": "Male" if self.user_gender_male else "Female",
                "Do you now, or will you in the future, require sponsorship": self.user_visa_ans,
                "Are you NOW legally authorized to work in the country": "Yes",
                "In your current role, do you engage with Mastercard": "No",
                "Are you related to anyone who has the authority": "No",
                "Are you or were you recently (in the last six months)": "No",
                "Are you related to anyone who is an employee of a government": "No",
                "Have you entered into any restrictive covenant": "No",
                "Have you ever worked for Mastercard": "No",
                "Do you have a close personal relationship with a Mastercard employee": "No",
                "Are you currently engaged in any outside activities or employment": "No",
                "Desired Salary": "120K"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_dict = {
                "In your current role, do you engage with Mastercard": "No",
                "Are you related to anyone": "No",
                "Are you or were you recently": "No",
            }
            self.question_select(wd, q_2_dict)
            self.click_next_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Voluntary Disclosures'):
                disclosures_dict = {
                    "Have you ever served in the military": "I AM NOT A VETERAN",
                    "What is your Gender": "Male" if self.user_gender_male else "Female",
                    "What is your ethnicity": "Asian"
                }
                self.question_select(wd, disclosures_dict)
                self.click_agreement(wd)
                self.click_next_page(wd)
        except:
            pass
        self.submit(wd)
        return self.apply_success(wd)
