# -*- coding: utf-8 -*-
# @Time  : 7/10/23 22:30
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: paypal.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class paypalApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        except:
            pass
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "I am related to or associated with a Politically Exposed Person (PEP). A PEP is defined as an individual who is or has been entrusted with a prominent public function.": "No",
                "I am a current or former Politically Exposed Person (PEP). select one required": "No",
                "I am related to or have a close relationship (common friendships, acquaintanceships and past working relationships excluded) with an employee working in the PayPal group of companies. select one required": "No",
                "Are you legally authorized to work in the United States for PayPal? select one required": "Yes",
                "Do you now, or will you in the future, require sponsorship for employment visa status (e.g., H-1B visa status, etc.) to work legally for PayPal in the United States? select one required": self.user_visa_ans,
                "I am related to a Politically Exposed Person (PEP).": "No"
            }
            self.question_select(wd, question_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosure_dict = {
                "Gender: select one required": "Male" if self.user_gender_male else "Female",
                "If you believe you belong to any of the categories of protected veterans listed above": "I AM NOT A VETERAN",
                "Ethnicity: select one required": "Asian",
                "Race": "Asian"
            }
            self.question_select(wd, disclosure_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
