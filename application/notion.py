import random
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class notionApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        sentences = [
            "The company culture at Notion promotes both autonomy and collaboration, fostering an environment where innovation thrives.",
            "I'm drawn to Notion's mission of simplifying complex workflows, a goal that resonates deeply with my own approach to problem-solving.",
            "At Notion, the emphasis on continual learning and growth aligns perfectly with my career aspirations.",
            "The diversity of perspectives within Notion's teams inspires me and encourages creative thinking.",
            "I appreciate how Notion values work-life balance, acknowledging its importance in sustaining productivity and well-being.",
            "Notion's impact on revolutionizing productivity tools motivates me to contribute my skills to their forward-thinking projects."
        ]
        random_sentences = random.sample(sentences, 2)
        paragraph = ' '.join(random_sentences)
        question_dict = {
            "Why do you want to work at Notion": paragraph,
            "Starting August 1st 2022": "Yes",
            "Notion is an in person company": "Yes",
            "<PERSON><PERSON> is excited to resume work": "Yes"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
