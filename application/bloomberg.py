from abc import ABC
from datetime import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class bloombergApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[class="button button--primary"]')
        except:
            if wd.find_element(By.CSS_SELECTOR, '[class="col-xs-12 errortext"]').text.startswith(
                    "Sorry, we are no longer accepting applications for this job."):
                self.database.delete(f"link = '{url}'")
                return -4
        self.hide.hideElement(wd, '[class="cookies__info"]')
        self.hide.hideHeadAndFoot(wd)
        self.click_ele(wd, '[class="button button--primary"]')
        self.expected_shown_element(wd, '[name="username"]')
        wd.find_element(By.CSS_SELECTOR, '[name="username"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[name="password"]').send_keys(self.user_pwd)
        self.click_ele(wd, '[id="login"]')
        # pwd error
        try:
            self.expected_shown_element(wd, '[role="alert"]')
            if wd.find_element(By.CSS_SELECTOR, '[role="alert"]').text:
                return -1
        except:
            pass
        # Applied
        try:
            self.wait_x_sec(3)
            if any(x.text.startswith("You have already applied") for x in
                   wd.find_elements(By.CSS_SELECTOR, '[class="col-xs-12 errortext"]')):
                return 1
        except:
            pass
        # step1 experience
        self.wait_x_sec(5)
        self.expected_shown_element(wd, '[class="AcceptCheckboxFieldContainer tc_formField"]')
        for agree in wd.find_elements(By.CSS_SELECTOR, '[class="AcceptCheckboxFieldContainer tc_formField"]'):
            try:
                ele = agree.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]').get_attribute('id')
                self.click_ele(wd, f'[id="{ele}"]')
            except:
                continue
        for select_ele in wd.find_elements(By.CSS_SELECTOR,
                                           '[class="fieldSpec SelectFormField isRequired fieldSpecPadder"]'):
            Select(select_ele.find_element(By.CSS_SELECTOR, 'select')).select_by_visible_text("Yes")
        self.click_ele(wd, '[class="saveButton tc_formButton"]')
        # step 2
        self.wait_x_sec(5)
        for question in wd.find_elements(By.CSS_SELECTOR, '[class="requiredField WizardFieldLabel tc_formLabel"]'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="labelRequiredIcon"]')
            except:
                continue
            question_text = question.text
            if not question_text: continue
            try:
                ans = question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR, 'select')
            except:
                ans = question.find_element(By.XPATH, '..').find_elements(By.CSS_SELECTOR, 'input[type="radio"]')
            if question_text.startswith("Tell us how you"):
                Select(ans).select_by_visible_text("LinkedIn")
            elif question_text.startswith("Are you legally authorized"):
                a_id = ans[0].get_attribute('id')
                self.click_ele(wd, f'[id="{a_id}"]')
            elif question_text.startswith("Will you require visa sponsorship"):
                a_id = ans[0 if self.user_visa == 'H1b' else -1].get_attribute('id')
                self.click_ele(wd, f'[id="{a_id}"]')
            elif question_text.startswith("Have you been employed by Bloomberg") or question_text.startswith(
                    "Do you or your spouse") or question_text.startswith(
                "Are you currently bound") or question_text.startswith("To ensure Bloomberg's compliance"):
                a_id = ans[-1].get_attribute('id')
                self.click_ele(wd, f'[id="{a_id}"]')

        for question in wd.find_elements(By.CSS_SELECTOR,
                                         '[class="formfieldSpec TextField isRequired formfieldSpecPadder"]') + wd.find_elements(
            By.CSS_SELECTOR, '[class="formfieldSpec MultiLineFormField isRequired formfieldSpecPadder"]'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="labelRequiredIcon"]')
            except:
                continue
            question_text = question.text
            if not question_text: continue
            try:
                ans = question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR, 'input[type="text"]')
            except:
                ans = question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR, 'textarea')
            a_id = ans.get_attribute('id')
            if question_text.startswith("Please list your preferred coding language"):
                wd.find_element(By.CSS_SELECTOR, f'[id="{a_id}"]').send_keys("Python")
            elif question_text.startswith("What is your available start date"):
                wd.find_element(By.CSS_SELECTOR, f'[id="{a_id}"]').send_keys(datetime.now().strftime("%d/%m/%Y"))
        for select_question in wd.find_elements(By.CSS_SELECTOR,
                                                '[class="formfieldSpec SelectFormField isRequired formfieldSpecPadder"]'):
            try:
                select_question.find_element(By.CSS_SELECTOR, '[class="labelRequiredIcon"]')
            except:
                continue
            question_text = select_question.text
            if not question_text: continue
            ans = select_question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR, 'select')
            if question_text.startswith("Can you perform all the essential functions"):
                Select(ans).select_by_visible_text("Yes")
            elif question_text.startswith("Please select the highest level"):
                degree_dict = {
                    'Bachelor': "Bachelor's Degree or equivalent experience",
                    'Master': "Master's Degree or equivalent experience",
                    'PhD': "PhD or equivalent experience"
                }
                Select(ans).select_by_visible_text(degree_dict[self.user_degree])
        self.click_ele(wd, '[class="nextButton tc_formButton"]')
        # step 3
        self.wait_x_sec(4)
        for question in wd.find_elements(By.CSS_SELECTOR, '[class="requiredField WizardFieldLabel tc_formLabel"]'):
            try:
                question.find_element(By.CSS_SELECTOR, '[class="labelRequiredIcon"]')
            except:
                continue
            question_text = question.text
            if not question_text: continue
            try:
                ans = question.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR, 'select')
            except:
                ans = question.find_element(By.XPATH, '..').find_elements(By.CSS_SELECTOR, 'input[type="radio"]')
            if question_text.startswith("Ethnicity"):
                Select(ans).select_by_visible_text("Decline to Identify")
            elif question_text.startswith("Gender"):
                Select(ans).select_by_visible_text("Male" if self.user_gender_male else "Female")
            elif question_text.startswith("Please select one of the options below"):
                a_id = ans[1].get_attribute('id')
                self.click_ele(wd, f'[id="{a_id}"]')
        self.click_ele(wd, '[class="saveButton tc_formButton"]')
        # final step
        self.wait_x_sec(4)
        self.click_ele(wd, '[class="saveButton tc_formButton"]')
        try:
            self.expected_shown_element(wd, '[class="article__header__text"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="article__header__text"]').text.startswith("Thank you"):
                return 1
            return 0
        except:
            return 0
