from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class ciscoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[class="section_with-sidebar autoClearer"]')
        except:
            if wd.find_element(By.CSS_SELECTOR, '[class="title_page-1"]').text == "An error has occurred":
                self.database.delete(f"""link = '{url}'""")
                return -4
        new_url = wd.find_element(By.CSS_SELECTOR, '[class="section_with-sidebar autoClearer"]').find_element(
            By.CSS_SELECTOR, '[class="button-bar button-bar_reverse button-bar_fixed-buttons"]').find_element(
            By.CSS_SELECTOR, 'a').get_attribute('href')
        wd.get(new_url)
        try:
            self.expected_shown_element(wd, '[id="tpt_loginUsername"]')
            wd.find_element(By.CSS_SELECTOR, '[id="tpt_loginUsername"]').send_keys(self.user_account)
            wd.find_element(By.CSS_SELECTOR, '[id="tpt_loginPassword"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[name="login"]')
        except:
            pass
        try:
            self.wait_x_sec(2)
            if wd.find_element(By.CSS_SELECTOR, '[class="errorText"]'):
                return -1
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="title_section-1"]').text == "My Job Activities":
                return 1
        except:
            pass
        try:
            Select(wd.find_element(By.CSS_SELECTOR, '[name="2139"]')).select_by_visible_text("Cisco Jobs Career Site")
            self.click_ele(wd, '[class="saveButton"]')
        except:
            pass
        # Contact Information
        self.expected_shown_element(wd, '[class="title_page-1"]')
        if wd.find_element(By.CSS_SELECTOR, '[class="title_page-1"]').text == "Final Submission":
            try:
                question_id = {"282-4": 'id="282-4_260"',
                               "282-8": 'id="282-8_260"' if self.user_visa == 'H1b' else 'id="282-8_261"',
                               "282-6": 'id="282-6_260"'}
                for id_button in wd.find_elements(By.CSS_SELECTOR, '[class="RadioButtonListContainer"]'):
                    q_id = id_button.get_attribute("id")
                    self.click_ele(wd, f"[{question_id[q_id]}]")
                Select(wd.find_element(By.CSS_SELECTOR, '[id="282-9"]')).select_by_visible_text("1 - 5 years")
            except:
                pass
            self.click_ele(wd, '[class="saveButton"]')
        try:
            self.expected_shown_element(wd, '[class="title_page-1"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="title_page-1"]').text == "Application Submitted":
                return 1
        except:
            return 0
