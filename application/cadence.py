from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class cadenceApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Application Questions'):
                question_dict = {
                    "Have you ever Applied for Work at Cadence": "No",
                    "Have you ever Worked at Cadence": "No",
                    "Are you legally authorized to work": "Yes",
                    "Will you require Immigration Support": self.user_visa_ans,
                    "Do you have any relatives": "No",
                    "Do you have any commitments": "No"
                }
                self.question_select(wd, question_dict)
                self.click_next_page(wd)
        except:
            pass
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Do you identify as Hispanic or Latino": "No",
                "Please select the ethnicity": "Asian",
                "This employer is a Government": "I AM NOT"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
