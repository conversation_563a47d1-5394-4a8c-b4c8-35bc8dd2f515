# -*- coding: utf-8 -*-
# @Time  : 3/26/23 14:36
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: workday.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class workdayApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Would you consider relocating for this role?": 'Yes, I would consider relocating for this role',
                "Are you subject to any non-compete or non-solicitation restrictions at your current or most recent employer?": "No",
                "In your current job, do you use or work on the Workday system?": "No, I do not use the Workday system in my current job",
                "Are you authorized to work in the country where this job is located?": "Yes",
                "Do you now or in the future require any immigration filing or visa sponsorship to maintain work authorization, including sponsorship by Workday, renewal/extension of open work permit, permanent residency, etc.?": self.user_visa_ans,
                "Are you now, or have you ever been: (a) employed by the U.S. federal government, or (b) a U.S. military officer or civilian equivalent?": "No",
                "Are you a current citizen, national or resident of any of the following countries/regions: Iran, Cuba, North Korea, Syria, Crimea, Donetsk People’s Republic (DNR), Luhansk People’s Republic (LNR) regions of Ukraine?": "No",
                "Are you related to a current Workday employee?": "No",
                "To the best of your knowledge, are you related to an employee of a customer, or a government official, who has direct business interactions with Workday?": "No",
                "This information is sought for compliance": "No",
                "I acknowledge that I have read": "Yes",
                "To be considered for employment": "I have read",
                "Please type in the required information below": self.user_name
            }
            self.question_select(wd, question_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_dict = {
                'Have you read and agree to the Non Disclosure Agreement': "I have read and agree to the Non Disclosure Agreement",
                "Have you read and agree to the Arbitration Agreement": "I have read and agree to the Mutual Arbitration Agreement",
                "Click on the link below to review the Non Disclosure Agreement": "I have read and agree to the Non Disclosure Agreement",
                "Click on the link below to review the Arbitration Agreement.": "I have read and agree to the Mutual Arbitration Agreement",
                "Are you related to a current Workday employee": "No",
                "Please enter your name": self.user_name
            }
            self.question_select(wd, q_2_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
