from abc import ABC

from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class uberApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            try:
                self.expected_shown_element(wd, '[text="Apply Now"]')
            except:
                next(filter(lambda x: x.text.startswith("Sorry"), wd.find_elements(By.CSS_SELECTOR, 'h2')))
                self.database.delete(f"link='{url}'")
                return -4
            wd.get(wd.find_element(By.CSS_SELECTOR, '[text="Apply Now"]').get_attribute('href'))
            try:
                self.expected_shown_element(wd, '[data-testid="signin-button"]')
            except:
                if "not-found" in wd.current_url:
                    self.database.delete(f"link='{url}'")
                    return -4
            self.click_ele(wd, '[data-testid="signin-button"]')
            wd.find_element(By.CSS_SELECTOR, '[name="email"]').send_keys(self.user_account)
            wd.find_element(By.CSS_SELECTOR, '[name="password"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[name="submit-button"]')
            try:
                self.expected_shown_element(wd, '[role="alert"]')
                if "Application" in wd.find_element(By.CSS_SELECTOR, '[role="alert"]').text:
                    return 99
            except:
                pass
            try:
                self.expected_shown_element(wd, '[role="alert"]')
                if "successfully" not in wd.find_element(By.CSS_SELECTOR, '[role="alert"]').text:
                    # try to register
                    self.click_ele(wd, '[aria-label="Close"]')
                    self.click_ele(wd, '[data-testid="createaccount-button"]')
                    self.expected_shown_element(wd, '[placeholder="<EMAIL>"]')
                    wd.find_element(By.CSS_SELECTOR, '[placeholder="<EMAIL>"]').send_keys(
                        self.user_account)
                    wd.find_element(By.CSS_SELECTOR, '[placeholder="Password"]').send_keys(self.user_pwd)
                    self.click_ele(wd, '[name="submit-button"]')
                    self.expected_shown_element(wd, '[role="alert"]')
                    if "successfully" not in wd.find_element(By.CSS_SELECTOR, '[role="alert"]').text:
                        return -1
            except:
                pass
            # resume
            self.expected_shown_element(wd, '[data-baseweb="file-uploader-basic"]')
            resume_btn_class = wd.find_element(By.CSS_SELECTOR, '[data-baseweb="file-uploader-basic"]').find_element(
                By.CSS_SELECTOR, 'button').get_attribute('class')
            self.click_ele(wd, f'[class="{resume_btn_class}"]')
            wd.find_elements(By.CSS_SELECTOR, 'input[type="file"]')[-1].send_keys(self.user_resume_path)
            self.expected_shown_element(wd, '[name="firstName"]')
            first_name = wd.find_element(By.CSS_SELECTOR, '[name="firstName"]')
            if first_name.get_attribute('value') != self.user_name.split()[0]:
                first_name.clear()
                first_name.send_keys(self.user_name.split()[0])
            last_name = wd.find_element(By.CSS_SELECTOR, '[name="lastName"]')
            if last_name.get_attribute('value') != self.user_name.split()[1]:
                last_name.clear()
                last_name.send_keys(self.user_name.split()[1])
            phone = wd.find_element(By.CSS_SELECTOR, '[name="mobileNumber"]')
            if phone.get_attribute('value') != self.user_phone:
                phone.clear()
                phone.send_keys(self.user_phone)
            self.wait_x_sec(5)
            self.expected_shown_element(wd, '[class="_css-dvKwsj"]')
            for ele in list(filter(lambda x: x.text.startswith("Check to skip"),
                                   wd.find_elements(By.CSS_SELECTOR, '[class="_css-dvKwsj"]'))):
                self.click_ele(wd, ele)
            personal_info = wd.find_element(By.XPATH, "//h5[starts-with(text(), 'Personal information')]").find_element(
                By.XPATH, "..")
            for question, ans in zip(personal_info.find_elements(By.CSS_SELECTOR, 'span'),
                                     personal_info.find_elements(By.CSS_SELECTOR,
                                                                 '[data-baseweb="form-control-container"]')):
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                if question_text.startswith("Are you currently or have you ever been a Driver"):
                    a_ans = ans.find_element(By.CSS_SELECTOR, 'input[value="No"]').find_element(By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Are you currently employed by one of Uber"):
                    ans.find_element(By.CSS_SELECTOR, 'input[role="combobox"]').send_keys('No')
                    self.expected_shown_element(wd, 'ul[role="listbox"]')
                    p_id = wd.find_element(By.CSS_SELECTOR, 'ul[role="listbox"]').find_elements(By.CSS_SELECTOR, 'li')[
                        -1].get_attribute('id')
                    self.click_ele(wd, f'[id="{p_id}"]')
                elif question_text.startswith("Zip"):
                    ans.find_element(By.CSS_SELECTOR, 'input[name="zipCode"]').send_keys(self.user_zipcode)
                elif question_text.startswith("Can we keep your application") or question_text.startswith(
                        "Are you open to") or question_text.startswith("Do you reside in") or question_text.startswith(
                    "Do you have the legal right"):
                    a_ans = ans.find_element(By.CSS_SELECTOR, 'input[value="Yes"]').find_element(By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Will you now or in the future"):
                    u_choice = self.user_visa_ans
                    a_ans = ans.find_element(By.CSS_SELECTOR, f'input[value="{u_choice}"]').find_element(By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                else:
                    self.logger.error(question_text)
            demographic_info = wd.find_element(By.XPATH,
                                               "//h5[starts-with(text(), 'Demographic information')]").find_element(
                By.XPATH, "..")
            for question in demographic_info.find_elements(By.CSS_SELECTOR, 'span'):
                try:
                    question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                except:
                    continue
                if question_text.startswith("Please select the gender"):
                    gender = "Male" if self.user_gender_male else "Female"
                    a_ans = wd.find_element(By.CSS_SELECTOR, f'input[value="{gender}"]').find_element(By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Please select the race"):
                    a_ans = wd.find_element(By.CSS_SELECTOR,
                                            'input[value="Asian (Not Hispanic or Latino)"]').find_element(
                        By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Please check"):
                    a_ans = wd.find_element(By.CSS_SELECTOR,
                                            'input[value="No, I do not have a disability"]').find_element(
                        By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Do you identify as a Veteran"):
                    a_ans = wd.find_element(By.CSS_SELECTOR,
                                            'input[value="I am not a Protected Veteran, Veteran, military spouse or partner"]').find_element(
                        By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
                elif question_text.startswith("Please select the sexual"):
                    a_ans = wd.find_element(By.CSS_SELECTOR, 'input[value="Heterosexual / Straight"]').find_element(
                        By.XPATH, '..')
                    ActionChains(wd).move_to_element(a_ans).click().perform()
            try:
                wd.find_element(By.CSS_SELECTOR, '[name="zipCode"]').send_keys(self.user_zipcode)
            except:
                pass
            # Arbitration Agreement
            ele = wd.find_elements(By.CSS_SELECTOR, '[class="_css-fEDxno"]')[-2]
            self.click_ele(wd, ele)
            submit = wd.find_element(By.XPATH, "//span[starts-with(text(), 'Submit application')]")
            submit_button_class = submit.find_element(By.XPATH, '..').get_attribute("class")
            self.click_ele(wd, f'[class="{submit_button_class}"]')
            try:
                self.expected_shown_element(wd, '[data-baseweb="heading"]')
                wd.find_element(By.XPATH, "//h1[starts-with(text(), 'Application submitted')]")
                return 1
            except:
                return 0
        except:
            self.expected_shown_element(wd, '[class="btn-base btn-sign-in _style_1RU8Qb"]')
            self.click_ele(wd, 'button[class="btn-base btn-sign-in _style_1RU8Qb"]')
            self.expected_shown_element(wd, 'input[type="email"]')
            wd.find_element(By.CSS_SELECTOR, 'input[type="email"]').send_keys(self.user_account)
            wd.find_element(By.CSS_SELECTOR, 'input[type="password"]').send_keys(self.user_pwd)
            self.click_ele(wd, 'button[class="_style_1Y8Iaj"]')
            try:
                self.wait_x_sec(2)
                try:
                    wd.find_element(By.XPATH, "//div[starts-with(text(), 'Incorrect password')]")
                    return -1
                except:
                    wd.find_element(By.XPATH,
                                    "//div[starts-with(text(), 'Account does not exist for the entered email address.')]")
                    return -1
            except:
                pass
            try:
                wd.find_element(By.XPATH,
                                "//span[starts-with(text(), 'Application limit reached')]")
                return 1
            except:
                pass
            self.expected_shown_element(wd, '[class="personal-information-container"]')
            self.click_ele(wd, '[for="data-retention-question-true"]')
            self.click_ele(wd, '[for="driver-partner-question-false"]')
            first_name = wd.find_element(By.CSS_SELECTOR, '[placeholder="First Name"]')
            first_name.clear()
            first_name.send_keys(self.user_name.split()[0])
            last_name = wd.find_element(By.CSS_SELECTOR, '[placeholder="Last Name"]')
            last_name.clear()
            last_name.send_keys(self.user_name.split()[1])
            phone = wd.find_element(By.CSS_SELECTOR, '[placeholder="Phone Number"]')
            phone.clear()
            phone.send_keys(self.user_phone)
            self.click_ele(wd, '[for="in-usa-true"]')
            try:
                wd.find_element(By.CSS_SELECTOR, '[placeholder="Zip Code"]').send_keys(self.user_zipcode)
            except:
                pass
            self.click_ele(wd, '[for="open-for-roles-true"]')
            # skip experience
            self.click_ele(wd, '[for="no-job-experience"]')
            # skip education
            self.click_ele(wd, '[for="no-education-report"]')
            wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
            self.click_ele(wd, '[for="legal-right-to-work-question-true"]')
            self.click_ele(wd, '[for="sex-1"]' if self.user_gender_male else '[for="sex-2"]')
            self.click_ele(wd, '[for="race-2"]')
            self.click_ele(wd, '[for="disability-2"]')
            self.click_ele(wd, '[for="veteran-4"]')
            self.click_ele(wd, '[for="sexualOrientation-8"]')
            self.click_ele(wd, '[class="apply-now-btn"]')
            try:
                self.expected_shown_element(wd, '[class="bottom-space h4-responsive"]')
                if wd.find_element(By.CSS_SELECTOR, '[class="bottom-space h4-responsive"]').text.startswith("Careers"):
                    return 1
            except:
                return 0
