from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class intelApply(applyInterface, ABC):
    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 20).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "1) As of today's date, are you 18 years of age or older?": "Yes",
                "15) Are you currently authorized to work in the U.S": "Yes",
                "16) Do you now, or in the future, require Intel to sponsor": self.user_visa_ans,
                "1)* As of today's date, are you 18 years of age": "Yes",
                "2)* Are you a current or former employee": "No",
                "3)* Are you an immediate family member": "No",
                "4)* To the best of your knowledge and belief": "No",
                "5)* To the best of your knowledge and belief": "No",
                "6)* Do you own, control, or have": "No",
                "7)* If hired, do you intend to maintain": "No",
                "8)* Do you sit on the board of directors": "No",
                "9)* Are you currently or have you previously worked": "No",
                "10)* Have you previously been employed": "No",
                "12)* Are you a current employee of the US": "No",
                "13)* Are you a current Federal, State": "No",
                "14)* Export Control:": "No",
                "15)* Are you currently authorized": "Yes",
                "16)* Do you now, or in the future, require Intel": self.user_visa_ans,
                "14a)*If your response to question 14 is “No”, please specify": 'China'
            }
            self.question_select(wd, question_dict)
            self.question_select(wd, {"16a)* Are you now, or have you in the past": "No",
                                      "16a)  Please indicate your current immigration status": "H-1",
                                      "14a)*If your response to question 14 is “No”, please specify": 'China'})
            self.question_select(wd, {"16b)* Was your I-140 approved": "I do not have",
                                      "16b) How many months of work authorization": "24",
                                      "14a)*If your response to question 14 is “No”, please specify": 'China'})
            self.question_select(wd, {"16c)* How many months of work authorization": "24",
                                      "16c) Was your I-140 approved": "I do not have an I-140",
                                      "14a)*If your response to question 14 is “No”, please specify": 'China'})
            self.question_select(wd, {"16d)* Please indicate your current immigration": "H-1",
                                      "16d) Are you now, or have you in the past": "No",
                                      "14a)*If your response to question 14 is “No”, please specify": 'China'})
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select the ethnicity": "Asian",
                "Please indicate your veteran": "I am not a veteran",
                "Please indicate your gender": "Male" if self.user_gender_male else "Female"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
