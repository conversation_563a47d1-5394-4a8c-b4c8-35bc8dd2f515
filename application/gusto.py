from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class gustoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Do you live in one of the following Metro areas": "Yes",
            "How did you hear about this": "LinkedIn",
            "Are you legally authorized": "Yes",
            "Do you have": "Yes",
            "Do you currently live in": "Yes",
            "What professional experience": "N/A",
            "In 3-5 sentences": "N/A",
            "Describe": "N/A",
            "Have you applied": "No",
            "Zip Code": self.user_zipcode,
            "How many years": "0"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
