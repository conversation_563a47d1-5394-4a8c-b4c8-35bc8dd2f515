from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class instacartApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split("=")[-1]
        url = f"https://boards.greenhouse.io/embed/job_app?for=instacart&token={j_id}"
        wd.get(url)
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="jobTitle"]').text == "No Jobs Found":
                self.database.delete(f"link='{url}'")
                return -4
        except:
            pass
        try:
            self.hide.hideElement(wd, '[id="masthead"]')
            self.hide.hideElement(wd, '[role="dialog"]')
        except:
            pass
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you hear": "LinkedIn",
            "Are you authorized to work": "Yes",
            "Do you have": "Yes",
            "Have you run": "Yes",
            "Are you open to": "Yes",
            "Are you comfortable": "Yes",
            "Are you proficient": "Yes",
            "What is your experience": "No",
            "Are you currently, or have you previously, worked at Instacart?": "No",
            "Are you legally entitled to work in Canada": "No",
            "Are you an Australian citizen": "No",
            "If you are not an Australian citizen": "No",
            "How have you used data science skills": "I leveraged my data science skills to analyze customer behavior and preferences, allowing us to tailor the marketing campaign for the new product more effectively. This resulted in a 20% increase in pre-launch sign-ups and a successful product launch with higher customer engagement.",
            "What’s the most difficult": "The most challenging Python project I've tackled was a complex natural language processing (NLP) pipeline for sentiment analysis and topic modeling. It involved fine-tuning pre-trained models, handling large datasets, and optimizing performance to achieve accurate sentiment classification and topic extraction for a real-time social media monitoring application.",
            "Please explain": "N/A",
            "Please note your experience administering 401k plans": "None",
            "How many participants did the 401k": "0",
        }

        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
