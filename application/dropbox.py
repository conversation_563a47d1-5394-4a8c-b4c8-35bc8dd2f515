import random
from abc import ABC
from datetime import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class dropboxApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url + "/apply")
        self.expected_shown_element(wd, 'iframe')
        try:
            if wd.current_url == "https://jobs.dropbox.com/all-jobs":
                self.database.delete(f"link='{url}'")
                return -4
        except:
            pass
        self.hide.hideHeadAndFoot(wd)
        self.hide.hideElement(wd, 'iframe')
        self.hide.hideElement(wd, '[id="ccpa_consent_banner"]')
        d = {
            "first_name": self.user_name.split()[0],
            "last_name": self.user_name.split()[-1],
            "email": self.user_account,
            "phone": self.user_phone,
        }
        for k, v in d.items():
            self.expected_shown_element(wd, f'input[name="{k}"]')
            wd.find_element(By.CSS_SELECTOR, f'input[name="{k}"]').send_keys(v)
        self.click_ele(wd, 'a[data-option="attach"]')
        self.expected_shown_element(wd, 'input[name="resume"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        for question in wd.find_elements(By.CSS_SELECTOR, '[class="f02-question"]')[6:]:
            try:
                question.find_element(By.CSS_SELECTOR, '[class="label--required"]')
            except:
                continue
            self.wait_x_sec(random.randint(1, 3))
            question_text = question.find_element(By.CSS_SELECTOR,
                                                  '[class="input__label input__label--before"]').text.strip()
            try:
                ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
            except:
                ans = question.find_element(By.CSS_SELECTOR, 'select')
            if question_text.startswith("What is your legal first"):
                ans.send_keys(self.user_name.split()[0])
            elif question_text.startswith("Current Location"):
                Select(ans).select_by_visible_text(f"US - {self.user_state}")
            elif question_text.startswith("Are you currently authorized to work"):
                Select(ans).select_by_index(2 if self.user_visa == 'H1b' else 1)
            elif question_text.startswith("How did you hear about this role"):
                Select(ans).select_by_visible_text("LinkedIn")
            elif question_text.startswith("Please provide the zip code"):
                ans.send_keys(self.user_zipcode)
            elif question_text.startswith("Have you previously worked"):
                Select(ans).select_by_visible_text("No, I have not previously worked for Dropbox")
            elif question_text.startswith("Did you receive your Bachelor") or question_text.startswith(
                    "Do you have at least") or question_text.startswith("Please email me") or question_text.startswith(
                "Do you have a minimum") or question_text.startswith(
                "Do you live in Canada or the U.S") or question_text.startswith(
                "Do you have experience") or question_text.startswith("Have you completed") or question_text.startswith(
                "Have you developed") or question_text.startswith("Have you worked") or question_text.startswith(
                "Have you customized") or question_text.startswith("Have you managed") or question_text.startswith(
                "Do you have deep, hands-on experience") or question_text.startswith(
                "Do you have prior experience") or question_text.startswith(
                "Do you have professional experience") or question_text.startswith(
                "Do you have demonstrated expertise"):
                Select(ans).select_by_visible_text("Yes")
            elif question_text.startswith("Location Cost Tier"):
                Select(ans).select_by_visible_text("Mid Cost")
            elif "LinkedIn" in question_text or "Website" in question_text:
                ans.send_keys(self.user_linkedin)
            elif question_text.startswith("Education"):
                add = \
                    question.find_element(By.CSS_SELECTOR, '[data-name="educations"]').find_elements(By.CSS_SELECTOR,
                                                                                                     'a')[
                        -1].get_attribute("class")
                self.click_ele(wd, f'[class="{add}"]')
                current_year = datetime.now().year
                for education_que in wd.find_element(By.CSS_SELECTOR, '[data-name="educations"]').find_elements(
                        By.CSS_SELECTOR, '[class="f02-question"]'):
                    education_que_text = education_que.find_element(By.CSS_SELECTOR, 'label').text.strip()
                    if not education_que_text: continue
                    try:
                        ans = education_que.find_elements(By.CSS_SELECTOR, 'input[type="text"]')[0]
                    except:
                        pass
                    if education_que_text.startswith("School Name"):
                        self.click_ele(wd, ans)
                        self.expected_shown_element(wd, '[aria-label="Select a value..."]')
                        input_ = education_que.find_element(By.CSS_SELECTOR, '[aria-label="Select a value..."]')
                        input_.send_keys(self.user_school)
                        self.expected_shown_element(wd, '[class="autocomplete-dropdown__list"]')
                        self.wait_x_sec(1)
                        education_que.find_element(By.CSS_SELECTOR,
                                                   'div[class="autocomplete-dropdown__list"]').find_elements(
                            By.CSS_SELECTOR, 'div')[0].click()
                    elif education_que_text.startswith("Degree"):
                        self.click_ele(wd, ans)
                        self.expected_shown_element(wd, '[aria-label="Select a value..."]')
                        input_ = education_que.find_element(By.CSS_SELECTOR, '[aria-label="Select a value..."]')
                        if self.user_degree == 'PhD':
                            self.user_degree = 'Doctoral'
                        input_.send_keys(self.user_degree)
                        self.expected_shown_element(wd, '[class="autocomplete-dropdown__list"]')
                        self.wait_x_sec(1)
                        education_que.find_element(By.CSS_SELECTOR,
                                                   'div[class="autocomplete-dropdown__list"]').find_elements(
                            By.CSS_SELECTOR, 'div')[0].click()
                    elif education_que_text.startswith("Discipline"):
                        self.click_ele(wd, ans)
                        input_ = education_que.find_element(By.CSS_SELECTOR, '[aria-label="Select a value..."]')
                        input_.send_keys("Computer Science")
                        self.expected_shown_element(wd, '[class="autocomplete-dropdown__list"]')
                        self.wait_x_sec(1)
                        education_que.find_element(By.CSS_SELECTOR,
                                                   'div[class="autocomplete-dropdown__list"]').find_elements(
                            By.CSS_SELECTOR, 'div')[0].click()
                    elif education_que_text.startswith("Start Date"):
                        self.start_month = random.randint(1, 12)
                        self.start_year = random.randint(current_year - 4,
                                                         current_year - 2)  # Adjusted to ensure gap for end date

                        Select(education_que.find_element(By.CSS_SELECTOR,
                                                          '[name="educations[][start_date][month]"]')).select_by_index(
                            self.start_month)
                        education_que.find_element(By.CSS_SELECTOR,
                                                   '[name="educations[][start_date][year]"]').send_keys(self.start_year)
                    elif education_que_text.startswith("End Date"):
                        if self.start_year == current_year - 2:
                            # If start year is recent, end date should be in same year but later month
                            end_year = self.start_year
                            end_month = random.randint(min(self.start_month + 1, 11), 12)
                        else:
                            # Otherwise, generate end date in a later year but before current year
                            end_year = random.randint(min(self.start_year + 1, current_year - 1), current_year - 1)
                            end_month = random.randint(1, 12)

                        Select(education_que.find_element(By.CSS_SELECTOR,
                                                          '[name="educations[][end_date][month]"]')).select_by_index(
                            end_month)
                        education_que.find_element(By.CSS_SELECTOR,
                                                   '[name="educations[][end_date][year]"]').send_keys(end_year)
            elif question_text.startswith("Employment"):
                add = question.find_element(By.CSS_SELECTOR, '[data-name="employments"]').find_elements(By.CSS_SELECTOR,
                                                                                                        'a')[-1]
                self.click_ele(wd, add)
                for company_que in wd.find_element(By.CSS_SELECTOR, '[data-name="employments"]').find_elements(
                        By.CSS_SELECTOR, '[class="f02-question"]'):
                    company_que_text = company_que.find_element(By.CSS_SELECTOR, 'label').text.strip()
                    if not company_que_text: continue
                    try:
                        ans = company_que.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    except:
                        pass
                    if company_que_text.startswith("Company Name"):
                        ans.send_keys(self.user_company)
                    elif company_que_text.startswith("Title"):
                        ans.send_keys(self.user_title)
                    elif company_que_text.startswith("Start Date"):
                        Select(company_que.find_element(By.CSS_SELECTOR,
                                                        '[name="employments[][start_date][month]"]')).select_by_index(
                            int(self.user_start_time.split("/")[0]))
                        company_que.find_element(By.CSS_SELECTOR, '[name="employments[][start_date][year]"]').send_keys(
                            self.user_start_time.split("/")[1])
                    elif company_que_text.startswith("End Date"):
                        if self.user_end_time != "Present":
                            Select(company_que.find_element(By.CSS_SELECTOR,
                                                            '[name="employments[][end_date][month]"]')).select_by_index(
                                int(self.user_start_time.split("/")[0]))
                            company_que.find_element(By.CSS_SELECTOR,
                                                     '[name="employments[][end_date][year]"]').send_keys(
                                self.user_start_time.split("/")[1])
                    elif company_que_text.startswith("Current"):
                        if self.user_end_time == "Present":
                            self.click_ele(wd, company_que.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]'))
            else:
                if question_text and question_text != "Education":
                    self.logger.error(f"Company: {self.company.title()}, Url: {url}, Question: {question_text}")
        # eeco
        try:
            d = {
                "gender": "Male" if self.user_gender_male else "Female",
                "race": "Asian",
                "veteran_status": "I am not a protected veteran",
                "disability_status": "No, I don't have a disability"
            }
            for k, v in d.items():
                ans = wd.find_element(By.CSS_SELECTOR, f'[name="{k}"]')
                Select(ans).select_by_visible_text(v)
        except:
            pass
        self.click_ele(wd, '[value="Submit application"]')
        try:
            self.wait_x_sec(5)
            if "success" in wd.current_url:
                return 1
        except:
            pass
        return 0
