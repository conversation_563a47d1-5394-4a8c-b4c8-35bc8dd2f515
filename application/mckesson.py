from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class mckessonApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Within the last three years, have you been employed by a government": "No",
                "Do you have an agreement with your employer": "No",
                "Are you at least 18 years": "Yes",
                "Are you legally authorized to work": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "To provide the most efficient recruiting process": "Yes",
                "If yes, please": self.user_visa,
            }
            self.question_select(wd, question_dict)
            question_dict_2 = {
                "If yes, please": self.user_visa,
            }
            self.question_select(wd, question_dict_2)
            self.click_next_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith("Voluntary Disclosures"):
            disclosures_dict = {
                "Please select your ethnicity": "Asian",
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Please select your veteran": "I am not a veteran"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
