# -*- coding: utf-8 -*-
# @Time  : 7/25/23 21:15
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: docker.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class dockerApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[id="job-application-form"]')
        except:
            if wd.find_element(By.XPATH, '//*[@id="root"]/div[2]/div[1]/h1').text == "Page not found":
                self.database.delete(f"link = '{url}'")
                return -4
        self.click_ele(wd, '[id="job-application-form"]')
        self.expected_shown_element(wd, '[id="_systemfield_name"]')
        nav_class = wd.find_element(By.XPATH, '//*[@id="root"]/div[1]/ul').get_attribute("class")
        self.hide.hideElement(wd, f'[class="{nav_class}"]')
        wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_name"]').send_keys(self.user_name)
        wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_email"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_resume"]').find_element(By.XPATH, '..').find_element(
            By.CSS_SELECTOR, 'button').click()
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.wait_x_sec(15)
        for question_label in list(
                filter(lambda x: "required" in x.get_attribute("class"), wd.find_elements(By.XPATH,
                                                                                          "//label[contains(@class, 'ashby-application-form-question-title')]"))):
            question = question_label.find_element(By.XPATH, '..')
            question_text = question_label.text
            if question_text.startswith("Phone"):
                question.find_element(By.CSS_SELECTOR, 'input[type="tel"]').send_keys(self.user_phone)
            elif question_text.startswith("Linkedin Profile"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_linkedin)
            elif question_text.startswith("Are you legally authorized to work for any"):
                ans = question.find_elements(By.CSS_SELECTOR, 'button')[0 if self.user_visa == 'H1b' else -1]
                self.click_ele(wd, ans)
            elif question_text.startswith("Location"):
                question.find_element(By.CSS_SELECTOR, 'input[role="combobox"]').send_keys("United States")
                self.expected_shown_element(wd, '[role="option"]')
                p_id = wd.find_elements(By.CSS_SELECTOR, 'div[role="option"]')[0].get_attribute("id")
                self.click_ele(wd, f'[id="{p_id}"]')
            elif question_text.startswith("Physical Mailing Address"):
                ans = question.find_element(By.CSS_SELECTOR, 'textarea')
                ans.send_keys(", ".join([self.user_address, self.user_city, self.user_state, "United States"]))
            else:
                if question_text not in ["Name", "Email", "Resume", "Location"]:
                    self.logger.error(f"Company: {self.company.title()}, Question: {question_text}, {url}, {self.user}")
        submit_btn = wd.find_element(By.XPATH, "//span[contains(text(), 'Submit Application')]")
        self.click_ele(wd, submit_btn)
        try:
            self.expected_shown_element(wd, '[data-highlight="positive"]')
            return 1
        except:
            return 0
