from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class salesforceApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            d = {
                "I understand that upon employment": "Yes",
                "Government": "No",
                "Will you now or could you in the future require sponsorship": self.user_visa_ans,
                "Are you a citizen, national or resident of any": "No",
                "Have you ever been convicted of a crime": "No",
                "Regarding future positions at Salesforce": "Yes",
                "Do you have the unrestricted right to work in the country to which you're applying": self.user_visa_ans,
                "Are you currently or have you in the past been debarred": "No",
                "Are you a citizen, national or resident": "No",
                "Do you have an immediate family member": "No",
                "I attest/confirm": "Yes",
                "Are you a citizen, national or permanent resident": "No"
            }
            self.question_select(wd, d)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            if wd.find_element(By.CSS_SELECTOR, '[data-automation-id="formField-"]').text.startswith("Current School"):
                return 1
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            d = {
                "Gender": "I prefer",
                "Veterans": "I am not",
                "Ethnicity": "Asian"
            }
            self.question_select(wd, d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
