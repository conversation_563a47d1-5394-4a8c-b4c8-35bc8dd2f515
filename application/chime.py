from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class chimeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        if wd.current_url == 'https://careers.chime.com/en/jobs/':
            self.database.delete(f"link='{url}'")
            return -4
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you hear about this job?": "LinkedIn",
            "Are you currently eligible to work legally in the United States of America?": "Yes",
            "Do you acknowledge": "Yes",
            "This role requires": "Yes",
            "Are you currently located": "Yes",
            "This role is": "Yes",
            "How many years of": "3",
            "Are you currently eligible to work legally in Canada": "No",
            "Do you now or in the future require immigration support or visa sponsorship to continue working in Canada": "Yes",
            "Do you now or in the future require immigration support or visa sponsorship to continue working in the United States": self.user_visa_ans,
            "This is a technical role. Do you have hands on experience": "Yes"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
