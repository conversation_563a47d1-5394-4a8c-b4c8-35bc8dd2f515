from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class ebayApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Application Questions'):
                question_dict = {
                    "Are you subject to a non-compete agreement": "No",
                    "Do you authorize eBay": "Yes",
                    "Are you a current or former employee": "No",
                    "Are you legally authorized to work": "Yes",
                    "Will you now or in the future require sponsorship": self.user_visa_ans,
                    "Would you be open to": "Open"
                }
                self.question_select(wd, question_dict)
                self.click_next_page(wd)
        except:
            pass
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Veteran Status Identification": "I am not a veteran",
                "Gender Identification": "Male" if self.user_gender_male else "Female",
                "Ethnicity Identification": "Asian"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
