from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class twosigmaApply(applyInterface, ABC):
    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideHeadAndFoot(wd)
        # link not found
        try:
            if wd.current_url == "https://careers.twosigma.com/careers/Error":
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        self.expected_shown_element(wd, '[class="button button--primary"]')
        self.click_ele(wd, '[class="button button--primary"]')
        self.expected_shown_element(wd, '[id="username"]')
        wd.find_element(By.CSS_SELECTOR, '[id="username"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="password"]').send_keys(self.user_pwd)
        self.click_ele(wd, '[id="login"]')
        try:
            self.expected_shown_element(wd, '[class="alert alert--error"]')
            return -2
        except:
            pass
        # About You
        self.expected_shown_element(wd, '[class="nextButton tc_formButton"]')
        self.hide.hideHeadAndFoot(wd)
        self.click_ele(wd, '[class="nextButton tc_formButton"]')
        # Role Specific Questions
        self.wait_x_sec(3)
        cur_title = list(filter(lambda x: "list__item--current" in x.get_attribute('class'),
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="list list--steps"]').find_elements(
                                    By.CSS_SELECTOR, 'li')))[0]
        if cur_title.text.startswith("Role Specific Questions"):
            for question in wd.find_elements(By.CSS_SELECTOR, '[class="schemaFieldContainer"]'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="labelRequiredIcon"]')
                except:
                    continue
                question_text = question.find_element(By.CSS_SELECTOR, 'legend').text
                if question_text.startswith("How many years"):
                    ans = next((x for x in question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]') if
                                "4" in x.get_attribute('data-option-name')),
                               question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]')[0])
                    ans.click()
                elif question_text.startswith("Strongest programming language proficiency"):
                    ans = next((x for x in question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]') if
                                "Python" in x.get_attribute('data-option-name')),
                               question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')[0])
                    ans.click()
                elif question_text.startswith("Do you have any professional experience"):
                    ans = next((x for x in question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]') if
                                "Yes" in x.get_attribute('data-option-name')),
                               question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')[0])
                    ans.click()
                else:
                    if question_text:
                        ans = ",".join([x.get_attribute('data-option-name') for x in
                                        question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]')])
                        self.logger.error(f"Company: {self.company.title()}, Question: {question_text}, Ans: {ans}")
            self.click_ele(wd, '[class="nextButton tc_formButton"]')
        # Confirm Application
        self.wait_x_sec(3)
        self.expected_shown_element(wd, '[class="saveButton tc_formButton"]')
        self.click_ele(wd, '[class="saveButton tc_formButton"]')
        try:
            self.expected_shown_element(wd, '[class="article"]')
            if len(list(filter(lambda x: "Thank you" in x.text, wd.find_elements(By.CSS_SELECTOR, 'h3')))) == 1:
                return 1
        except:
            return 0
