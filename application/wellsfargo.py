from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class wellsfargoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Did a Senior": "No",
                "Are you a Senior Government Official": "No",
                "Do you currently work, or have you worked for KPMG": "No",
                "Are you currently serving in": "No",
                "Do you have a family relationship": "No",
                "Are you currently a member of": "No",
                "Do you currently serve": "No",
                "Are you currently serving as": "No",
                "Do you have any non-compete": "No",
                "Do you currently have 25%": "No",
                "Do you currently have, or plan to have, any employment or other work that you intend to continue": "No",
                "Have you ever been disciplined by an administrative": "No",
                "Are you a current or former": "No",
                "Are you 18 years of age or older?": "Yes",
                "Are you legally authorized to work in": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Have you ever been involuntarily discharged": "No",
                "Have you ever been disciplined for or the subject": "No",
                "Are you party to any agreement": "No",
                "Do you have any fiduciary appointments": "No",
                "Do you currently hold a position": "No",
                "Do you own, or plan to own any type of business": "No",
                "Are you related to, or do you have a close": "No",
                "Do you currently or have you worked for KPMG": "No",
                "Do you currently hold an elected or appointed public": "No",
                "Are you a current or former military": "No",
                "Do you currently hold or have you held": "No",
                "Have you been referred or recommended": "No",
                "Are you related to or do you have a close personal relationship": "No",
                "Enter current or future visa requirements": self.user_visa
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
