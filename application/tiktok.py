from abc import ABC

from selenium.webdriver import Action<PERSON>hains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.applicationInterfaceModule import applicationInterface as applyInterface


class tiktokApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideElement(wd, '[class="header__17rAy"]')
        try:
            self.expected_shown_element(wd, '[data-test="signInWithEmail"]')
            self.click_ele(wd, '[data-test="signInWithEmail"]')
            # Login
            try:
                self.expected_shown_element(wd, '[id="email"]')
                wd.find_element(By.CSS_SELECTOR, '[id="email"]').send_keys(self.user_account)
                wd.find_element(By.CSS_SELECTOR, '[id="password"]').send_keys(self.user_pwd)
                self.click_ele(wd, '[class="atsx-checkbox-input"]')
                self.click_ele(wd, '[data-test="signInBtn"]')
            except:
                pass
            try:
                WebDriverWait(wd, 5).until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '[class="emailLogin-errorTips-text"]')))
                self.expected_shown_element(wd, '[class="emailLogin-errorTips-text"]')
                if wd.find_element(By.CSS_SELECTOR, '[class="emailLogin-errorTips-text"]'):
                    return -1
            except:
                pass
        except:
            WebDriverWait(wd, 20).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class="ud__confirm__header"]')))
        # NG
        try:
            ng_check = ["Graduate", "BS/MS", "Start"]
            self.expected_shown_element(wd, '[class="resumeEditForm-headerText sofiaBold"]')
            if any(ng in wd.find_element(By.CSS_SELECTOR, '[class="resumeEditForm-headerText sofiaBold"]').text for ng
                   in ng_check):
                with self.lock:
                    if len(self.database.read("link", f"link='{url}'", 'job_info_ng')) == 0:
                        self.database.create({"company": f"{self.company.lower()}", "link": url}, 'job_info_ng')
                    self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        # Application interface
        try:
            WebDriverWait(wd, 20).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class="ud__confirm__header"]')))
            err_msg = wd.find_element(By.CSS_SELECTOR, '[class="ud__confirm__header"]').text
            if "You've already applied for this job. Unable to apply again." in err_msg:
                return 1
        except:
            pass
        try:
            WebDriverWait(wd, 20).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR,
                 '[data-form-field-i18n-name="What gender do you identify with? (Select all that apply)"]')))
            # Gender
            gender = wd.find_element(By.CSS_SELECTOR,
                                     '[data-form-field-i18n-name="What gender do you identify with? (Select all that apply)"]').find_element(
                By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
            wd.execute_script("arguments[0].scrollIntoView(true);", gender)
            gender.click()
            self.wait_x_sec(2)
            next(filter(lambda x: x.text and x.text == "Man" if self.user_gender_male == 1 else "Woman",
                        wd.find_element(By.CSS_SELECTOR, '[class="rc-virtual-list-holder-inner"]').find_elements(
                            By.CSS_SELECTOR, 'span'))).click()
            self.click_ele(wd, '[data-test="applyResumeBtn"]')
            # Race
            self.wait_x_sec(2)
            race = wd.find_element(By.CSS_SELECTOR,
                                   '[data-form-field-i18n-name="What is your race/ethnicity? (Select all that apply)"]').find_element(
                By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
            wd.execute_script("arguments[0].scrollIntoView(true);", race)
            race.click()
            self.wait_x_sec(1)
            next(filter(lambda x: x.text == "Asian", wd.find_elements(By.CSS_SELECTOR, 'span'))).click()
            self.click_ele(wd, '[data-test="applyResumeBtn"]')
        except:
            pass
        # Legal
        self.wait_x_sec(2)
        legal = wd.find_element(By.CSS_SELECTOR,
                                '[data-form-field-i18n-name="Are you legally authorized to work in the US without restriction? "]').find_element(
            By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
        wd.execute_script("arguments[0].scrollIntoView(true);", legal)
        legal.click()
        self.wait_x_sec(1)
        next(filter(lambda x: x.text == "Yes", wd.find_elements(By.CSS_SELECTOR, 'span'))).click()
        self.click_ele(wd, '[data-test="applyResumeBtn"]')
        # Sponsor
        self.wait_x_sec(2)
        sponsor = wd.find_element(By.CSS_SELECTOR,
                                  '[data-form-field-i18n-name="Will you now or in the future require visa sponsorship or a visa transfer?"]').find_element(
            By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
        wd.execute_script("arguments[0].scrollIntoView(true);", sponsor)
        sponsor.click()
        self.wait_x_sec(2)
        try:
            next(filter(lambda x: x.text == self.user_visa_ans,
                        wd.find_elements(By.CSS_SELECTOR, 'span'))).click()
        except:
            ActionChains(wd).move_to_element(sponsor).move_by_offset(0, 35).double_click().perform()
        self.click_ele(wd, '[data-test="applyResumeBtn"]')
        try:
            # Other
            self.wait_x_sec(2)
            try:
                how_your_hear = wd.find_element(By.CSS_SELECTOR,
                                                '[data-form-field-id="7358067228342520114"]').find_element(
                    By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
            except:
                how_your_hear = wd.find_element(By.CSS_SELECTOR,
                                                '[data-form-field-id="7358836697306253618"]').find_element(
                    By.CSS_SELECTOR, '[class="ud-formily-item-control-content-component"]')
            wd.execute_script("arguments[0].scrollIntoView(true);", how_your_hear)
            how_your_hear.click()
            self.wait_x_sec(1)
            next(filter(lambda x: x.text == "Career Website", wd.find_elements(By.CSS_SELECTOR, 'span'))).click()
        except:
            pass
        try:
            check_box = wd.find_element(By.CSS_SELECTOR, '[class="atsx-checkbox"]')
            check_box.find_element(By.CSS_SELECTOR, 'input').click()
        except:
            pass
        # applied through different channels
        try:
            WebDriverWait(wd, 15).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class="atsx-message-custom-text"]')))
            if "applied" in wd.find_element(By.CSS_SELECTOR, '[class="atsx-message-custom-text"]').text:
                return 1
        except:
            pass
        try:
            WebDriverWait(wd, 15).until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[class="thanks"]')))
            return 1
        except:
            try:
                self.click_ele(wd, '[data-test="applyResumeBtn"]')
                WebDriverWait(wd, 15).until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '[class="thanks"]')))
                return 1
            except:
                return 0
