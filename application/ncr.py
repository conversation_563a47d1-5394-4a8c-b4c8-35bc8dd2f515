from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class ncrApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application questions
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {"Are you authorized to work": "Yes",
                             "Do you now, or will you in the future, require sponsorship": self.user_visa_ans,
                             "Are you willing to relocate": "Yes",
                             "What is your Desired Annual Salary": "120K",
                             "I confirm": "Yes"}
            self.question_select(wd, question_dict)
            try:
                addition_dict = {
                    "Will you now or in the future require sponsorship for employment visa status": self.user_visa_ans,
                    "Are you are a citizen of, were you born in": "No",
                    "What is your Desired Annual Salary": "120K",
                    "If Yes, please provide": "H-1B"
                }
                self.question_select(wd, addition_dict)
            except:
                pass
            self.click_calender(wd)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {"Veteran Status": "I am not a veteran",
                                "Gender": "Male" if self.user_gender_male else "Female",
                                "Race/Ethnicity": "Asian (Not Hispanic or Latino) (United States of America)"}
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
