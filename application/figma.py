import random
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class figmaApply(applyInterface, ABC):
    def generate_cover_letter(self):
        # Define sections for the cover letter
        intro_sections = [
            "I am excited to apply for a position at Figma, a company renowned for its innovative approach to design collaboration. ",
            "I've been following Figma's journey closely and I'm thrilled to express my interest in joining your dynamic team. ",
            "Figma's commitment to revolutionizing the design industry has inspired me to seek a role within your organization. ",
            "As a passionate design enthusiast, I'm eager to contribute to Figma's mission of simplifying the design process. "
        ]

        skills_sections = [
            "My experience in user interface design and proficiency with design tools make me a strong candidate for this role. ",
            "I have a solid background in UX research and a deep understanding of design principles, which aligns perfectly with Figma's vision. ",
            "My expertise in front-end development and design thinking methodologies would allow me to make an immediate impact at Figma. ",
            "With my strong problem-solving skills and a creative mindset, I believe I can help Figma push the boundaries of design collaboration. "
        ]

        motivation_sections = [
            "I'm drawn to Figma's culture of continuous learning and cross-functional collaboration, and I'm eager to grow both personally and professionally here. ",
            "Figma's dedication to user-centric design resonates with my values, and I'm enthusiastic about contributing to the development of cutting-edge products. ",
            "The opportunity to work with a team of talented individuals at Figma and to be part of a company shaping the future of design is truly inspiring. ",
            "Figma's user-first approach aligns perfectly with my belief in the power of design to create meaningful and impactful experiences. "
        ]

        closing_sections = [
            "I look forward to the possibility of contributing to Figma's vision and would welcome the opportunity to discuss how my skills and passion can benefit your team. ",
            "Thank you for considering my application. I'm excited about the prospect of being part of Figma's design revolution. ",
            "I'm eager to explore potential collaborations and share my ideas on how I can contribute to Figma's continued success. ",
            "I'm excited about the chance to help Figma shape the future of design, and I would love to discuss how I can be a valuable addition to your team. "
        ]

        # Select random sections from each category
        selected_intro = random.choice(intro_sections)
        selected_skills = random.choice(skills_sections)
        selected_motivation = random.choice(motivation_sections)
        selected_closing = random.choice(closing_sections)

        # Combine the selected sections to create the cover letter
        cover_letter = selected_intro + selected_skills + selected_motivation + selected_closing

        return cover_letter

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Do you have experience with": "Yes",
            "Why do you want to join Figma": self.generate_cover_letter(),
            "From where do you intend to work": ", ".join([self.user_city, self.user_state]),
            "Have you ever worked for Figma": "No",
            "Why do you want to join": "N/A"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
