# -*- coding: utf-8 -*-
# @Time  : 10/24/23 21:40
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: hp.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class hpApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "1. Within the past 5 years, have you been employed by the federal": "No",
                "Conflict of Interest": "No",
                "Non-compete": "No",
                "In order to comply with the U.S. Export Control regulations": "No",
                "Are you legally authorized": "Yes",
                "Will you now, or in the future, require sponsorship": self.user_visa_ans,
            }
            self.question_select(wd, question_dict)
            self.question_select(wd, {"Are you located in US": "Yes"})
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Are you a protected U.S. Military Veteran?": "I AM NOT A VETERAN",
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Please select your race": "Asian (United States of America)"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
