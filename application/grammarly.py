import datetime
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class grammarlyApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Do you need visa sponsorship now or in the future": self.user_visa_ans,
            "What is your state/province of residence": self.user_state,
            "How many years of professional": "0",
            "How many professional years": "1",
            "Where are you located": "SF",
            "Are you authorized to be employed in the US": "Yes",
            "Are you currently located in either": "Yes",
            "Do you live within commutable distance": "Yes",
            "State/Province": self.user_state,
            "Do you require some form of visa sponsorship": self.user_visa,
            "Which of the following best describes": "Machine Learning Engineer",
            "How long have worked for your current": "12",
            "End date year": datetime.datetime.now().year - 2,
            "Do you currently live within": "Yes",
            "If you don't currently live within": "Yes"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
