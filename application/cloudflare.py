from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class cloudflareApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            if wd.current_url == 'https://www.cloudflare.com/careers/':
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Where are you currently based, are you open to relocate?": f"Current location {self.user_city}, {self.user_state}. I am open to relocate",
            "Do you have": "Yes",
            "Please review and acknowledge": "Acknowledge",
            "Are you located in Austin": "Yes",
            "Are you fluent in English": "Yes",
            "Are you currently located in": "Yes",
            "What has been your primary expertise": "My expertise is developing",
            "Within your primary QA expertise": "Validation of web apps or APIs",
            "Have you interacted directly with clients": "Yes",
            "Share a link to your GitHub": "N/A",
            "When was the last time you built a web app": "N/A",
            "Are you currently residing in Austin, TX": "Yes" if self.user_state == "Texas" else "No",
            "Are you based in Portugal": "No",
            "Are you open to relocate": "Yes",
            "Which country are you looking to work": "UK",
            "Are you looking for a hybrid or fully remote role": "Fully Remote",
            "Can you work at one of the following offices two days a week": "Yes",
            "Are you available": "Yes",
            "This position is for a Senior Software Engineer.": "I have between 1 and",
            "What area of software engineering do you have experience with": "I am a lower-level"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
