from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class blockApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you ever been employed at": "No",
            "Have you ever provided any contract work for Block": "No",
            "By clicking": "Accept",
            "I certify that all of the information": self.user_name,
            "Have you been a full-time employee at Block": "No",
            "Are you currently in good standing": "Yes",
            "Have you informed your lead that you are interested in moving internally": "No",
            "To ensure an equitable experience": "Accept",
            "Please confirm you have reviewed": "Yes",
            "Can you please confirm": "Yes",
            "How we interview": "I agree",
            "Have you ever been employed full-time at Block": "No"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
