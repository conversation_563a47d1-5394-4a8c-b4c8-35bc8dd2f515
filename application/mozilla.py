from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class mozillaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you ever been employed": "No",
            "If offered this position would you be able": "Yes",
            "Are you authorized to work": "Yes",
            "Applicant Privacy Notice": "Yes",
            "Which country do you intend to work": "United States",
            "How many years": "3"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
