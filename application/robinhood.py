from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class robinhoodApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        if wd.current_url == 'https://careers.robinhood.com/':
            self.database.delete(f"link='{url}'")
            return -4
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you ever worked for Robinhood as an employee": "I have never worked at Robinhood",
            "Please review and acknowledge": "I acknowledge",
            "What are your gender": "He / Him" if self.user_gender_male else "She / Her",
            "Do you have any Familial Relationships": "No",
            "Do you have at least 2 years of Actimize": "No",
            "Robinhood adheres to applicable laws and regulations in relation to government officials": "No",
            "Relationships": "No",
            "Are you located in either": "Yes, I am located in one of these locations",
            "Are you legally work authorized": "Yes",
            "Are you willing to work from": "Yes",
            "Do you have": "Yes",
            "Are you legally eligible": "Yes",
            "Will you now (or in the future) require visa sponsorship": self.user_visa_ans,
            "What is your preferred office": "Menlo Park, CA",
            "If you have 2+ years": "N/A",
            "Do you currently hold any active": "N/A",
            "Describe your": "N/A",
            "What is your experience building financial models": "I have used financial modeling to influence",
            "Which tools and software are you proficient in": ["A", "D"],
            "What excites you most": "I’m passionate about Robinhood’s mission to democratize"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
