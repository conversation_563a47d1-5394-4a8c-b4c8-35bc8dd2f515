from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class zoomApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you authorized to work for any employer in the country where the job is posted?": "Yes",
                "Do you now, or will you in the future, require sponsorship": self.user_visa_ans,
                "Do you speak multiple": "Yes",
                "Did you learn about this employment opportunity with <PERSON><PERSON> through a government official?": "No",
                "Do you have a personal relationship(s)": "No",
                "To the best of your knowledge": "No",
                "Were you referred or recommended to the position at Zoom": "No",
                "Have you submitted or are planning to submit to Zoom a letter of recommendation from": "No",
                "Please list any languages": "Mandarin"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you require sponsorship to work legally": self.user_visa_ans
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            d = {
                "Please select the veteran": "I am not",
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Please select the ethnicity": "Asian"
            }
            self.question_select(wd, d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
