# -*- coding: utf-8 -*-
# @Time  : 10/1/23 20:48
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: snap.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class snapApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Application Questions'):
                question_dict = {
                    "Are you authorized to work": "Yes",
                    "Will you now or in the future, require sponsorship": self.user_visa_ans
                }
                self.question_select(wd, question_dict)
                self.click_next_page(wd)
        except:
            pass
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "What is your veterans status?": "I AM NOT A VETERAN",
                "What is your gender?": "Male" if self.user_gender_male else "Female",
                "Are you Hispanic or Latino?": "No",
                "What is your race/ethnicity?": "Asian (Not Hispanic or Latino) (United States of America)"
            }
            self.question_select(wd, disclosures_dict)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
