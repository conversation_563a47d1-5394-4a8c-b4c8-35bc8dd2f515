import random
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait

from module.applicationInterfaceModule import applicationInterface as applyInterface


class microsoftApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.expected_shown_element(wd, '[aria-label="Apply"]')
        try:
            wd.find_element(By.XPATH, '//label[starts-with(text(), "* No longer accepting applications")]')
            self.database.delete(f"link = '{url}'")
            return -4
        except:
            pass
        self.expected_shown_element(wd, '[aria-label="Apply"]')
        self.click_ele(wd, '[aria-label="Apply"]')
        self.expected_shown_element(wd, '[aria-label="Sign in with LinkedIn"]')
        self.click_ele(wd, '[aria-label="Sign in with LinkedIn"]')
        self.expected_shown_element(wd, '[id="username"]')
        wd.find_element(By.CSS_SELECTOR, '[id="username"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="password"]').send_keys(self.user_pwd)
        self.click_ele(wd, '[aria-label="Sign in"]')
        try:
            self.expected_shown_element(wd, '[id="error-for-password"]')
            return -1
        except:
            pass
        try:
            self.expected_shown_element(wd, '[value="authorize"]')
            self.click_ele(wd, '[value="authorize"]')
        except:
            pass
        # Applied
        try:
            self.expected_shown_element(wd, '[aria-label="Applied"]')
            return 1
        except:
            pass
        try:
            self.expected_shown_element(wd, '[aria-label="Apply"]')
            self.click_ele(wd, '[aria-label="Apply"]')
        except:
            try:
                self.expected_shown_element(wd, '[aria-label="Complete application"]')
                self.click_ele(wd, '[aria-label="Complete application"]')
            except:
                # applied
                self.expected_shown_element(wd, '[aria-label="Applied"]')
                return 1
        self.wait_x_sec(2)
        wd.switch_to.window(wd.window_handles[1])
        self.expected_shown_element(wd, '[id="gcsVNextCookieBannerId"]')
        self.expected_shown_element(wd, '[id="Qualification_Consent"]')
        self.click_ele(wd, '[id="Qualification_Consent"]')
        self.click_ele(wd, '[id="DPN_Consent"]')
        self.click_ele(wd, '[aria-label="Click to save and continue"]')
        try:
            self.expected_shown_element(wd, '[id="isLegallyAuthorized"]')
        except:
            self.wait_x_sec(random.randint(3, 5))
            self.click_ele(wd, '[aria-label="Click to save and continue"]')
        self.expected_shown_element(wd, '[id="isLegallyAuthorized"]')
        self.click_ele(wd, '[id="isLegallyAuthorized"]')
        self.expected_shown_element(wd, '[id="isLegallyAuthorized-list"]')
        self.expected_shown_element(wd, '[id="isLegallyAuthorized-list0"]')
        self.click_ele(wd, '[id="isLegallyAuthorized-list0"]')
        self.click_ele(wd, '[id="isImmigrationBenefitEligible"]')
        self.expected_shown_element(wd, '[id="isImmigrationBenefitEligible-list"]')
        self.click_ele(wd, f'[id="isImmigrationBenefitEligible-list{0 if self.user_visa == "H1b" else 1}"]')
        next_button_id = wd.find_element(By.XPATH, "//span[starts-with(text(), 'Save and continue')]").get_attribute(
            "id")
        self.click_ele(wd, f'[id={next_button_id}]')
        self.expected_shown_element(wd, '[role="alertdialog"]')
        verify_button_id = list(filter(lambda x: x.get_attribute("id") != next_button_id,
                                       wd.find_elements(By.XPATH,
                                                        "//span[starts-with(text(), 'Save and continue')]")))[0]
        self.click_ele(wd, f'[id={verify_button_id.get_attribute("id")}]')
        try:
            self.expected_shown_element(wd, '[value="authorize"]')
            self.click_ele(wd, '[value="authorize"]')
        except:
            pass
        try:
            self.expected_shown_element(wd, '[class="ms-apply-thank-you-content"]')
            return 1
        except:
            # Candidate Questions
            self.expected_shown_element(wd, '[id="icims_content_iframe"]')
            wd.switch_to.frame(wd.find_element(By.CSS_SELECTOR, 'iframe'))
            self.expected_shown_element(wd, '[id="quesp_form_submit_i"]')
            self.click_ele(wd, '[id="quesp_form_submit_i"]')
            self.expected_shown_element(wd, '[class="iCIMS_TableRow "]')
            for question in wd.find_elements(By.CSS_SELECTOR, '[class="iCIMS_TableRow "]'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="Field_Required"]')
                except:
                    continue
                value = question.find_element(By.CSS_SELECTOR, 'select').text.strip()
                if "make a selection" not in value.lower():
                    continue
                q_id = question.find_element(By.CSS_SELECTOR, 'label').get_attribute("for")
                question_text = question.text
                if question_text.startswith('Are you currently employed by a government') or question_text.startswith(
                        "Have you signed a non-compete") or question_text.startswith(
                    "Are you now or have you ever been employed by a Microsoft") or question_text.startswith(
                    "Have you ever worked with Microsoft"):
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]')).select_by_visible_text("No")
                else:
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]')).select_by_visible_text("Yes")
            wd.find_element(By.CSS_SELECTOR, '[id="quesp_form_submit_i"]').click()
        # Job Specific Questions
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[starts-with(text(), 'Job Specific Questions')]")))
            self.expected_shown_element(wd, '[class="iCIMS_TableRow "]')
            for question in wd.find_elements(By.CSS_SELECTOR, '[class="iCIMS_TableRow "]'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="Field_Required"]')
                except:
                    continue
                value = question.find_element(By.CSS_SELECTOR, 'select').text.strip()
                if "make a selection" not in value.lower():
                    continue
                q_id = question.find_element(By.CSS_SELECTOR, 'label').get_attribute("for")
                question_text = question.text
                if question_text.startswith("Do you have"):
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]')).select_by_visible_text("Yes")
            wd.find_element(By.CSS_SELECTOR, '[id="quesp_form_submit_i"]').click()
            wd.switch_to.default_content()
        except:
            wd.switch_to.default_content()
        try:
            self.expected_shown_element(wd, '[class="ms-apply-thank-you-content"]')
            return 1
        except:
            return 0
