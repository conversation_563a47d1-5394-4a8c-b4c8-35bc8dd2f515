import random
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class airbnbApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split('/')[-2]
        url = f"https://job-boards.greenhouse.io/embed/job_app?for=airbnb&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        answers = [
            "I'm passionate about revolutionizing the travel industry and believe Airbnb's unique approach aligns with my vision.",
            "I admire Airbnb's commitment to sustainability and innovation in creating memorable experiences for travelers.",
            "I have always been impressed by Airbnb's inclusive culture and the opportunity it offers to work with a diverse and talented team.",
            "Airbnb's focus on leveraging technology to create value for hosts and guests resonates with my background in tech-driven solutions.",
            "I am inspired by Airbnb's mission to foster a global community and create a world where anyone can belong anywhere."
        ]
        question_dict = {
            "Why have you chosen to apply to Airbnb": random.choice(answers),
            "Will you now or in the future require visa sponsorship": self.user_visa_ans,
            "How did you hear": "Company Website (Airbnb Careers)",
            "How many years of": "2-4 years",
            "Are you proficient in": "Yes",
            "Please briefly describe": "N/A",
            "Current location and work location preferences": self.user_state,
            "Please identify your gender.": "Male" if self.user_gender_male else "Female",
            "Consent to Privacy Notice": "I have read and consent to the Privacy Notice.",
            "This position requires a PHD degree.": "No" if self.user_degree != 'PhD' else "Yes",
            "Have you been employed by Airbnb": "No",
            "Do you have any immediate blood relatives": "No",
            "Do you have": "Yes",
            "I have experience": "Yes",
            "Would you require visa sponsorship": self.user_visa_ans,
            "What is your level of proficiency": "Advanced",
            "Have you used Large Language Models": "Yes",
            "Please explain": "N/A",
            "Are you located in the United": "Yes",
            "Please list any data visualization": "Tebula",
            "Please list any BI tools": "Tebula"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
