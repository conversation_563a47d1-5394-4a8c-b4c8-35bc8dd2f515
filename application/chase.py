from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class chaseApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd,
                                        '[class="button apply-now-button theme-color-1 apply-now-button--apply-now"]')
        except:
            self.expected_shown_element(wd, '[class="job-details__expired-header error-404__header"]')
            self.database.delete(f"link='{url}'")
            return -4
        self.click_ele(wd, '[class="button apply-now-button theme-color-1 apply-now-button--apply-now"]')
        self.expected_shown_element(wd, 'input[type="email"]')
        wd.find_element(By.XPATH, '//input[@type="email"]').send_keys(self.user_account)
        self.click_ele(wd, '[class="apply-flow-input-checkbox__button"]')
        self.click_ele(wd, 'button[type="submit"]')
        # verification
        try:
            self.expected_shown_element(wd, '[class="pin-code-input__segment"]')
            self.wait_x_sec(6)
            auth_code = self.get_verification_from_gmail("Take action to confirm")
            for code, input_ in zip(auth_code, wd.find_elements(By.CSS_SELECTOR, '[class="pin-code-input__segment"]')):
                input_.find_element(By.CSS_SELECTOR, 'input').send_keys(code)
            self.click_ele(wd, 'button[type="submit"]')
        except:
            pass
        # applied
        try:
            self.expected_shown_element(wd, '[class="apply-flow-dialog__header"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="apply-flow-dialog__header"]').text.startswith(
                    "You already applied"):
                return 1
        except:
            pass
        # step 1 / 4
        try:
            self.expected_shown_element(wd, '[name="addressLine1"]')
        except:
            try:
                self.expected_shown_element(wd, '[name="addressLine1"]')
                if wd.find_element(By.CSS_SELECTOR, '[name="addressLine1"]').get_attribute("value") == '':
                    return -2
            except:
                pass
        try:
            self.click_ele(wd, '[data-qa="applyFlowPaginationNextButton"]')
        except:
            self.expected_shown_element(By.CSS_SELECTOR, '[class="input-row__validation"]')
            return -2
        # page 2 / 4
        # application questions
        questions = wd.find_element(By.CSS_SELECTOR,
                                    'div[class="apply-flow-block apply-flow-block--disqualification-questions"]').find_elements(
            By.CSS_SELECTOR, '[class="input-row input-row--has-picker"]')
        for question in questions:
            question_text = question.find_element(By.CSS_SELECTOR, 'label').text
            ans_choices = question.find_elements(By.CSS_SELECTOR, 'button')
            if question_text.startswith("Are you at least 18 years") or question_text.startswith(
                    "For the position you are applying to, are you legally authorized to work") or question_text.startswith(
                "Do you have"):
                ans = filter(lambda x: x.text.startswith("Yes"), ans_choices)
                if 'selected' not in ans.get_attribute('class'):
                    self.click_ele(wd, ans)
            elif "require sponsorship" in question_text:
                ans = filter(lambda x: x.text.startswith(self.user_visa_ans), ans_choices)
                if 'selected' not in ans.get_attribute('class'):
                    self.click_ele(wd, ans)
            else:
                self.logger.error("Company: {}, Area: {}, Question: {}", self.company, "Main", question_text)
        try:
            questions = wd.find_element(By.CSS_SELECTOR,
                                        'div[class="apply-flow-block apply-flow-block--disqualification-questions"]').find_elements(
                By.CSS_SELECTOR, '[class="input-row input-row--has-picker"]')
            for question in questions:
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                ans = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                if question_text.startswith("What type of immigration support"):
                    ans.send_keys(self.user_visa)
                else:
                    self.logger.error("Company%s, Area%s, Question%s", self.company, "Additional", question_text)
        except:
            pass
        self.click_ele(wd, '[data-qa="applyFlowPaginationNextButton"]')
        # page 3 / 4
        self.click_ele(wd, '[data-qa="applyFlowPaginationNextButton"]')
        # page 4 / 4
        self.expected_shown_element(wd, '[class="apply-flow-block__form-list"]')
        full_name = wd.find_element(By.CSS_SELECTOR, '[name="fullName"]')
        if full_name.get_attribute("value") == '':
            full_name.send_keys(self.user_name)
        try:
            self.expected_shown_element(wd, '[aria-label="Gender"]')
            not_disclose = \
                wd.find_element(By.CSS_SELECTOR, '[aria-label="Gender"]').find_elements(By.CSS_SELECTOR, 'button')[-1]
            if 'selected' not in not_disclose.get_attribute("class"):
                self.click_ele(wd, not_disclose)
        except:
            pass
        submit_btn = wd.find_element(By.CSS_SELECTOR, 'apply-flow-pagination') \
            .find_elements(By.CSS_SELECTOR, 'button[type="button"]')[-1]
        self.click_ele(wd, submit_btn)
        try:
            self.expected_shown_element(wd, '[class="notification__list"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="notification__list"]').text.startswith(
                    "Thank you for your job application"):
                return 1
        except:
            if "my-profile" in wd.current_url:
                return 1
            return 0
