from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class teslaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[class="tds-btn"]')
        except:
            if wd.find_element(By.CSS_SELECTOR, '[class="error-text"]').text.startswith("404"):
                self.database.delete(f"link='{url}'")
                return -4
        wd.get(wd.find_element(By.CSS_SELECTOR, '[class="tds-btn"]').get_attribute("href"))
        self.expected_shown_element(wd, '[name="personal.firstName"]')
        wd.find_element(By.CSS_SELECTOR, '[name="personal.firstName"]').send_keys(self.user_name.split()[0])
        wd.find_element(By.CSS_SELECTOR, '[name="personal.lastName"]').send_keys(self.user_name.split()[1])
        wd.find_element(By.CSS_SELECTOR, '[name="personal.phone"]').send_keys(self.user_phone)
        wd.find_element(By.CSS_SELECTOR, '[name="personal.email"]').send_keys(self.user_account)
        Select(wd.find_element(By.CSS_SELECTOR, '[name="personal.phoneType"]')).select_by_visible_text("Mobile")
        Select(wd.find_element(By.CSS_SELECTOR, '[name="personal.country"]')).select_by_visible_text(
            "United States")
        self.click_ele(wd, '[class="tds-btn tds-btn--tertiary"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.click_ele(wd, '[name="next"]')
        # id="step--legal"
        self.expected_shown_element(wd, '[id="step--legal"]')
        for question in wd.find_element(By.CSS_SELECTOR, '[id="step--legal"]').find_elements(By.CSS_SELECTOR,
                                                                                             '[class="tds-form-item"]'):
            question_text = question.find_element(By.CSS_SELECTOR, '[class="tds-form-label"]').text
            ans_id = question.find_element(By.CSS_SELECTOR, '[class="tds-form-label"]').get_attribute("for")
            if question_text.startswith("What is your availability or notice period?"):
                Select(wd.find_element(By.CSS_SELECTOR, '[name="legal.legalNoticePeriod"]')).select_by_visible_text(
                    "Immediately")
            elif question_text.startswith("Will you now or in the future require"):
                self.click_ele(wd, f'[id="{ans_id}_yes"]' if self.user_visa == 'H1b' else f'[id="{ans_id}_no"]')
            elif question_text.startswith("I authorize Tesla to consider me"):
                self.click_ele(wd, f'[id="{ans_id}_yes"]')
            elif question_text.startswith("Have you previously been employed") or question_text.startswith(
                    "Are you a former/current intern") or question_text.startswith(
                "Do you consent to receiving text messages"):
                self.click_ele(wd, f'[id="{ans_id}_no"]')
            elif question_text.startswith("I have read and understand"):
                self.click_ele(wd, '[name="legal.legalAcknowledgment"]')
            elif question_text.startswith("Legal Name"):
                wd.find_element(By.CSS_SELECTOR, '[name="legal.legalAcknowledgmentName"]').send_keys(self.user_name)
            else:
                self.logger.error(f"Company: {self.company.title()}, Link:{url}, Question: {question_text}")
        self.click_ele(wd, '[name="next"]')
        # id="step--eeo"
        self.expected_shown_element(wd, '[id="step--eeo"]')
        css_selector = '#step--eeo > fieldset:nth-child(1) > div.style_Disclaimer__toBo- > div > div:nth-child(12) > p:nth-child(2)'
        wd.execute_script(f"document.querySelector('{css_selector}').scrollIntoView();")
        self.wait_x_sec(4)
        self.click_ele(wd, '[name="eeo.eeoAcknowledgment"]')
        Select(wd.find_element(By.CSS_SELECTOR, '[name="eeo.eeoGender"]')).select_by_visible_text(
            "Male" if self.user_gender_male else "Female")
        Select(wd.find_element(By.CSS_SELECTOR, '[name="eeo.eeoVeteranStatus"]')).select_by_visible_text(
            "I am not a protected veteran")
        Select(wd.find_element(By.CSS_SELECTOR, '[name="eeo.eeoRaceEthnicity"]')).select_by_visible_text("Asian")
        Select(wd.find_element(By.CSS_SELECTOR, '[name="eeo.eeoDisabilityStatus"]')).select_by_visible_text(
            "No, I do not have a disability and have not had one in the past")
        wd.find_element(By.CSS_SELECTOR, '[name="eeo.eeoDisabilityStatusName"]').send_keys(self.user_name)
        self.click_ele(wd, '[type="submit"]')
        try:
            self.expected_shown_element(wd, '[class="style_ConfirmationHeading__+w44T"]')
            return 1
        except:
            return 0
