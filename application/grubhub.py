# -*- coding: utf-8 -*-
# @Time  : 4/21/24 16:50
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: grubhub.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class grubhubApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question 1 of 2
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally permitted to work in the country": "Yes",
                "Do you now, or will you in the future": self.user_visa_ans
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            d = {
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Do you identify as Hispanic": "No",
                "Please select the ethnicity": "Asian",
                "Please indicate whether you identify as one": "I am not"
            }
            self.question_select(wd, d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
