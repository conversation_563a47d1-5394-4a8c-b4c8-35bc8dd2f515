from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class bmoApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally authorized to work": "Yes",
                "Will you now or in the future require": self.user_visa_ans,
                "Are you presently involved in any outside activities": "No",
                "Have you ever worked for": "No",
                "What is your gender": "Male" if self.user_gender_male else "Female",
                "What is your sexual": "I do not",
                "Are you Hispanic": "No",
                "Are you a Protected Veteran": "No",
                "Please clarify": self.user_visa
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
