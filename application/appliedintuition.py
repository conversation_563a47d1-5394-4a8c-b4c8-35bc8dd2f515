from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class appliedintuitionApply(applyInterface, ABC):
    def applyIt(self, url, wd):
        wd.get(url)
        if wd.current_url == 'https://www.appliedintuition.com/careers':
            self.database.delete(f"link='{url}'")
            return -4
        res = self.greenhouse_info(wd, url)
        if res != 1:
            return res

        question_dict = {
            "Are you open to": "Yes",
            'If you chose "search engine"': "N/A",
            "What is the hardest thing you’ve done": "N/A",
            "Why Applied Intuition": "N/A",
            "What is your preferred programming language": "Python",
            "Is the customer always right": "Yes",
            "When did you do the highest quality work of your life and why": "Every day",
            "What steps did you take to become the best at your job": "Customer obsession",
            "What is the most impactful thing": "N/A",
            "Within the past five years, have you participated in any matter involving Applied Intuition, Inc. as a Government employee": "No",
            "Have you obtained, or are you required to obtain, an ethics opinion regarding employment discussions or post-Government employment restrictions": "No"

        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
