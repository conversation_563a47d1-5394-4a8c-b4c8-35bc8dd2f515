import datetime
from abc import ABC

from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class amazonApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[id="apply-button"]')
        except:
            wd.find_element(By.XPATH, '//*[@id="not-found"]/div[1]/div/div/div/div/h2')
            self.database.delete(f"link = '{url}'")
            return -4
        wd.get(wd.find_element(By.CSS_SELECTOR, '[id="apply-button"]').get_attribute("href"))
        self.expected_shown_element(wd, 'input[name="email"]')
        wd.find_element(By.CSS_SELECTOR, '[name="email"]').send_keys(self.user_account)
        self.click_ele(wd, '[id="sign-in-button"]')
        try:
            self.expected_shown_element(wd, '[id="loginFormPasswordInputField"]')
            wd.find_element(By.CSS_SELECTOR, '[id="loginFormPasswordInputField"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[class="btn btn-main btn btn-default btn-block"]')
        except:
            self.expected_shown_element(wd, '[name="PasswordForm"]')
            wd.find_element(By.CSS_SELECTOR, '[id="passwordFormNewPasswordInputField"]').send_keys(self.user_pwd)
            wd.find_element(By.CSS_SELECTOR, '[id="passwordFormConfirmNewPasswordInputField"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[class="btn btn-main btn btn-default btn-block"]')
        try:
            self.expected_shown_element(wd, '[class="error-msg error-body"]')
            return -2
        except:
            pass
        try:
            self.hide.hideElement(wd, '[data-react-class="UsCookieBanner"]')
        except:
            pass
        # applied
        try:
            wd.find_element(By.CSS_SELECTOR, '[data-react-class="ApplicationDuplicateScreen"]')
            return 1
        except:
            pass
        # You've reached the maximum number of job applications.
        try:
            wd.find_element(By.CSS_SELECTOR, '[class="er-limit-showing"]')
            return 99
        except:
            pass
        # MFA
        try:
            self.click_ele(wd, '[id="disableOptionalMfa"]')
        except:
            pass

        def input_info_till_submit():
            title = \
                list(filter(lambda x: x.text, wd.find_elements(By.XPATH, "//div[@class='application-content']//h2")))[
                    0].text
            if title.startswith("Get SMS"):
                self.click_ele(wd, '[id="save-and-continue-form-button"]')
            elif title.startswith("Job-specific"):
                title = title.split("\n")[0]
                last_parent = wd.find_element(By.CSS_SELECTOR, f'[aria-label="{title}"]')
                for question in last_parent.find_element(By.CSS_SELECTOR, '[id="application-questions"]').find_elements(
                        By.CSS_SELECTOR, '[class="question"]'):
                    question_text = question.find_element(By.CSS_SELECTOR,
                                                          '[class="question question-text"]').find_element(
                        By.CSS_SELECTOR, 'label').text
                    try:
                        s_id = question.find_element(By.CSS_SELECTOR,
                                                     '[class="select2-selection__rendered"]').get_attribute("id")
                        self.click_ele(wd, f'[id="{s_id}"]')
                    except:
                        pass
                    if "sponsorship" in question_text:
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[0 if self.user_visa == 'H1b' else 1].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif "previously worked for Amazon" in question_text or "government" in question_text or question_text.startswith(
                            "Have you interviewed with Amazon"):
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[-1].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith(
                            "Amazon collects this information solely for the purpose of complying with United States "
                            "export control laws and regulations. If you have dual citizenship, please identify the "
                            "last country"):
                        ele = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                        if not ele.text:
                            ele.send_keys("I do not have dual citizenship.")
                    elif question_text.startswith("What date would you be available to start"):
                        try:
                            question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(
                                datetime.datetime.now().strftime('%Y-%m-%d'))
                        except:
                            q_id = \
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                                    By.CSS_SELECTOR, 'li')[0].get_attribute("id")
                            self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith("Which option") or question_text.startswith(
                            "Review the optional locations in the job description"):
                        try:
                            q_id = \
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                                    By.CSS_SELECTOR, 'li')[2].get_attribute("id")
                            self.click_ele(wd, f'[id="{q_id}"]')
                        except:
                            ele = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                            if not ele.text:
                                ele.send_keys("Yes")
                    elif (question_text.startswith("Are you") or question_text.startswith("Do you have") or
                          question_text.startswith(
                              "What would be the timeline") or "office" in question_text or "relocate" in question_text or "Experience" in question_text or question_text.startswith(
                                "What date would you be available") or question_text.startswith(
                                "Can you work") or "commuting" in question_text) or question_text.startswith(
                        "Do you hold"):
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[0].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif "legal name" in question_text:
                        ans = question.find_element(By.CSS_SELECTOR, 'input')
                        if not ans.get_attribute('value'):
                            ans.send_keys(self.user_name)
                    elif question_text.startswith("If you have dual citizenship"):
                        ans = question.find_element(By.CSS_SELECTOR, 'input')
                        if not ans.get_attribute('value'):
                            ans.send_keys("I do not have dual citizenship.")
                    elif question_text.startswith("What is your expected hourly pay rate"):
                        ans = question.find_element(By.CSS_SELECTOR, 'input')
                        if not ans.get_attribute('value'):
                            ans.send_keys("80")
                    elif question_text.startswith("Are you currently studying") or question_text.startswith(
                            "Are you a US citizen or national") or question_text.startswith(
                        "Have you previously been employed by Amazon"):
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[1].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith(
                            "Amazon collects this information solely for the purpose of complying with United States "
                            "export control laws and regulations. Are you a US citizen"):
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[1 if self.user_visa_ans else -1].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith("If you are currently on a visa"):
                        ans = question.find_element(By.CSS_SELECTOR, 'input')
                        if not ans.get_attribute('value'):
                            ans.send_keys(self.user_visa)
                    elif question_text.startswith("What is the highest level"):
                        q_id = \
                            list(filter(lambda x: self.user_degree in x.text, wd.find_element(By.CSS_SELECTOR,
                                                                                              'ul[class="select2-results__options"]').find_elements(
                                By.CSS_SELECTOR, 'li')))[0].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith("How would you rate") or question_text.startswith("How many years"):
                        try:
                            q_id = \
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                                    By.CSS_SELECTOR, 'li')[2].get_attribute("id")
                        except:
                            q_id = \
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                                    By.CSS_SELECTOR, 'li')[-1].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif question_text.startswith("On a scale of 1-10"):
                        q_id = wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                            By.CSS_SELECTOR, 'li')[5].get_attribute("id")
                        self.click_ele(wd, f'[id="{q_id}"]')
                    elif "compensation" in question_text:
                        ans = question.find_element(By.CSS_SELECTOR, 'input')
                        if not ans.get_attribute('value'):
                            ans.send_keys("120K")
                    elif "clearance" in question_text:
                        try:
                            q_id = \
                                wd.find_element(By.CSS_SELECTOR, 'ul[class="select2-results__options"]').find_elements(
                                    By.CSS_SELECTOR, 'li')[-1].get_attribute("id")
                            self.click_ele(wd, f'[id="{q_id}"]')
                        except:
                            ans = question.find_element(By.CSS_SELECTOR, 'input')
                            if not ans.get_attribute('value'):
                                ans.send_keys("NA")
                    else:
                        self.logger.error(f"Company: {self.company}, Area:{title}, Question: {question_text}")
                last_parent.find_element(By.CSS_SELECTOR, '[class="btn btn-primary"]').click()
            elif title.startswith("Work Eligibility"):
                title = title.split("\n")[0]
                last_parent = wd.find_element(By.CSS_SELECTOR, f'[aria-label="{title}"]')
                for question in last_parent.find_element(By.CSS_SELECTOR, '[id="application-questions"]').find_elements(
                        By.CSS_SELECTOR, '[class="question"]'):
                    question_text = question.find_element(By.CSS_SELECTOR,
                                                          '[class="question question-text"]').find_element(
                        By.CSS_SELECTOR, 'label').text
                    if "sponsorship" in question_text or question_text.startswith(
                            "Your response is mandatory when applying for a U.S.-based position. Do you need, or will you need in the future"):
                        q_id = question.find_elements(By.CSS_SELECTOR,
                                                      '[class="custom-control custom-radio"]')[
                            0 if self.user_visa == 'H1b' else 1].find_element(By.CSS_SELECTOR,
                                                                              'label').get_attribute("id")
                        ActionChains(wd).move_to_element(
                            question.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]')).click().perform()
                    elif "government" in question_text or question_text.startswith(
                            "Are you a current or former employee of any government"):
                        q_id = question.find_elements(By.CSS_SELECTOR,
                                                      '[class="custom-control custom-radio"]')[0].find_element(
                            By.CSS_SELECTOR, 'label').get_attribute("id")
                        ActionChains(wd).move_to_element(
                            question.find_element(By.CSS_SELECTOR, f'[id="{q_id}"]')).click().perform()
                continue_button = ActionChains(wd).move_to_element(
                    last_parent.find_element(By.CSS_SELECTOR, '[class="btn btn-primary"]'))
                continue_button.perform()
                continue_button.click().perform()

        try:
            self.expected_shown_element(wd, '[class="card-header form-title"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="card-header form-title"]').text.startswith("Get SMS"):
                self.click_ele(wd, '[id="save-and-continue-form-button"]')
        except:
            pass
        self.expected_shown_element(wd, '[class="question-forms"]')
        cnt = 5
        while not list(filter(lambda x: x.text, wd.find_elements(By.XPATH, "//div[@class='application-content']//h2")))[
            0].text.startswith("Review") and cnt:
            input_info_till_submit()
            cnt -= 1
            self.wait_x_sec(3)
        self.click_ele(wd, '[class="submit-application-button"]')
        try:
            self.expected_shown_element(wd, '[class="next-steps"]')
            return 1
        except:
            return 0
