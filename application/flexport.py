from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class flexportApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        if wd.current_url == 'https://www.flexport.com/careers/jobs/':
            self.database.delete(f"link = '{url}'")
            return -4
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you first hear": "Media",
            "Are you located in or open to relocation": "Yes",
            "Are you authorized to work": "Yes",
            "Application consent": "Yes",
            "Do you have experience": "Yes",
            "Have you previously been employed": "No",
            "Do you currently reside in": "Yes",
            "Are you able": "Yes"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
