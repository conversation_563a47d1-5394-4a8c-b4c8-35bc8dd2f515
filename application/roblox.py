from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class robloxApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        try:
            # school
            for q in wd.find_element(By.CSS_SELECTOR, '[class="education"]').find_elements(By.CSS_SELECTOR,
                                                                                           '[class="field"]'):
                try:
                    q.find_element(By.CSS_SELECTOR, '[class="asterisk"]')
                except:
                    continue
                question_text = q.find_element(By.CSS_SELECTOR, 'label').text.strip()
                if question_text.startswith("School"):
                    self.click_ele(wd, '[id="s2id_education_school_name_0"]')
                    self.expected_shown_element(wd, '[for="s2id_autogen1_search"]')
                    wd.find_element(By.CSS_SELECTOR, '[for="s2id_autogen1_search"]').find_element(By.XPATH,
                                                                                                  '..').find_element(
                        By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_school)
                    self.wait_x_sec(1)
                    self.click_ele(wd, '[id="selectedOption"]')
                elif question_text.startswith("Degree"):
                    self.click_ele(wd, '[id="s2id_education_degree_0"]')
                    self.expected_shown_element(wd, '[for="s2id_autogen2_search"]')
                    wd.find_element(By.CSS_SELECTOR, '[for="s2id_autogen2_search"]').find_element(By.XPATH,
                                                                                                  '..').find_element(
                        By.CSS_SELECTOR, 'input[type="text"]').send_keys(
                        self.user_degree if self.user_degree != 'PhD' else 'Doctor of Philosophy (Ph.D.)')
                    self.wait_x_sec(1)
                    self.click_ele(wd, '[id="selectedOption"]')
                elif question_text.startswith("End Date"):
                    try:
                        education_end_month = wd.find_element(By.CSS_SELECTOR,
                                                              '[name="job_application[educations][][end_date][month]"]')
                        education_end_month.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                      '[class="asterisk"]')
                        education_end_month.send_keys(self.user_school_end_time.split('/')[0])
                    except:
                        pass
                    education_end_year = wd.find_element(By.CSS_SELECTOR,
                                                         '[name="job_application[educations][][end_date][year]"]')
                    education_end_year.find_element(By.XPATH, '..').find_element(By.CSS_SELECTOR,
                                                                                 '[class="asterisk"]')
                    education_end_year.send_keys(self.user_school_end_time.split('/')[-1])
        except:
            pass
        question_dict = {
            "At the time of application, are you 18+ years": "Yes",
            "Are you legally authorized to work": "Yes",
            "This role requires": "Yes",
            "Please review": "I acknowledge that I have read and understood Roblox's Job Applicant Privacy Notice.",
            "How did you first hear about this role?": "LinkedIn",
            "What type of visa sponsorship will you require": "H-1B" if self.user_visa == "H1b" else "Visa Sponsorship Not Required"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
