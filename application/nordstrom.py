from abc import ABC
from datetime import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class nordstromApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you 18 years of age or older?": "Yes",
                "Are you legally eligible to work in the country to which you are applying?": "Yes",
                "Will you now or in the future require": self.user_visa_ans
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "How many years": ["3", "More than 3 years, less than 5 years"],
                "This role requires employees": "I am able to",
                "What is the most complex": "The most complex SQL query I have written involved a combination of multiple subqueries, joins, and conditional logic to retrieve specific data from a large and intricate database.",
                "What are your compensation": "120K",
                "Which of the following cloud technologies": "AWS",
                "Which of the following programming languages": "Python",
                "When is the earliest you would be available to work": datetime.now().strftime("%m, %Y")
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Are you a US Military Veteran?": "I AM NOT A VETERAN",
                "Gender": "Male" if self.user_gender_male else "Female",
                "Ethnicity/Race": "Asian (Not Hispanic or Latino) (United States of America)"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Take Assessment'):
            self.expected_shown_element(wd, '[data-automation-id="inlineAssessmentButton"]')
            self.click_ele(wd, '[data-automation-id="inlineAssessmentButton"]')
            window_before = wd.window_handles[0]
            window_after = wd.window_handles[1]
            wd.switch_to.window(window_after)
            self.expected_shown_element(wd, '[id="OptOutHiddenLink"]')
            self.click_ele(wd, '[id="OptOutHiddenLink"]')
            self.click_ele(wd, '[id="OptOutConfirmYesButton"]')
            self.click_ele(wd, '[name="SubmitButton"]')
            wd.switch_to.window(window_before)
        self.submit(wd)
        return self.apply_success(wd)
