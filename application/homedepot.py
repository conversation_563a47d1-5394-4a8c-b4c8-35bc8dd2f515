from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class homedepotApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Application Questions'):
                question_dict = {
                    "Are you currently 18 years of age or older": "Yes",
                    "Will you now or in the future require sponsorship": self.user_visa_ans,
                    "Are you currently eligible to work": "Yes",
                    "Do you currently, or have you previously, worked for The Home Depot?": "Not Applicable",
                    "Are you willing to": "Yes",
                    "Based on the job description, do you have": "Yes",
                    "Do you meet the educational requirement": "Yes",
                    "Never miss a new career opportunity!": "I do not consent",
                }
                self.question_select(wd, question_dict)
                self.click_next_page(wd)
        except:
            pass
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith('Voluntary Disclosures'):
                disclosures_dict = {
                    "Gender": "Male" if self.user_gender_male else "Female",
                    "Are you Hispanic or Latino": "No",
                    "Race": "Asian"
                }
                self.question_select(wd, disclosures_dict)
                self.click_agreement(wd)
                self.click_next_page(wd)
        except:
            pass
        self.submit(wd)
        return self.apply_success(wd)
