from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class cartaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you currently eligible to work": "Yes",
            "Does this work for you": "Yes",
            "Do you have professional": "Yes",
            "Which is your preferred office": "CA",
            "Have you worked for Carta": "No"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
