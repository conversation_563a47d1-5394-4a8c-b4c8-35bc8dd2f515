from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class creditkarmaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        state2abbrev = {
            'Alaska': 'AK',
            'Alabama': 'AL',
            'Arkansas': 'AR',
            'Arizona': 'AZ',
            'California': 'CA',
            'Colorado': 'CO',
            'Connecticut': 'CT',
            'District of Columbia': 'DC',
            'Delaware': 'DE',
            'Florida': 'FL',
            'Georgia': 'GA',
            'Hawaii': 'HI',
            'Iowa': 'IA',
            'Idaho': 'ID',
            'Illinois': 'IL',
            'Indiana': 'IN',
            'Kansas': 'KS',
            'Kentucky': 'KY',
            'Louisiana': 'LA',
            'Massachusetts': 'MA',
            'Maryland': 'MD',
            'Maine': 'ME',
            'Michigan': 'MI',
            'Minnesota': 'MN',
            'Missouri': 'MO',
            'Mississippi': 'MS',
            'Montana': 'MT',
            'North Carolina': 'NC',
            'North Dakota': 'ND',
            'Nebraska': 'NE',
            'New Hampshire': 'NH',
            'New Jersey': 'NJ',
            'New Mexico': 'NM',
            'Nevada': 'NV',
            'New York': 'NY',
            'Ohio': 'OH',
            'Oklahoma': 'OK',
            'Oregon': 'OR',
            'Pennsylvania': 'PA',
            'Rhode Island': 'RI',
            'South Carolina': 'SC',
            'South Dakota': 'SD',
            'Tennessee': 'TN',
            'Texas': 'TX',
            'Utah': 'UT',
            'Virginia': 'VA',
            'Vermont': 'VT',
            'Washington': 'WA',
            'Wisconsin': 'WI',
            'West Virginia': 'WV',
            'Wyoming': 'WY'
        }
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you come to know about this role": "LinkedIn",
            "Why are you interested in Credit Karma": "Credit Karma offers free credit monitoring and provides users "
                                                      "with their credit scores from TransUnion and Equifax. It also "
                                                      "offers tools and insights to help people understand and "
                                                      "improve their credit. It's popular because it's free and "
                                                      "provides valuable financial information that can help "
                                                      "individuals make better financial decisions.",
            "Why are you interested in this role": "Credit Karma offers free credit monitoring and provides users "
                                                   "with their credit scores from TransUnion and Equifax. It also "
                                                   "offers tools and insights to help people understand and improve "
                                                   "their credit. It's popular because it's free and provides "
                                                   "valuable financial information that can help individuals make "
                                                   "better financial decisions.",
            "Have you worked with data": "Yes",
            "This job will be physically": "I do not currently live in the location mentioned, but would plan to "
                                           "relocate myself for this role.",
            "Will you now or in the future require visa": self.user_visa_ans,
            "State": state2abbrev[self.user_state],
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
