from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class fidelityApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you interested in the location(s)": "Yes",
                "Do you have any agreements with current": "No",
                "Are you authorized to work": "Yes",
                "Do you now or will you in the future require sponsorship": self.user_visa_ans,
                "Have you ever accepted an offer to work": "No",
                "Please select your highest level": "Master's Degree",
                "Do you have any agreements with current ": "No",
                "This role will require in office presence": "Yes",
                "We are currently in a Dynamic Working model.": "Yes",
                "Do you live within commuting distance": "No, I do not live within commuting distance of the location(s) listed, but would relocate",
                "Please check all items": "Agile",
                "What is your base salary": "120K",
                "What is your total compensation": "170K",
                "This role does not include relocation assistance": "Yes",
                "Do you have product development": "Yes",
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {
                "What are your total annual compensation": "$125,000-$149,999",
                "Are you in commuting distance": "I am not in commuting distance from the Merrimack or Westlake, TX office but AM willing to relocate at my own expense",
                "Do you possess a Master of Library Science": "I possess an MS in Information Studies degree",
                "Do you have a Master’s Degree": "Yes" if self.user_degree != 'Bachelor' else "No",
                "How many years of experience": "3",
                "Are you subject to any restrictive covenant": "No",
                "Do you hold a Bachelor of Science": "Yes",
                "Do you have a BS": "Yes",
                "Fidelity’s working model blends": "Yes",
                "The salary range": "Yes",
                "Do you have a minimum": "Yes",
                "Do you have a bachelor's": "Yes",
                "Do you have 3-5 years": "Yes",
                "Do you have": "Yes",
                "What base salary are you targeting for this role": "90",
                "What are your base salary expectations": "120K",
                "Are you": "Yes",
                "How many years": "1",
                "When Fidelity employees return to the office": "I am within already in proximity of one of the locations.",
                "Please rate": "Intermediate",
                "Have you used business intelligence": "Yes",
                "What base salary": "120K",
                "Please indicate": "Any location",
                "What total compensation range": "120K",
                "Do you live within commuting": "No, I do not live within commuting distance of the location(s) listed, but would relocate",
                "What salary range": "120K - 140K",
                "For this role, you will be eligible for a base salary": "120K",
                "Please share your compensation expectations": "120K",
                "Where are you currently located": self.user_state,
                "We expect this hire to work a hybrid": self.user_state,
                "Please detail": "N/A",
                "Please check all items": "Agile",
                "What is your base salary": "120K",
                "What is your total compensation": "170K",
                "This role does not include relocation assistance": "Yes",
                "Do you have product development": "Yes",
                "Which of the following business analysis components": "Data Analysis",
                "What would you like us to know about": "N/A",
                "What three words": "N/A",
                "To ensure this opportunity": "N/A",
                "How would you describe your ability": "N/A",
                "The base salary range": "Yes",
                "I understand that": "Yes",
                "This role involves data entry": "Yes",
                "This is a salary grade 4 opportunity": "Yes",
            }
            self.question_select(wd, q_2_d)
            question_dict_2 = {
                "Are you open": "Yes",
                "Do you have any agreements with current": "No",
                "Do you have a basic knowledge": "Yes",
                "At Fidelity, our goal is for": "Yes",
                "Please share your compensation expectations": "120K",
                "Where are you currently located": self.user_state,
                "How would you describe your ability": "N/A",
                "The base salary range": "Yes",
                "I understand that": "Yes",
                "This role involves data entry": "Yes",
                "This is a salary grade 4 opportunity": "Yes",
            }
            self.question_select(wd, question_dict_2)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            voluntary_dict = {
                "Gender": "Male" if self.user_gender_male else "Female",
                "Veteran Status": "I AM NOT A VETERAN",
                "Ethnicity/Race": "Asian (Not Hispanic or Latinx) (United States of America)"
            }
            self.question_select(wd, voluntary_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
