# -*- coding: utf-8 -*-
# @Time  : 11/16/23 21:59
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: vanguard.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class vanguardApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you related to, by birth": "No",
                "Are you a current or former Government Official": "No",
                "Are you a U.S. citizen, lawful permanent resident": "No" if self.user_visa == "H1b" else "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Have you": "No",
                "Has a search agency submitted your application for consideration within the last 12 months": "No",
                "Do you": "No",
                "If no, please provide": self.user_visa,
                "Securities industry regulations require": "No",
                "Under the Employment policy of Vanguard": "No",
                "To the best of your knowledge": "No",
            }
            self.question_select(wd, question_dict)
            try:
                ans = wd.find_element(By.CSS_SELECTOR, 'textarea')
                if not ans.text:
                    ans.send_keys(self.user_visa)
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
