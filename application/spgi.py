from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class spgiApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally authorized to work in the country to": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Are you currently employed by": "No",
                "Are you willing to relocate": "Yes",
                "Do you require further sponsorship from S&P Global": self.user_visa_ans,
                "What is your notice period": "Less",
                "What types of schedules you are willing to work": "Onsite",
                "Are any of your personal relations": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {
                "Are you authorized to work": "Yes",
                "Are you legally authorized to work in the country to which you are applying": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Are you willing to relocate for the position?": "Yes",
                "Are you currently employed by": "No",
                "Do you require further sponsorship from S&P Global": self.user_visa_ans,
                "Please list": "N/A"
            }
            self.question_select(wd, q_2_d)
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_dict = {
                "I believe that": "I AM NOT A VETERAN",
                "Which of the following describes how you think of yourself?": "Male" if self.user_gender_male else "Female",
                "Please select the race/ethnicity": "Asian (United States of America)",
                "How do you identify your gender?": "Male" if self.user_gender_male else "Female",
            }
            self.question_select(wd, v_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
