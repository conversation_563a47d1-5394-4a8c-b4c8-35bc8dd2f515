from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class pinterestApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "What U.S State do you currently reside in?": self.user_state,
            "Will you require our assistance with work authorization": self.user_visa_ans,
            "What type of engineering work are you primarily interested in?": "Backend",
            "Please select your country ": "USA",
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
