from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class celonisApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you legally authorized": "Yes",
            "Are you currently located": "Yes",
            "Are you able": "Yes",
            "Are you willing": "Yes",
            "Salary Expectation": "120K",
            "I confirm": "I confirm",
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
