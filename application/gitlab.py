from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class gitlabApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Please choose the country": "United States",
            "Are you located in California": "Yes" if self.user_state in ["California", "New York"] else "No",
            "Are you subject to any employment agreements": "No",
            "Are you located in an AMER": "Yes",
            "Do you have": "Yes",
            "Are you based in the Mountain": "Yes",
            "Are you located in the US or Canada": "Yes",
            "How many years of experience": "3 YOE",
            "Why are you interested in strategic": "N/A",
            "Are you located in the United States": "Yes",
            "Do your have Corporate Finance experience": "Yes",
            "Share a project you": "N/A",
            "Give us an example": "N/A",
            "What state or US Territory": self.user_state,
            "Do you meet the requirement of being a US citizen": "No" if self.user_visa == 'H1b' else "Yes",
            "How frequently have you used": "Daily",
            "If yes, please list the programming languages": "Python",
            "Provide an example, or enter N/A": "N/A",
            "It is important to us to create an accessible": "N/A",
            "Please choose the country in which you": ["United States", "My country isn't listed"],
            "What's the name you'd prefer us to use": self.user_name.split(" ")[0]
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
