from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class nikeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you authorized to work in the United States": "Yes",
                "Are you at least 18 yrs of age": "Yes",
                "Are you legally authorized to work in the United States": "Yes",
                "Will you, now or in the future, require Nike sponsorship": self.user_visa_ans,
                "What type of sponsorship will you need now": "H-1B" if self.user_visa == 'H1b' else "Not Applicable",
                "What type of sponsorship will you need in the future to work": "H-1B" if self.user_visa == 'H1b' else "Not Applicable",
                "What is your current immigration status": "H-1B" if self.user_visa == 'H1b' else "Not Applicable",
                "If applicable, when does your current U.S immigration status max-out": "Not Applicable",
                "Have you ever been employed by": "No",
                "Under NIKE Policy": "None",
                "I have the following number of years": "3 years"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "I have the following level": "Advanced",
                "I have the following combination": "Bachelor's degree or higher",
                "I have the following number of years'": ["More than 3 years", "3 years but less than 4 years"],
                "I have the following years": "More than 3 years but less than 4 years",
                "I am willing to work in the area": "Yes, with or without relocation assistance",
                "I have": "Yes",
                "My work experience includes": "Yes",
                "I meet the desired functional requirements": "Yes",
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Veteran Status": "I am not a veteran",
                "Gender": "Male" if self.user_gender_male else "Female",
                "Race": "Asian"
            }
            self.question_select(wd, disclosures_dict)
            try:
                self.click_agreement(wd)
            except:
                pass
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
