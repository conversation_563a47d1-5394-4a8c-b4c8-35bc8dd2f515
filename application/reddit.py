import random
from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class redditApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            'By selecting "I agree,"': "I agree",
            "Degree": self.user_degree + "'s" if self.user_degree != 'PhD' else "Doctor of Philosophy",
            "Discipline": "Computer Science",
            "How many years": ["3", "2"],
            "Describe": "N/A",
            "Do you have": "Yes",
            "Can you briefly": "N/A",
            "Could you describe": "N/A",
            "When a model is": "N/A",
            "Have you collaborated with": "N/A",
            "Have you participated in": "N/A",
            "What is the estimated total number": random.randint(5, 20),
            "What specific experience": "N/A",
            "Have you worked on": "Yes",
            "Have you ever worked on": "Yes",
            "Can you share an example": "N/A",
            "What % of your experience is focused on frontend vs backend development": "100% backend development"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
