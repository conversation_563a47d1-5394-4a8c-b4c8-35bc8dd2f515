from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class applovinApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you worked at AppLovin": 'No',
            "How did you hear about us": 'LinkedIn',
            "Are you located in Austin": "Yes",
            "Please explain": "N/A",
            "Please provide your annual gross salary expectation": "$130K"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
