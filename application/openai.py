import datetime
from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class openaiApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[id="job-application-form"]')
        except:
            self.expected_shown_element(wd, 'h1')
            next(filter(lambda x: x.text.startswith("Page not found"), wd.find_elements(By.CSS_SELECTOR, 'h1')))
            self.database.delete(f"link='{url}'")
            return -4
        self.click_ele(wd, '[id="job-application-form"]')
        self.expected_shown_element(wd, '[id="_systemfield_name"]')
        nav_class = wd.find_element(By.XPATH, '//*[@id="root"]/div[1]/ul').get_attribute("class")
        self.hide.hideElement(wd, f'[class="{nav_class}"]')
        wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_resume"]').find_element(By.XPATH, '..').find_element(
            By.CSS_SELECTOR, 'button').click()
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.wait_x_sec(15)
        name = wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_name"]')
        name.clear()
        name.send_keys(self.user_name)
        email = wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_email"]')
        email.clear()
        email.send_keys(self.user_account)
        for question_label in list(
                filter(lambda x: "required" in x.get_attribute("class"), wd.find_elements(By.XPATH,
                                                                                          "//label[contains(@class, 'ashby-application-form-question-title')]"))):
            question = question_label.find_element(By.XPATH, '..')
            question_text = question_label.text
            if question_text.startswith("When can you start a new role"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(
                    datetime.datetime.now().strftime("%m/%d/%Y"))
                self.click_ele(wd, '[id="_systemfield_name"]')
            elif question_text.startswith("Phone"):
                try:
                    phone = question.find_element(By.CSS_SELECTOR, 'input[name="phone"]')
                except:
                    phone = question.find_element(By.CSS_SELECTOR, 'input[type="tel"]')
                phone.send_keys(self.user_phone)
            elif question_text.startswith("Location"):
                question.find_element(By.CSS_SELECTOR, 'input[role="combobox"]').send_keys(
                    ", ".join([self.user_city, self.user_state]))
                self.expected_shown_element(wd, '[role="option"]')
                wd.find_elements(By.CSS_SELECTOR, '[role="option"]')[0].click()
            elif question_text.startswith("LinkedIn"):
                ele = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                if not ele.get_attribute('value') != self.user_linkedin:
                    ele.clear()
                    ele.send_keys(self.user_linkedin)
            elif question_text.startswith("What percentage of time"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys("80%")
            elif question_text.startswith("What technical domain do you prefer to work"):
                try:
                    ans_id = next(filter(
                        lambda x: x.find_element(By.XPATH, '../..').find_element(By.CSS_SELECTOR,
                                                                                 'label').text.startswith('Back'),
                        question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                except:
                    ans_id = question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]')[0].get_attribute("id")
                self.click_ele(wd, f'[id="{ans_id}"]')
            elif question_text.startswith("What language"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys("Python")
            elif question_text.startswith("Please include a link to your LinkedIn profile"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_linkedin)
            elif question_text.startswith("Will you now or in the future require sponsorship"):
                try:
                    ans_id = next(filter(
                        lambda x: x.find_element(By.XPATH, '../..').find_element(By.CSS_SELECTOR,
                                                                                 'label').text == self.user_visa_ans,
                        question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                    self.click_ele(wd, f'[id="{ans_id}"]')
                except:
                    ans = next(filter(lambda x: x.text == self.user_visa_ans,
                                      question.find_elements(By.CSS_SELECTOR, 'button')))
                    self.click_ele(wd, ans)
            elif question_text.startswith("Are you able to") or question_text.startswith(
                    "Are you comfortable") or question_text.startswith("Are you over the age of 18"):
                ans = question.find_elements(By.CSS_SELECTOR, 'button')[0]
                self.click_ele(wd, ans)
            elif question_text.startswith("Are you authorized to work lawfully in the US") or question_text.startswith(
                    "Do you have experience") or question_text.startswith(
                "Do you have deep") or question_text.startswith("Have you built"):
                try:
                    ans_id = next(filter(
                        lambda x: x.find_element(By.XPATH, '../..')
                                  .find_element(By.CSS_SELECTOR, 'label').text == "Yes",
                        question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                    self.click_ele(wd, f'[id="{ans_id}"]')
                except:
                    ans = next(filter(lambda x: x.text == "Yes", question.find_elements(By.CSS_SELECTOR, 'button')))
                    self.click_ele(wd, ans)
            elif question_text.startswith("How many years of"):
                try:
                    ans_id = next(filter(
                        lambda x: x.find_element(By.XPATH, '../..').find_element(By.CSS_SELECTOR,
                                                                                 'label').text.startswith(
                            "2"),
                        question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                    self.click_ele(wd, f'[id="{ans_id}"]')
                except:
                    question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys("3+ YOE")
            elif question_text.startswith("Please provide an example or evidence of your exceptional ability"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys("N/A")
            elif question_text.startswith("Please select the option that best describes your current U.S. government"):
                ans_id = next(filter(
                    lambda x: x.find_element(By.XPATH, '../..').find_element(By.CSS_SELECTOR, 'label').text.startswith(
                        "No government clearance held"),
                    question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                self.click_ele(wd, f'[id="{ans_id}"]')
            elif question_text.startswith("GitHub Link"):
                ele = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                if not ele.get_attribute('value') != self.user_linkedin:
                    ele.clear()
                    ele.send_keys("N/A")
            else:
                if question_text not in ["Name", "Email", "Resume", "Location"]:
                    self.logger.error(f"Company: {self.company.title()}, Question: {question_text}, {url}, {self.user}")
        submit_btn = wd.find_element(By.XPATH, "//span[contains(text(), 'Submit Application')]")
        self.click_ele(wd, submit_btn)
        try:
            self.expected_shown_element(wd, '[data-highlight="positive"]')
            return 1
        except:
            return 0
