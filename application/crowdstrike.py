import random
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface

sentences = [
    "CrowdStrike's innovative approach to cybersecurity is inspiring.",
    "The use of AI and ML in threat detection is cutting-edge.",
    "I admire the company's proactive stance on emerging threats.",
    "Working with top professionals in the industry excites me.",
    "I am passionate about contributing to cybersecurity solutions.",
    "CrowdStrike's dynamic environment fosters growth and learning.",
    "The company's mission aligns with my professional values.",
    "I am eager to apply my skills in quantitative analysis.",
    "Protecting organizations from cyber threats is crucial.",
    "The collaborative culture at CrowdStrike is very appealing."
]


class crowdstrikeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        # Application Questions
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you eligible to work": "Yes",
                "Will you now or in the future": self.user_visa_ans,
                "Please indicate the applicable": "No",
                "Conflict of Interest": "No",
                "If required, are you willing to relocate": "Yes",
                "Why are you interested": " ".join(random.sample(sentences, 2)),
                "What departments": "Engineering" if self.direction == 'software' else "Finance",
                "Which location": "Austin"
            }
            self.question_select(wd, question_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Do you need a reasonable accommodation": "No"
            }
            self.question_select(wd, question_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        # Voluntary Disclosures
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select the corresponding race": "Asian",
                "Please select the corresponding sex": "Male" if self.user_gender_male else "Female",
                "Please select corresponding veteran": "I AM NOT"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # Review
        self.submit(wd)
        return self.apply_success(wd)
