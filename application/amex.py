from abc import ABC
from datetime import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class amexApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideElement(wd, '[class="headercontainer"]')
        self.hide.hideElement(wd, '[class="djeheader"]')
        try:
            self.click_ele(wd, '[id="dialogTemplate-dialogForm-StatementBeforeAuthentificationContent-ContinueButton"]')
            self.hide.hideElement(wd, '[class="headercontainer"]')
        except:
            pass
        self.expected_shown_element(wd, '[id="dialogTemplate-dialogForm-login-name1"]')
        wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-login-name1"]').send_keys(self.user_account)
        wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-login-password"]').send_keys(self.user_pwd)
        self.click_ele(wd, '[id="dialogTemplate-dialogForm-login-defaultCmd"]')
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="message-notification"]'):
                # register
                self.expected_shown_element(wd, '[id="dialogTemplate-dialogForm-login-register"]')
                self.click_ele(wd, '[id="dialogTemplate-dialogForm-login-register"]')
                self.expected_shown_element(wd, '[id="dialogTemplate-dialogForm-userName"]')
                wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-userName"]').send_keys(
                    self.user_account)
                wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-password"]').send_keys(self.user_pwd)
                wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-passwordConfirm"]').send_keys(
                    self.user_pwd)
                wd.find_element(By.CSS_SELECTOR, '[id="dialogTemplate-dialogForm-email"]').send_keys(self.user_account)
                self.click_ele(wd, '[id="dialogTemplate-dialogForm-defaultCmd"]')
                try:
                    try:
                        self.expected_shown_element(wd, '[id="errorIAM"]')
                    except:
                        self.expected_shown_element(wd, '[class="message-error"]')
                    return -1
                except:
                    pass
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR, '[class="title"]').text.startswith("The job is no longer available."):
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        try:
            if wd.find_element(By.CSS_SELECTOR,
                               '[class="titlepage"]').text == "Review and Submit":
                return 1
        except:
            pass
        try:
            job_title = wd.find_element(By.CSS_SELECTOR, '[title="View this job description"]').text
            if any(word in job_title.lower() for word in ["campus", str(datetime.now().year + 1), "graduate"]):
                if len(self.database.read("link", f"link='{url}'", 'job_info_ng')) == 0:
                    self.database.create({"company": f"{self.company.lower()}", "link": url}, 'job_info_ng')
                    self.logger.warning(f"Found New Grad position: {url}")
                return -4
        except:
            pass
        self.hide.hideElement(wd, '[class="headercontainer"]')
        # You information
        self.wait_x_sec(4)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Your Information":
            wd.find_elements(By.CSS_SELECTOR, '[value="Save and Continue"]')[-1].click()
        # Resume / CV and Cover Letter
        self.wait_x_sec(4)
        if wd.find_element(By.CSS_SELECTOR,
                           '[class="titlepage"]').text == "Resume / CV and Cover Letter":
            self.hide.hideElement(wd, '[class="headercontainer"]')
            wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
            self.click_ele(wd, '[value="Attach"]')
            try:
                self.expected_shown_element(wd, '[value="Yes"]')
                self.click_ele(wd, '[value="Yes"]')
            except:
                pass
            self.click_ele(wd, '[id="editTemplateMultipart-editForm-content-ftf-saveContinueCmdBottom"]')
        # Contact Information
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Contact Information":
            self.hide.hideElement(wd, '[class="headercontainer"]')
            Select(wd.find_element(By.CSS_SELECTOR,
                                   'select[id="et-ef-content-ftf-gp-j_id_id16pc9-page_0-sourceTrackingBlock-recruitmentSourceType"]')).select_by_visible_text(
                "Job Board")
            self.expected_shown_element(wd, '[id="recruitmentSourceDP"]')
            Select(wd.find_element(By.CSS_SELECTOR, 'select[id="recruitmentSourceDP"]')).select_by_visible_text(
                "LinkedIn")
            for question in wd.find_elements(By.CSS_SELECTOR, 'label'):
                try:
                    question.find_element(By.CSS_SELECTOR, '[class="mandatory-img"]')
                except:
                    continue
                question_text = question.text
                try:
                    ans = wd.find_element(By.CSS_SELECTOR, f'input[id="{question.get_attribute("for")}"]')
                except:
                    ans = "et-ef-content-ftf-gp-j_id_id16pc9-page_2-cpi-cfrmsub-frm-dv_cs_candidate_personal_info_ResidenceLocation"
                if question_text.startswith("Legal First Name") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_name.split()[0])
                elif question_text.startswith("Legal Last Name") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_name.split()[1])
                elif question_text.startswith("Street Address") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_address)
                elif question_text.startswith("City") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_city)
                elif question_text.startswith("Place of Residence"):
                    country = ans + "-0"
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{country}"]')).select_by_visible_text(
                        "United States")
                    state = ans + "-1"
                    self.wait_x_sec(1)
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{state}"]')).select_by_visible_text(
                        self.user_state)
                    city = ans + "-2"
                    self.wait_x_sec(1)
                    Select(wd.find_element(By.CSS_SELECTOR, f'[id="{city}"]')).select_by_index(1)
                elif question_text.startswith("Email") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_account)
                elif question_text.startswith("Primary Phone Number") and ans.get_attribute("value") == "":
                    ans.send_keys(self.user_phone)
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # Experience / Education
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Experience / Education":
            self.hide.hideElement(wd, '[class="headercontainer"]')
            company = wd.find_element(By.CSS_SELECTOR,
                                      '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_0-we-wei-0-frm-dv_cs_experience_Employer"]')
            company.clear()
            company.send_keys(self.user_company)
            edu_mapping = {
                "Bachelor": "Bachelor",
                "Master": "Master",
                "PhD": "Doc"
            }
            edu_select = Select(wd.find_element(By.CSS_SELECTOR,
                                                '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_1-csef-efi-0-frm-dv_cs_education_StudyLevel"]'))
            ans = next(filter(lambda x: x.text.startswith(edu_mapping[self.user_degree]), edu_select.options)).text
            edu_select.select_by_visible_text(ans)
            school = wd.find_element(By.CSS_SELECTOR,
                                     '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_1-csef-efi-0-frm-dv_cs_education_Institution"]')
            school.clear()
            school.send_keys(self.user_school)
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # General Questions
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "General Questions":
            try:
                for question in wd.find_element(By.CSS_SELECTOR, '[class="questionnaire"]').find_elements(
                        By.CSS_SELECTOR, 'fieldset'):
                    try:
                        question.find_element(By.CSS_SELECTOR, '[class="requiredimg"]')
                    except:
                        continue
                    question_text = question.find_element(By.CSS_SELECTOR, '[class="description"]').text
                    try:
                        question_text = question_text.split(")")[1].strip()
                    except:
                        pass
                    answer_choices = question.find_elements(By.CSS_SELECTOR, '[class="possibleanswers"]')
                    if any(x.get_attribute("checked") == 'true' for x in answer_choices):
                        continue
                    if "American Express" in question_text or "PricewaterhouseCoopers" in question_text or "Government" in question_text or question_text.startswith(
                            "Do you currently perform any additional paid") or question_text.startswith(
                        "Do you, or your spouse") or question_text.startswith(
                        "In the past three years, have you been a partner") or question_text.startswith(
                        "Do you have a close family member") or "government" in question_text or "employment agreement" in question_text or question_text.startswith(
                        "Do you currently perform any additional paid") or question_text.startswith(
                        "Do you have one or more close family members"):
                        a_id = answer_choices[1].get_attribute('id')
                        self.click_ele(wd, f'[id="{a_id}"]')
                    elif question_text.startswith(
                            "Will you now or in the future require employment sponsorship"):
                        a_id = answer_choices[0 if self.user_visa == "H1b" else -1].get_attribute('id')
                        self.click_ele(wd, f'[id="{a_id}"]')
                        self.expected_shown_element(wd,
                                                    '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_0-koq-j_id_id7pc10-page__1-q-j_id_id2pc11-11-questionExplanationText"]')
                        wd.find_element(By.CSS_SELECTOR,
                                        '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_0-koq-j_id_id7pc10-page__1-q-j_id_id2pc11-11-questionExplanationText"]').send_keys(
                            self.user_visa)
                    elif question_text.startswith(
                            "If you are a minor, do you have working papers") or question_text.startswith(
                        "Are you legally authorized to work"):
                        a_id = answer_choices[0].get_attribute('id')
                        self.click_ele(wd, f'[id="{a_id}"]')
                    else:
                        self.logger.error(
                            f"Company: {self.company.title()}, Question: {question_text}")
                self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
            except:
                pass
        # Job Specific Questions
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Job Specific Questions":
            self.hide.hideElement(wd, '[class="headercontainer"]')
            try:
                for question in wd.find_element(By.CSS_SELECTOR, '[class="questionnaire"]').find_elements(
                        By.CSS_SELECTOR, 'fieldset'):
                    question_text = question.find_element(By.CSS_SELECTOR, '[class="description"]').text
                    if "salary" in question_text or "compensation" in question_text:
                        ans = question.find_element(By.CSS_SELECTOR, 'textarea')
                        if not ans.text:
                            ans.send_keys("100K")
                    elif question_text.startswith("Are you currently working as a temporary"):
                        ans = question.find_elements(By.CSS_SELECTOR, '[class="possibleanswers"]')
                        self.click_ele(wd, ans[-1])
                    elif question_text.startswith("What is your highest level of education completed?"):
                        ans = question.find_elements(By.CSS_SELECTOR, '[class="possibleanswers"]')
                        for answer in ans:
                            answer_text = answer.find_element(By.XPATH, '..').text
                            if self.user_degree in answer_text:
                                self.click_ele(wd, answer)
                                break
                            elif self.user_degree == "PhD" and answer_text.startswith("PHD"):
                                self.click_ele(wd, answer)
                                break
                    elif question_text.startswith("I am interested in the following work locations"):
                        ans = question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                        for ans in ans:
                            if ans.get_attribute("checked") == 'true':
                                continue
                            p_id = ans.get_attribute("id")
                            self.click_ele(wd, f'[id="{p_id}"]')
            except:
                pass
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # Demographics
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Demographics":
            self.hide.hideElement(wd, '[class="headercontainer"]')
            for demo_question in wd.find_elements(By.CSS_SELECTOR, 'select'):
                try:
                    Select(demo_question).select_by_visible_text("Asian (Not Hispanic or Latino)")
                except:
                    try:
                        Select(demo_question).select_by_visible_text("Male" if self.user_gender_male else "Female")
                    except:
                        Select(demo_question).select_by_visible_text("I am not a protected Veteran")

            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # Voluntary Self ID
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "Voluntary Self ID":
            self.expected_shown_element(wd, 'iframe')
            wd.switch_to.frame(wd.find_element(By.CSS_SELECTOR, 'iframe'))
            name = wd.find_element(By.CSS_SELECTOR, '[aria-label="Name"]')
            name.clear()
            name.send_keys(self.user_name)
            if wd.find_element(By.CSS_SELECTOR,
                               '[aria-label="No, I do not have a disability and have not had one in the past"]').get_attribute(
                "value") != 'Yes':
                self.click_ele(wd, '[aria-label="No, I do not have a disability and have not had one in the past"]')
            date = wd.find_element(By.CSS_SELECTOR, '[title="Date format: MM/dd/yy"]')
            date.clear()
            date.send_keys(datetime.now().strftime("%m/%d/%Y"))
            wd.switch_to.default_content()
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # eSignature
        self.wait_x_sec(3)
        if wd.find_element(By.CSS_SELECTOR, '[class="titlepage"]').text == "eSignature":
            name = '[id="et-ef-content-ftf-gp-j_id_id16pc9-page_0-eSignatureBlock-cfrmsub-frm-dv_cs_esignature_FullName"]'
            self.click_ele(wd, name)
            wd.find_element(By.CSS_SELECTOR, name).send_keys(self.user_name)
            self.click_ele(wd, '[id="et-ef-content-ftf-saveContinueCmdBottom"]')
        # sumbit
        self.wait_x_sec(4)
        self.hide.hideElement(wd, '[class="headercontainer"]')
        self.click_ele(wd, '[id="et-ef-content-ftf-submitCmdBottom"]')
        try:
            self.expected_shown_element(wd,
                                        '[id="et-ef-content-flowTemplate-gp-j_id_id16pc9-page_0-thankYouBlock-tyTitle"]')
            return 1
        except:
            return 0
