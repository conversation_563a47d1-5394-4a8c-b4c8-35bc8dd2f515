from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class lyftApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split("=")[-1]
        url = f"https://boards.greenhouse.io/embed/job_app?for=lyft&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Please enter your relevant employment and military": "Thank you",
            "Have you been employed by Lyft": "No",
            "Do you have a L3 Anaplan": "No",
            "May we contact your current employer": "Yes",
            "Can you perform these essential functions": "Yes",
            "Do you have experience working as a Data Scientist": "Yes",
            "This position is based in the United States. Do you currently reside": "I am willing to relocate",
            "Please review the linked document": "I ack",
            "This role is work-from-anywhere": "Yes",
            "If you currently reside in a U.S. territory": "Yes",
            "I have reviewed and": "Yes",
            "Do you live in": "Yes",
            "Please describe any need for a reasonable accommodation for this hiring process": "N/A",
            "Tell us": "N/A",
            "Work Authorization": "I am authorized to work for any" if self.user_visa != "H1b" else '''I require/will require Lyft's''',
            "I certify that the facts set forth": self.user_name,
            "Are you interested in Data Science": "Yes",
            "This position is based in": "Yes",
            "Do you have experience": "Yes",
            "This position is required to work": "Yes",
            "How many years of SQL experience": "4",
            "Have you worked in a B2B": "B2C",
            "How many years of experience": "4"
        }
        try:
            self.greenhouse_question_answer_old(wd, question_dict)
        except:
            self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
