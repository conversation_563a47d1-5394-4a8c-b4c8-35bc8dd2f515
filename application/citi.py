from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class citiApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        # To workday
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you at least 18 years of age?": "Yes",
                "Are you serving, or have you ever served": "No",
                "Are you legally authorized to work in the country": "Yes",
                "Do you have any relatives": "No",
                "Do you have any family members": "No",
                "Were you a partner and/or have you ever been employed by KPMG LLP": "No",
                "To the best of your knowledge": "No",
                "Will you, now or in the future, require sponsorship": self.user_visa_ans,
                "In the text box below please provide": self.user_visa
            }
            self.question_select(wd, question_dict)
            self.wait_x_sec(2)
            self.question_select(wd, {"Can you, within the time period prescribed by law": "Yes",
                                      "In the text box below please provide": self.user_visa})
            self.click_next_page(wd)
        self.wait_x_sec(4)
        # Application question 2
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict_second = {
                "Are you at least 18 years of age?": "Yes",
                "Are you serving, or have you ever served": "No",
                "Will you, now or in the future": self.user_visa_ans
            }
            self.question_select(wd, question_dict_second)
            self.click_next_page(wd)
        # Voluntary disclosure page
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        # Submit
        self.submit(wd)
        return self.apply_success(wd)
