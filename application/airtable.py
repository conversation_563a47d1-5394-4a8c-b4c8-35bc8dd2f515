from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class airtableApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you currently based in": "Yes" if self.user_state == 'California' else "No",
            "If you are not based out of the San Francisco": "I am based in Bay Area" if self.user_state == 'California' else f"I am based in {self.user_state} and willing to relocate",
            "At a high level (3-5 sentences)": "N/A",
            "What is your preference on which team to join":
                ["AI", "Deploy Infrastructure", "Scaling and Resiliency",
                 "Storage"],
            "How many years of full-time": "4+",
            "How many total years of full-time": "4+",
            "Briefly describe": "N/A",
            "What is your current job title": self.user_title,
            "What is your current company": self.user_company,
            "Are you open to": "Yes",
            "If yes": "San",
            "Where is your ideal next role": "Backend"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
