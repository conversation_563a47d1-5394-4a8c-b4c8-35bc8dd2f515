from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class fisApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question 1 of 2
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you now or have you ever been employed": "No",
                "Do you have any relatives": "No",
                "Do you now, or will you in the future": self.user_visa_ans,
                "Do you have any restrictions": "No",
                "FIS requests the following": "No",
                "What is your desired salary": "120K"
            }
            self.question_select(wd, question_dict)
            try:
                ans = wd.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')[-1]
                if not ans.get_attribute("checked"):
                    self.click_ele(wd, ans)
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {"Are you willing to relocate": "Yes", "What is your desired salary": "120K"}
            self.question_select(wd, q_2_d)
            self.click_next_page(wd)
        # Voluntary disclosure
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
            self.click_next_page(wd)
        except:
            pass
        # submit
        self.submit(wd)
        return self.apply_success(wd)
