from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class hudsonrivertradingApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "How did you hear": "Job Posting",
            "Have you interviewed with HRT in the past": "No",
            "Have you previously worked for Hudson River Trading": "No",
            "Do you have any outstanding offers or deadlines": "No",
            "If you heard about us": "N/A",
            "Which HRT office": "New York",
            "Please do not apply to multiple jobs. If you are interested in being considered for another role": "Any position"
        }
        self.greenhouse_question_answer_old(wd, question_dict)
        return self.greenhouse_submit(wd)
