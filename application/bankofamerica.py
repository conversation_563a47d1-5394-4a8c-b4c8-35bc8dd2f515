import random
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class bankofamericaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you legally authorized to work in the United States": "Yes",
                "Are you at least 18 years old": "Yes",
                "Do you have any relatives": "No",
                "Do you currently or have you worked at": "No",
                "Are you currently an active contractor": "No",
                "Were you referred to Bank of America": "No",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Do you reside in one of the five": "No",
                "Would you consider": "Yes, at my own expense under the right circumstances",
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            languages = [
                "Python", "Java", "Ruby", "PHP", "JavaScript",
                "C++", "C#", "Swift", "Go", "R"
            ]
            question_dict = {
                "How many years of": "2",
                "List the top 3 programming languages": ", ".join(random.sample(languages, 3)),
                "In which of the following Microsoft": "Pivot Tables",
                "With which of the following data analysis tasks": "Analyzing large amounts of data"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Veteran": "Not A Veteran",
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
