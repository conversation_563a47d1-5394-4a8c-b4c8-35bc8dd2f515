from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class doordashApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Are you currently authorized to work": "Yes",
            "Some roles": "Yes",
            "How would you self-rank your SQL ability": "Intermediate",
            "Do you have": "Yes",
            "Applicant Privacy Acknowledgement": "Yes",
            "Have you worked at DoorDash": "I have not worked at DoorDash",
            "We would like to contact you": "No",
            "Would you like to receive communications": "No",
            "Have you worked for": "No",
            "How much did content from": "5 = Strong",
            "Select all analytics tools": ["Excel", "SQL", "Python", "Tableau", "Google", "Looker"],
            "What is your preferred working location": "Mountain View, CA",
            "Will you now require immigration sponsorship": self.user_visa_ans,
            "Will you in the future require immigration": self.user_visa_ans
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
