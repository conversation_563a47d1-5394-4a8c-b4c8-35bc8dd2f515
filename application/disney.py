# -*- coding: utf-8 -*-
# @Time  : 10/10/23 19:53
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: disney.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class disneyApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Do you currently require sponsorship": self.user_visa_ans,
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Are you legally authorized to work": "Yes",
                "Which of these potential reasons": "Company"
            }
            self.question_select(wd, question_dict)
            self.question_select(wd, {"At any point in the future": self.user_visa_ans,
                                      "Which of these potential reasons": "Company"})
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Please select your Veteran status": "I AM NOT A VETERAN",
                "Please select your gender": "Man" if self.user_gender_male else "Woman",
                "Please select the race category": "Asian (United States of America)"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
