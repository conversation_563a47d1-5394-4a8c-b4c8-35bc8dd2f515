from abc import ABC

from selenium.webdriver.common.by import By

from module.workdayModule import workdayModule as applyInterface


class comcastApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        self.my_info_page(wd)
        self.my_exp_page(wd)
        # Application Questions
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Highest degree": self.user_degree if self.user_degree != 'PhD' else 'Ph.D',
                "Are you at least 18 years of age? select one required": 'Yes',
                "What are your base salary expectations": "100,000 to 110,000",
                "What are your total cash compensation expectations": "110,000 to 120,000",
                "Do you have a legal right to work in this country": "Yes",
                "Will you now or in the future require sponsorship for employment visa status": self.user_visa_ans,
                "Have you ever applied for employment with this company": "No",
                "Do you have any relatives employed by Comcast": "No",
                "Are there any contractual restrictions on your ability to work": "No",
                "Are you bound by any non-solicitation agreements which would": "No",
                "Are you involved in any outside activities": "No",
                "Job Specific Questions:Are you comfortable with the pay": "Yes",
                "Government Affiliations and Procurement Disclosures": "No",
                "In the last five (5) years": "No",
                "If you have worked for a federal, state, or local": "No",
                "Select Yes if": "No",
                "Some positions at Comcast require employees be given access": "No",
                "Salary Expectation": "100,000 to 110,000",
                "Are you willing to Relocate": "Yes",
                "When are you available to start": "ASAP",
                "When are you able to work": "ASAP",
            }
            self.question_select(wd, question_dict)
            try:
                additional_dict = {
                    "Some positions at Comcast require": "No",
                    "Are you willing to Relocate?": "Yes"
                }
                self.question_select(wd, additional_dict)
            except:
                pass
            self.click_next_page(wd)
        # Voluntary Disclosures
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "If you believe you belong to any of the categories of protected veterans": "I am not a veteran",
                "EEO Questionnaire - Comcast is a Government Contractor subject to": "Asian (United States of America)",
                "What is your gender?": "Male" if self.user_gender_male else "Female",
                "What is your Sex": "Male" if self.user_gender_male else "Female",
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        # Review
        self.submit(wd)
        return self.apply_success(wd)
