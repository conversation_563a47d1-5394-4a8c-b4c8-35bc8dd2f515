from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class expediaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Have you ever worked at Expedia or any of its affiliates in any capacity,": "No",
                "Are you legally authorized to work in the United States?": "Yes",
                "This position will require the successful": "Yes",
                "Are you eligible to work": "Yes",
                "Please enter your current Address City": self.user_city,
                "Please enter your current Address State": self.user_state
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith("Application Questions 2"):
            q_2_d = {
                "Would you be open to relocation": "National",
                "Are you legally authorized to work": "Yes"
            }
            self.question_select(wd, q_2_d)
            for field in wd.find_elements(By.CSS_SELECTOR, '[data-automation-id="formField-"]'):
                question_text = field.find_element(By.CSS_SELECTOR, 'label').text
                if question_text.startswith("Please enter your current Address City"):
                    ans = field.find_element(By.CSS_SELECTOR, 'textarea')
                    if not ans.text:
                        ans.send_keys(self.user_city)
                elif question_text.startswith("Please enter your current Address State"):
                    ans = field.find_element(By.CSS_SELECTOR, 'textarea')
                    if not ans.text:
                        ans.send_keys(self.user_state)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
