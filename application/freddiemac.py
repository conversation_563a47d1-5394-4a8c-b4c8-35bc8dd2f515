# -*- coding: utf-8 -*-
# @Time  : 3/23/23 10:21
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: freddiemac.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class freddiemacApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Do you currently possess unrestricted lawful authorization to work in the U.S.": "Yes",
                "For purposes of the following question, “sponsorship ": self.user_visa_ans,
                "Have you ever been an employee": "No",
                "Are you willing to relocate": "Yes",
                "Desired Base Salary": "$90,000-$100,000",
                "Total Desired Compensation": "$100,000-$125,000",
                "Freddie Mac does not permit dual employment.": "Yes"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Are you a current or former employee of Freddie Mac’s current outside auditor, Price Waterhouse Coopers (PwC)": "No",
                "Is your spouse, domestic partner": "No",
                "Within the preceding three years": "No",
                "Are you subject to any agreement": "No",
                "Are (or were) you an employee ": "No",
                "Freddie Mac's Relationships": "No",
                "Do you have any confidential information": "No",
                "Freddie Mac policy": "No",
                "Have you": "No",
                "Freddie Mac does not permit dual employment.": "Yes"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.question_select(wd, {"Please select your race": "Asian (United States of America)"})
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
