import random
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class lowesApply(applyInterface, ABC):
    def click(self, wd):
        self.wait_x_sec(3)
        self.expected_shown_element(wd, '[class="ng-star-inserted"]')
        self.hide.hideElement(wd,
                              '[class="screen-modal__header  screen-bg__color--grey7 screen-page__header--SelectGroup"]')
        for no_button in list(filter(lambda x: x.text == 'NO',
                                     wd.find_elements(By.CSS_SELECTOR, "button"))):
            self.click_ele(wd, no_button)
            self.wait_x_sec(1)

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you 18 years of age or older": "Yes",
                "Do you have a legal right to work": "Yes",
                "In accordance with": "Yes",
                "As part of the hiring process": "Yes",
                "Do you have any relatives, related": "No",
                "Do you currently reside in the same": "No",
                "Will you now, or in the future": self.user_visa_ans,
                "US Residents Outside of Maine": "I have read and agree with the above release.",
                "Please enter your full legal name": self.user_name,
                "Permanent Address with Postal Code": ", ".join([self.user_address, self.user_zipcode]),
                "Do you have a gap in employment": "N/A",
                "Have you ever worked in any other company": "N/A",
                "Notice Period": ["2 Weeks", "31"],
                "What are the last 4 digits of your Social Security Number": "".join(
                    [str(random.randint(0, 9)) for _ in range(4)]),
                'What is the month and day of your date of birth': "".join(
                    [str(random.randint(0, 9)) for _ in range(4)]),
                "Desired Salary": "120K",
                "Please select the boxes": "Any",
                "Are you willing to work": "Yes",
                "How did you hear about": ["LinkedIn", "Others"],
                "IMPORTANT: CANDIDATE'S AGREEMENT AND": "I agree",
            }
            self.question_select(wd, question_dict)
            self.click_calender(wd)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Take Assessment'):
            self.click_ele(wd, '[data-automation-id="inlineAssessmentButton"]')
            window_before = wd.window_handles[0]
            window_after = wd.window_handles[1]
            wd.switch_to.window(window_after)
            try:
                self.expected_shown_element(wd, '[class="screen-button"]')
                self.click_ele(wd, '[class="screen-button"]')
            except:
                self.expected_shown_element(wd, '[id="FirstName"]')
                first_name = wd.find_element(By.CSS_SELECTOR, '[id="FirstName"]')
                first_name.clear()
                first_name.send_keys(self.user_name.split()[0])
                last_name = wd.find_element(By.CSS_SELECTOR, '[id="LastName"]')
                last_name.clear()
                last_name.send_keys(self.user_name.split()[1])
                self.expected_shown_element(wd, '[id="Address"]')
                address = wd.find_element(By.CSS_SELECTOR, '[id="Address"]')
                address.clear()
                address.send_keys(self.user_address)
                zipcode = wd.find_element(By.CSS_SELECTOR, '[placeholder="Zip Code"]')
                zipcode.clear()
                zipcode.send_keys(self.user_zipcode)
                # Age check
                self.expected_shown_element(wd, '[id="AgeCheck"]')
                self.wait_x_sec(3)
                age_check = wd.find_element(By.CSS_SELECTOR, '[id="AgeCheck"]')
                age_check.find_elements(By.CSS_SELECTOR, 'button')[0].click()
                self.expected_shown_element(wd, '[id="DateOfBirth"]')
                dob = wd.find_element(By.CSS_SELECTOR, '[id="DateOfBirth"]').find_element(By.CSS_SELECTOR, 'input')
                if not dob.get_attribute('value'):
                    dob.send_keys("01012000")
                # previous employee
                self.wait_x_sec(3)
                self.expected_shown_element(wd, '[id="PreviousEmployed"]')
                previous_check = wd.find_element(By.CSS_SELECTOR, '[id="PreviousEmployed"]')
                previous_check.find_elements(By.CSS_SELECTOR, 'button')[1].click()
                # select group
                self.expected_shown_element(wd, '[id="SelectGroup"]')
                wd.find_element(By.CSS_SELECTOR, '[id="SelectGroup"]').find_element(By.CSS_SELECTOR, 'button').click()
                self.click(wd)
                self.click(wd)
                self.click_ele(wd, '[data-component="Continue-component"]')
                # additional questions
                self.wait_x_sec(4)
                try:
                    self.expected_shown_element(wd, '[id="BIGroupQuestion"]')
                    wd.find_element(By.CSS_SELECTOR, '[id="BIGroupQuestion"]').find_element(By.CSS_SELECTOR,
                                                                                            'button').click()
                    self.click(wd)
                    self.click(wd)
                except:
                    pass
                try:
                    self.click_ele(wd, '[data-component="Continue-component"]')
                    self.click(wd)
                    self.click(wd)
                except:
                    pass
                self.click_ele(wd, '[data-component="submit-component"]')
                self.expected_shown_element(wd, '[class="screen-button"]')
                self.click_ele(wd, '[class="screen-button"]')
            wd.switch_to.window(window_before)
        self.submit(wd)
        return self.apply_success(wd)
