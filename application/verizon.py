from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class verizonApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Do you have a valid passport": "Yes",
                "Are you currently authorized": "Yes",
                "Do you currently require sponsorship": self.user_visa_ans,
                "Will you require visa sponsorship": self.user_visa_ans,
                "Are you at least 18 years old": "Yes",
                "Do you have any family": "No",
                "Are you subject to any agreement": "No",
                "Are you currently employed": "No",
                "Do you have any family members": "No",
                "Will you need a visa": "I will need a visa" if self.user_visa == 'H1b' else "I am legally"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {
                "If you are applying to a job in the U.S., are you currently enrolled in an accredited Bachelor's, Master's, JD, or PhD program": "No",
                "Are you currently in good academic standing": "Yes",
                "Are you planning to attend any of the following conferences": "No",
                "Are you available to work full-time for this position": "Yes",
                "This position requires": "Yes",
                "Are you a current participant": "No"
            }
            self.question_select(wd, q_2_d)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please provide your Gender": "Male" if self.user_gender_male else "Female",
                "Please provide your veteran status": "I am NOT a Protected Veteran",
                "Gender": "Male" if self.user_gender_male else "Female",
                "Please provide your Race": "Asian"
            }
            self.question_select(wd, v_d)
            race = wd.find_element(By.CSS_SELECTOR, '[id="1d175767e01d1000aec0e3cb33900001"]')
            if not race.get_attribute("checked"):
                race.click()
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
