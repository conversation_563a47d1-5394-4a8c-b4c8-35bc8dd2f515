from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class walmartApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application question
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        except:
            pass
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Do you certify you meet all minimum qualifications": "Yes",
                "Would you like to receive mobile text": "Opt-Out",
                "Are you legally able to work in the country where": "Yes",
                "Please select your age category": "18 years of age and Over",
                "Please select your Walmart Associate": "Have never been",
                "Will you now or in the future require": self.user_visa_ans,
                "The following questions are to assist Walmart in determining your eligibility for": "No",
                "Have you already left Active Duty, or will you leave ": "No",
                "Do you have a direct family member who currently works for Walmart? select one required": "No",
                "If yes, please choose the type of sponsorship from the below list: select one required": "H1-B",
                "Do you have a direct family": "No"
            }
            self.question_select(wd, question_dict)
            additional_questions = {
                "If yes, please choose the type of sponsorship": "H1"
            }
            self.question_select(wd, additional_questions)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {
                "Have you been employed by a Government Entity within the last two years": "No",
                "Do you have another government position to disclose?": "No",
                "Do you have any family members who are": "No",
                "Do you have another government position to disclose": "No",
                "My availability is": "Either Full Time or Part Time"
            }
            self.question_select(wd, q_2_d)
            for text_area in wd.find_elements(By.CSS_SELECTOR, 'textarea'):
                if not text_area.text:
                    text_area.send_keys("Not Applicable")
            try:
                for ele in wd.find_elements(By.XPATH, '//*[starts-with(text(), "All Times Below Work for Me")]'):
                    a_id = ele.get_attribute("for")
                    if wd.find_element(By.CSS_SELECTOR, f'[id="{a_id}"]').get_attribute("aria-checked") == 'false':
                        self.click_ele(wd, f'[id="{a_id}"]')
            except:
                pass
            self.click_next_page(wd)
        # Voluntary disclosure
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            try:
                self.click_ele(wd, '[data-automation-id="ethnicityDropdown"]')
                self.click_ele(wd, '[data-value="3110e91abc93017796f909b3b5e7576c"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[data-automation-id="gender"]')
                if self.user_gender_male:
                    self.click_ele(wd, '[data-value="3110e91abc9301f0fe4d3baab6e7666c"]')
                else:
                    self.click_ele(wd, '[data-value="3110e91abc930174fe4d3baab6e7656c"]')
            except:
                pass
            self.click_agreement(wd)
            self.click_next_page(wd)
        # submit
        self.submit(wd)
        return self.apply_success(wd)
