from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class twilioApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Please indicate whether you are either": "No",
            'By clicking the "Acknowledge"': "Acknowledge",
            "Voluntary Self-Identification of Gender": "Male" if self.user_gender_male else "Female",
            "Disability Status": "No",
            "Voluntary Self-Identification of Race/Ethnicity": "Asian",
            "Voluntary Self-Identification of Sexual Orientation": "Hetero",
            "Protected Veteran Status": "I am not"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
