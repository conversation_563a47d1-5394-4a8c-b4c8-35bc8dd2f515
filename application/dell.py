from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class dellApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "By selecting YES": "Yes",
                "Are you legally authorized to work": "Yes",
                "Will you now or in the future require Dell sponsorship": self.user_visa_ans,
                "Do you interact with Dell Technologies Personnel": "No",
                "Is your current employer a reseller of Dell Technologies": "No",
                "Are Dell Technologies personnel": "No",
                "Are you subject to a non-compete agreement": "No",
                "Are you aware if your current employer": "No",
                "Are you a foreign national and / or citizen": "No",
                "Please confirm your preferred method": "Email",
                "By selecting YES, you are granting Dell": "Yes",
            }
            self.question_select(wd, question_dict)
            self.question_select(wd, {"Is the role you are applying for located": "Yes"})
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_d = {
                "Are you eligible to work": "Yes",
                "Do you now or in the future require immigration": self.user_visa_ans,
                "Are you at least 18 years old": "Yes",
                "Have you ever been involuntarily": "No",
                "I am currently or have been employed": "No",
                "Do you or your relative(s) own": "No",
                "Are you on a temporary work visa": "No"
            }
            self.question_select(wd, q_2_d)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
