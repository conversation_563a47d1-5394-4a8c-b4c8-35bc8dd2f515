from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class attApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Were you referred to this position by a current AT&T employee": "No",
                "Are you at least 18 years of age or older": "Yes",
                "Are you currently employed by AT&T or AT&T family of companies": "No",
                "Do you now or will you in the future require immigration": self.user_visa_ans,
                "Have you ever been or are you now an immediate military": "No",
                "Have you signed an employment agreement": "No",
                "Have you ever been assigned to work at AT&T ": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            q_2_dict = {
                "Tell us how much": "3 - 5 Years",
                "In order to be considered": "Yes",
                "Are you willing": "Yes",
                "Do your sales skills": "Yes",
                "Have you had supervisory experience": "Yes",
            }
            self.question_select(wd, q_2_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.self_identify(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith("Take Assessment"):
            self.click_ele(wd, '[data-automation-id="inlineAssessmentButton"]')
            window_before = wd.window_handles[0]
            window_after = wd.window_handles[1]
            wd.switch_to.window(window_after)
            try:
                self.expected_shown_element(wd, '[id="ctl00_ContentHolder_btnRedirectWorkDay"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_btnRedirectWorkDay"]')
            except:
                self.click_ele(wd, '[id="ctl00_ContentHolder_btnTakeSurvey"]')
                self.expected_shown_element(wd, '[name="ctl00$ContentHolder$txtZipCode"]')
                wd.find_element(By.CSS_SELECTOR, '[name="ctl00$ContentHolder$txtZipCode"]').send_keys(self.user_zipcode)
                self.click_ele(wd, '[value="Continue"]')
                self.expected_shown_element(wd, '[value="REHIREN"]')
                self.click_ele(wd, '[value="REHIREN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCUNEMPLOYED27N"]')
                self.click_ele(wd, '[value="WOTCUNEMPLOYED27N"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCTANF2YRN"]')
                self.click_ele(wd, '[value="WOTCTANF2YRN"]')
                self.click_ele(wd, '[value="WOTCTANFN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCFOODSTAMPSNAPN"]')
                self.click_ele(wd, '[value="WOTCFOODSTAMPSNAPN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPSNAPFAMILYN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPN"]')
                self.wait_x_sec(1)
                self.click_ele(wd, '[value="WOTCFOODSTAMPFAMILYN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCVETERANN"]')
                self.click_ele(wd, '[value="WOTCVETERANN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCVOCREHABN"]')
                self.click_ele(wd, '[value="WOTCVOCREHABN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.expected_shown_element(wd, '[value="WOTCSSIN"]')
                self.click_ele(wd, '[value="WOTCSSIN"]')
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
                self.wait_x_sec(2)
                self.click_ele(wd, '[id="ctl00_ContentHolder_NextPageButton"]')
            wd.switch_to.window(window_before)
        self.submit(wd)
        return self.apply_success(wd)
