from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class usbankApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you currently authorized for employment in the U.S. without any": "No" if self.user_visa == 'H1b' else "Yes",
                "Will you now or in the future require visa sponsorship by U.S. Bank": self.user_visa_ans,
                "Are you subject to an order of prohibition from banking": "No",
                "Do you have any obligations to a previous employer": "No",
                "I understand that as a condition of new or continued employment": "Yes",
                "If at any time during my employment U.S. Bank is unable to obtain": "Yes",
                "Desired base compensation": "$110,000-$129,999",
                "Have you ever received a conditional job": "No",
                "Are you 18": "Yes",
                "If you have ever been discharged": "N/A",
                "Do you have an immediate family member": "No"
            }
            self.question_select(wd, question_dict)
            try:
                ans = wd.find_element(By.CSS_SELECTOR, 'textarea')
                if not ans.text:
                    ans.send_keys("N/A")
            except:
                pass
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            mapping_d = {
                "Bachelor": "4-",
                "Master": "Master",
                "PhD": "Doctoral"
            }
            self.question_select(wd, {
                "Indicate the highest level of education": mapping_d[self.user_degree]})
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
