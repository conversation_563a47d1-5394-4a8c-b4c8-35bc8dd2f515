from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class spotifyApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        self.hide.hideElement(wd, '[id="onetrust-consent-sdk"]')
        try:
            self.expected_shown_element(wd, '[aria-label="Apply now"]')
        except:
            if wd.find_element(By.CSS_SELECTOR, 'h1').text.startswith("OMG"):
                self.database.delete(f"link='{url}'")
                return -4
        apply_btn = list(
            filter(lambda x: x.text.startswith('Apply now'), wd.find_elements(By.CSS_SELECTOR, 'p')))[0]
        self.click_ele(wd, apply_btn)
        self.expected_shown_element(wd, '[id="basics"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.click_ele(wd, '[aria-label="Full name*"]')
        wd.find_element(By.CSS_SELECTOR, '[aria-label="Full name*"]').send_keys(self.user_name)
        wd.find_element(By.CSS_SELECTOR, '[aria-label="Email*"]').send_keys(self.user_account)
        self.click_ele(wd, '[aria-label="Phone*"]')
        wd.find_element(By.CSS_SELECTOR, '[aria-label="Phone*"]').send_keys(self.user_phone)
        wd.find_element(By.CSS_SELECTOR, '[aria-label="Current company*"]').send_keys(self.user_company)
        self.click_ele(wd, '[aria-label="Current location*"]')
        wd.find_element(By.CSS_SELECTOR, '[aria-label="Current location*"]').send_keys(
            self.user_city + ', ' + self.user_state + ', ' + 'United States')
        self.click_ele(wd, '[aria-label="LinkedIn"]')
        wd.find_element(By.CSS_SELECTOR, '[aria-label="LinkedIn"]').send_keys(self.user_linkedin)
        try:
            self.expected_shown_element(wd, '[id="deeper"]')
            self.click_ele(wd, '[id="deeper"]')
            question_deeper = wd.find_element(By.ID, 'deeper')
            for question in question_deeper.find_elements(By.CSS_SELECTOR,
                                                          '[role="radiogroup"]') + question_deeper.find_elements(
                By.CSS_SELECTOR, '[role="group"]'):
                self.click_ele(wd, f'[id="{question.get_attribute("id")}"]')
                try:
                    answer_btn = question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]')
                    if not answer_btn:
                        raise
                    question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                except:
                    answer_btn = question.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                    question_text = question.find_element(By.CSS_SELECTOR, 'span').text
                if question_text.startswith('Are you currently based in the United States') or question_text.startswith(
                        "Have you worked with microservice") or question_text.startswith(
                    "Are you physically located") or question_text.startswith("I have 3+ years"):
                    ans = next(
                        filter(lambda x: x.find_element(By.XPATH, '..').text == 'Yes', answer_btn)).find_element(
                        By.XPATH, 'following-sibling::div')
                    self.click_ele(wd, ans)
                elif question_text.startswith(
                        "Would you now or in the future require work authorization") or question_text.startswith(
                    "Do you now or in the future require"):
                    ans = next(
                        filter(lambda x: x.find_element(By.XPATH, '..').text == self.user_visa_ans,
                               answer_btn)).find_element(
                        By.XPATH, 'following-sibling::div')
                    self.click_ele(wd, ans)
                elif question_text.startswith("How many years"):
                    ans = next(
                        filter(lambda x: x.find_element(By.XPATH, '..').text.startswith("3"), answer_btn)).find_element(
                        By.XPATH, 'following-sibling::div')
                    self.click_ele(wd, ans)
                elif question_text.startswith("Are you proficient in the following"):
                    ans = list(filter(lambda x: not x.find_element(By.XPATH, '..').text.startswith('None'), answer_btn))
                    for btn in ans:
                        self.click_ele(wd, btn.find_element(By.XPATH, 'following-sibling::div'))
                elif question_text.startswith("I am looking for a data science role that focuses"):
                    ans = list(
                        filter(lambda x: not x.find_element(By.XPATH, '..').text.startswith('Business'), answer_btn))
                    for btn in ans:
                        self.click_ele(wd, btn.find_element(By.XPATH, 'following-sibling::div'))
                else:
                    self.logger.error(
                        f"Company: {self.company}, Question: {question_text}, "
                        f"Answer: {[x.find_element(By.XPATH, '..').text for x in answer_btn]}")
        except:
            pass
        self.click_ele(wd, '[aria-label="Submit application"]')
        try:
            self.expected_shown_element(wd, '[class="row text center"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="row text center"]').text.startswith(
                    "Thanks for applying"):
                return 1
        except:
            try:
                oops = wd.find_elements(By.XPATH, '//h1[starts-with(@class, "size-4 size-4-mobile")]')
                if len(oops) == 1 and oops[0].text.startswith("Oops"):
                    return 1
            except:
                return 0
