from abc import ABC
from datetime import datetime

from module.applicationInterfaceModule import applicationInterface as applyInterface


class sofiApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Will you now or in the future require": self.user_visa_ans,
            "Have you worked at or been a consultant for SoFi": "No",
            "Would you like to receive marketing communications": "No",
            "Are you authorized to lawfully work in the country": "Yes",
            "I acknowledge that by providing my phone number": "Yes",
            "Are you currently employed with or have been employed by Deloitte": "No",
            "Are you currently a SoFi, Galileo or Technisys employee": "No",
            "Date of Application": datetime.now().strftime("%m/%d/%Y"),
            "What FINRA license": "N/A",
            "Do you currently hold, or intend to hold": "No",
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
