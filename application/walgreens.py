# -*- coding: utf-8 -*-
# @Time  : 2/24/23 22:53
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: walgreens.py
# @Software: PyCharm
import datetime
from abc import ABC

from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class walgreensApply(applyInterface, ABC):
    def click_till_done(self, wd, times):
        try:
            flag = True if wd.find_element(By.CSS_SELECTOR,
                                           '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]') else False
            idx = 0
            while flag and idx < times:
                self.wait_x_sec(3)
                try:
                    date = wd.find_element(By.CSS_SELECTOR, '[placeholder="m/d/yyyy"]')
                    date.clear()
                    date.send_keys(datetime.datetime.now().strftime("%m/%d/%Y"))
                except:
                    pass
                try:
                    name = wd.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    if not name.text:
                        name.send_keys(self.user_name)
                except:
                    pass
                try:
                    wd.find_elements(By.CSS_SELECTOR, 'input[role="radio"]')[-1].click()
                except:
                    pass
                self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
                self.wait_x_sec(3)
                flag = True if wd.find_element(By.CSS_SELECTOR,
                                               '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]') else False
                idx += 1
        except:
            pass

    def applyIt(self, url, wd):
        wd.get(url)
        if self.is_page_not_found(wd.title) or wd.current_url == "https://jobs.walgreens.com/en":
            return -4
        try:
            self.hide.hideHeadAndFoot(wd)
        except:
            pass
        self.expected_shown_element(wd, '[data-selector-name="job-apply-link"]')
        self.click_ele(wd, '[data-selector-name="job-apply-link"]')
        # To login
        try:
            self.expected_shown_element(wd, '[name="loginField"]')
            wd.find_element(By.CSS_SELECTOR, '[name="loginField"]').send_keys(self.user_account)
            wd.find_element(By.CSS_SELECTOR, '[name="password"]').send_keys(self.user_pwd)
            self.click_ele(wd, '[id="btnLogin"]')
        except:
            pass
        # user account or password error
        try:
            self.expected_shown_element(wd, '[class="msgContainer BorderError"]')
            if wd.find_element(By.CSS_SELECTOR, '[class="msgContainer BorderError"]'):
                return -1
        except:
            pass
        try:
            wd.find_element(By.CSS_SELECTOR, '[class="ReApplyJobAPI"]')
            return 1
        except:
            pass
        try:
            # Agree
            self.wait_x_sec(3)
            self.click_ele(wd, '[class="button-label ng-binding"]')
        except:
            pass
        # bad password
        try:
            self.expected_shown_element(wd, '[class="badPassword ng-binding"]')
            return -1
        except:
            pass
        # To start
        self.wait_x_sec(3)
        self.click_ele(wd, '[class="primaryButton ladda-button ng-scope"]')
        # Employee Status Verification
        self.wait_x_sec(4)
        wd.find_elements(By.CSS_SELECTOR, 'input[role="radio"]')[-1].click()
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Contact Details
        self.wait_x_sec(4)
        for indicator in wd.find_elements(By.CSS_SELECTOR, '[class="requiredFieldIndicator"]'):
            question = indicator.find_element(By.XPATH, '..')
            try:
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
            except:
                continue
            try:
                ans = question.find_element(By.CSS_SELECTOR, 'input')
            except:
                continue
            if question_text.startswith("First name") or question_text.startswith("Preferred name"):
                ans.clear()
                ans.send_keys(self.user_name.split()[0])
            elif question_text.startswith("Last name"):
                ans.clear()
                ans.send_keys(self.user_name.split()[1])
            elif question_text.startswith("City"):
                ans.clear()
                ans.send_keys(self.user_city)
            elif question_text.startswith("State/Region/Province"):
                ans.clear()
                ans.send_keys(self.user_state)
                self.wait_x_sec(2)
                ActionChains(wd).move_to_element(ans).move_by_offset(0, 35).double_click().perform()
            elif question_text.startswith("Zip/Postal code"):
                ans.clear()
                ans.send_keys(self.user_zipcode)
            elif question_text.startswith("Primary Phone"):
                ans.clear()
                ans.send_keys(self.user_phone)
            elif question_text.startswith("Address line 1"):
                ans.clear()
                ans.send_keys(self.user_address)
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Resume
        self.wait_x_sec(4)
        self.click_ele(wd, '[id="AddResumeLink"]')
        self.expected_shown_element(wd, 'iframe')
        wd.switch_to.frame(
            wd.find_element(By.CSS_SELECTOR, '[aria-labelledby="iframeDialogTitle"]').find_element(By.CSS_SELECTOR,
                                                                                                   'iframe'))
        self.click_ele(wd, '[class="fileUpload primaryButton uploadButtonStyle"]')
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        wd.switch_to.default_content()
        self.wait_x_sec(3)
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Getting Started
        self.wait_x_sec(4)
        wd.find_elements(By.CSS_SELECTOR, 'input[role="radio"]')[-1].click()
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # application question
        self.wait_x_sec(4)
        for indicator in wd.find_elements(By.CSS_SELECTOR, '[class="requiredFieldIndicator"]'):
            question = indicator.find_element(By.XPATH, '..')
            try:
                question_text = question.find_element(By.CSS_SELECTOR, 'label').text
            except:
                continue
            if question_text.startswith("Are you") or question_text.startswith("Do you"):
                ans = question.find_elements(By.CSS_SELECTOR, 'input[role="radio"]')[0]
                self.click_ele(wd, ans)
            elif "sponsorship" in question_text:
                ans = question.find_elements(By.CSS_SELECTOR, 'input[role="radio"]')[
                    0 if self.user_visa == 'H1b' else -1]
                self.click_ele(wd, ans)
            elif "salary" in question_text:
                ans = question.find_element(By.CSS_SELECTOR, 'input')
                ans.clear()
                ans.send_keys("120K")
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Source Information
        self.wait_x_sec(4)
        self.click_ele(wd, '[class="ui-selectmenu-text"]')
        self.expected_shown_element(wd, '[class="ui-menu-item"]')
        ans = wd.find_elements(By.CSS_SELECTOR, '[class="ui-menu-item"]')[-1]
        self.click_ele(wd, ans)
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Work Opportunity Tax Credit (WOTC)
        self.wait_x_sec(4)
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Work Opportunity Tax Credit (WOTC)
        self.wait_x_sec(4)
        try:
            self.click_ele(wd, '[name="wotc_1_0_MOREONWOTC_chk_0"]')
        except:
            pass
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Work Opportunity Tax Credit (WOTC)
        self.wait_x_sec(4)
        try:
            self.click_ele(wd, '[name="wotc_1_0_FIRSTOPTOUT_chk_0"]')
        except:
            pass
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        # Work Opportunity Tax Credit (WOTC)
        self.wait_x_sec(4)
        try:
            self.click_ele(wd, '[name="wotc_4_0_OPTOUT_chk_0"]')
        except:
            pass
        self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        self.wait_x_sec(3)
        self.click_till_done(wd, 5)
        # EEO Information
        try:
            for indicator in wd.find_elements(By.CSS_SELECTOR, '[class="requiredFieldIndicator"]'):
                question = indicator.find_element(By.XPATH, '..')
                try:
                    question_text = question.find_element(By.CSS_SELECTOR, 'label').text
                except:
                    continue
                if question_text.startswith("Sex Assigned at Birth"):
                    self.click_ele(wd, question.find_element(By.CSS_SELECTOR, '[role="listbox"]'))
                    self.expected_shown_element(wd, '[class="ui-menu-item"]')
                    ans = wd.find_elements(By.CSS_SELECTOR, '[class="ui-menu-item"]')[2 if self.user_gender_male else 1]
                    self.click_ele(wd, ans)
                elif question_text.startswith("Please select the race/ethnicity"):
                    self.click_ele(wd, question.find_element(By.CSS_SELECTOR, '[role="listbox"]'))
                    self.expected_shown_element(wd, '[class="ui-menu-item"]')
                    ans = wd.find_elements(By.CSS_SELECTOR, '[class="ui-menu-item"]')[8]
                    self.click_ele(wd, ans)
            self.click_ele(wd, '[class="primaryButton btn btn-large btn-primary ladda-button ng-scope"]')
        except:
            pass
        self.click_till_done(wd, 5)
        # submit
        self.wait_x_sec(3)
        self.click_ele(wd, '[class="primaryButton ladda-button ng-scope"]')
        self.wait_x_sec(4)
        return 1
