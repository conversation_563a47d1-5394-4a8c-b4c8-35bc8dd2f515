from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class cignaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you authorized (or eligible) to work lawfully": "No" if self.user_visa == "H1b" else "Yes",
                "Are you only temporarily authorized (or eligible)": self.user_visa_ans,
                "Do you have any agreements or contracts": "No",
                "If you become employed by The Cigna Group": "No",
                "Please answer": "No",
                "Do you have a close friend or relative": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions 2'):
            question_dict = {
                "Do you have a close family member": "No"
            }
            self.question_select(wd, question_dict)
            self.click_next_page(wd)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            disclosures_dict = {
                "Please indicate your veteran status": "I am not a veteran",
                "Please select your gender": "Male" if self.user_gender_male else "Female",
                "Please select your race": "Asian"
            }
            self.question_select(wd, disclosures_dict)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
