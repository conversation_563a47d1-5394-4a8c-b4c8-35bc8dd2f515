# -*- coding: utf-8 -*-
# @Time  : 6/18/23 13:26
# <AUTHOR> <PERSON><PERSON><PERSON>
# @FileName: zillow.py
# @Software: PyCharm
from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from module.workdayModule import workdayModule as applyInterface


class zillowApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        self.my_exp_page(wd)
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            try:
                self.click_ele(wd, '[data-automation-id="577a8ce98ca20143935a19096d1baa75"]')
                self.click_ele(wd, '[data-value="3a5415494ea901525133af7b00971435"]')
                self.click_ele(wd, '[data-automation-id="577a8ce98ca2011803b319096d1bad75"]')
                if self.user_visa == "H1b":
                    self.click_ele(wd, '[data-value="3a5415494ea901b1bfdfca7b00973635"]')
                else:
                    self.click_ele(wd, '[data-value="3a5415494ea901fb7de6ca7b00973735"]')
            except:
                q_d = {"Are you legally authorized to work in Mexico": "No"}
                self.question_select(wd, q_d)
            self.click_next_page(wd)
        self.wait_x_sec(5)
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            try:
                self.click_agreement(wd)
            except:
                pass
            self.click_next_page(wd)
        self.submit(wd)
        return self.apply_success(wd)
