from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class asanaApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split("/")[-1]
        url = f"https://job-boards.greenhouse.io/embed/job_app?for=asana&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you been employed": "No",
            "As<PERSON> is seeking the below information": "No"
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
