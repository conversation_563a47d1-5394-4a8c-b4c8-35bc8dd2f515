from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class ripplingApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, 'h4')
            if wd.find_element(By.CSS_SELECTOR, 'h4').text == "The page you're looking for doesn't exist":
                self.database.delete(f"link = '{url}'")
                return -4
        except:
            pass
        self.hide.hideHeadAndFoot(wd)
        self.hide.hideElement(wd, '[id="transcend-consent-manager"]')
        self.click_ele(wd, '[data-testid="Apply now"]')
        self.expected_shown_element(wd, '[data-input="first_name"]')
        # roll to the bottom of the page
        wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.wait_x_sec(5)
        first_name = wd.find_element(By.CSS_SELECTOR, '[data-input="first_name"]')
        if first_name.get_attribute("value") == "":
            first_name.send_keys(self.user_name.split()[0])
        last_name = wd.find_element(By.CSS_SELECTOR, '[data-input="last_name"]')
        if last_name.get_attribute("value") == "":
            last_name.send_keys(self.user_name.split()[1])
        email = wd.find_element(By.CSS_SELECTOR, '[data-input="email"]')
        if email.get_attribute("value") == "":
            email.send_keys(self.user_account)
        try:
            company = wd.find_element(By.CSS_SELECTOR, '[data-input="current_company"]')
            if company.get_attribute("value") == "":
                company.send_keys(self.user_company)
        except:
            pass
        phone = wd.find_element(By.CSS_SELECTOR, '[data-input="phone_number"]')
        if phone.get_attribute("value") == "":
            phone.send_keys(self.user_phone)
        try:
            location_input = wd.find_element(By.CSS_SELECTOR, '[data-input="location"]')
            if location_input.get_attribute("value") == "":
                location_input.send_keys(self.user_city)
        except:
            loc = wd.find_element(By.CSS_SELECTOR, '[data-testid="location"]').find_element(By.CSS_SELECTOR,
                                                                                            'input')
            if loc.get_attribute("value") == '':
                for e in ", ".join([self.user_city, self.user_state]):
                    loc.send_keys(e)
                self.expected_shown_element(wd, 'li[role="option"]')
                ans = wd.find_element(By.CSS_SELECTOR, 'li[role="option"]').get_attribute("id")
                self.click_ele(wd, f'[id="{ans}"]')
        try:
            linkedin = wd.find_element(By.CSS_SELECTOR, '[data-input="linkedin_link"]')
            if linkedin.get_attribute("value") == '':
                linkedin.send_keys(self.user_linkedin)
        except:
            pass
        try:
            for question in wd.find_elements(By.XPATH, '//*[starts-with(@data-testid, "custom_question")]'):
                question_text = question.find_element(By.XPATH, '../../..').text
                if question_text.startswith("Do you live in"):
                    q_id = question.find_element(By.CSS_SELECTOR, '[aria-haspopup="listbox"]').get_attribute("id")
                    self.click_ele(wd, f'[id="{q_id}"]')
                    self.expected_shown_element(wd, f'[id="{q_id}-list"]')
                    self.click_ele(wd, f'[id="{q_id}-list-option-0"]')
                elif question_text.startswith("Which of the following languages"):
                    q_class = next(x for x in question.find_elements(By.CSS_SELECTOR, 'div[role="checkbox"]') if
                                   "English" in x.text).get_attribute("class")
                    self.click_ele(wd, f'[class="{q_class}"]')
                elif question_text.startswith("Are you located in"):
                    q_class = question.find_element(By.CSS_SELECTOR, 'div[role="checkbox"]').find_element(
                        By.CSS_SELECTOR,
                        'div').get_attribute(
                        'class')
                    self.click_ele(wd, f'[class="{q_class}"]')
                elif question_text.startswith("Are you willing"):
                    q_id = question.find_element(By.CSS_SELECTOR, 'div[role="combobox"]').get_attribute("id")
                    self.click_ele(wd, f'[id="{q_id}"]')
                    self.expected_shown_element(wd, f'[id="{q_id}-list"]')
                    self.click_ele(wd, f'[id="{q_id}-list-option-0"]')
                else:
                    self.logger.error(f"Question: {question_text}")
        except:
            pass
        self.wait_x_sec(3)
        self.click_ele(wd, '[data-testid="Apply"]')
        try:
            self.expected_shown_element(wd, '[data-testid="action-card"]')
            self.expected_shown_element(wd, 'h4')
            return 1
        except:
            return 0
