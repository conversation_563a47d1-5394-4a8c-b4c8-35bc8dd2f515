from abc import ABC

from module.applicationInterfaceModule import applicationInterface as applyInterface


class datadogApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        j_id = url.split('=')[-1]
        url = f"https://job-boards.greenhouse.io/embed/job_app?for=datadog&token={j_id}"
        wd.get(url)
        res = self.greenhouse_info(wd, url)
        if res != 1: return res
        question_dict = {
            "Have you installed software on Linux or done any configurations?": "Yes, I have installed software on Linux or done any configurations",
            "What programming or scripting languages are you experienced with": "Python, Java, TypeScript, Golang",
            "Are you comfortable": "Yes",
            "By submitting my resume, I agree": "Yes",
            "I understand my application": "Yes",
            "Are you open": "Yes",
            "Datadog is operating on a hybrid work": "Yes",
            "Have you built": "Yes",
            "Have your worked": "Yes",
            "Are you willing": "Yes",
            "If you are not currently": "Yes",
            "Do you have": "Yes",
            "I certify that": "Yes",
            "When is": "ASAP",
            "How did you hear about this opportunity": "LinkedIn",
            "Have you spoken to your current manager": "No",
            "Which office location are you applying to": "Open To New York And Boston",
            "What is your preferred name": self.user_name,
            "In what cities are you available": "Seattle",
            "Are you legally authorised to work": "Yes, but I will need sponsorship in the future." if self.user_visa == 'H1b' else "Yes, no restriction.",
            "Please select all the languages": "Mandarin",
            "Team/Manager": "N/A",
            "In a few sentences": "N/A",
            "In what location(s)": self.user_city + ", " + self.user_state,
            "I certify that the information": "Yes",
            "I understand": "Yes",
        }
        self.greenhouse_question_answer_new(wd, question_dict)
        return self.greenhouse_submit(wd)
