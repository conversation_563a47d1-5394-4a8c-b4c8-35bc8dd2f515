from abc import ABC
from typing import Dict

from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from module.applicationInterfaceModule import applicationInterface as applyInterface


class waymoApply(applyInterface, ABC):

    def question_select(self, wd, apply_wd, question_dict: Dict):
        questions = [x for x in apply_wd.find_elements(By.CLASS_NAME, "form-group")]
        for q in questions[6:]:
            question_text = q.text.strip()
            if "required" not in question_text:
                continue
            answer = question_dict[next(filter(lambda x: question_text.startswith(x), question_dict.keys()))]
            if "dropdown" in q.get_attribute("class"):
                select = q.find_element(By.TAG_NAME, "select")
                option = next(filter(lambda x: x.text.strip().startswith(answer),
                                     select.find_elements(By.TAG_NAME, "option")))
                Select(select).select_by_value(option.get_attribute("value"))
            elif "check-box" in q.get_attribute("class"):
                checkboxes = q.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
                for cb in checkboxes:
                    if cb.find_element(By.XPATH, '..').text.startswith(answer):
                        if not cb.is_selected():
                            value = cb.get_attribute("value")
                            self.click_ele(wd, f'[value="{value}"]')
                        break
            else:
                self.logger.error(f"Company: {self.company.title()}, Question: {question_text}")

    def applyIt(self, url, wd):
        wd.get(url + "#apply")

        try:
            self.expected_shown_element(wd, '[id="consent_reject"]')
            self.click_ele(wd, '[id="consent_reject"]')
        except:
            pass
        self.expected_shown_element(wd, '[data-candidate-field="candidate_first_name"]')

        apply_wd = wd.find_element(By.CSS_SELECTOR, '[class="call-to-action apply_url-call-to-action"]')

        apply_wd.find_element(By.CSS_SELECTOR, 'input[data-candidate-field="candidate_first_name"]').send_keys(
            self.user_name.split()[0])
        apply_wd.find_element(By.CSS_SELECTOR, 'input[data-candidate-field="candidate_last_name"]').send_keys(
            self.user_name.split()[1])
        apply_wd.find_element(By.CSS_SELECTOR, '[data-candidate-field="candidate_email"]').send_keys(self.user_account)
        apply_wd.find_element(By.CSS_SELECTOR, '[data-candidate-field="candidate_phone"]').send_keys(self.user_phone)
        apply_wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)

        question_dict = {
            "Work Authorization": "I am authorized to work" if self.user_visa != 'H1b' else "I require",
            "Please review and acknowledge": "I acknowledge",
            "Are you a current or former Alphabet employee": "Never",
            "Gender": "Man" if self.user_gender_male else "Woman",
            "Race/Ethnicity": "Asian",
            "Are you a veteran": "No",
            "Do you identify as a member of the LGBTQ": "No"
        }
        self.question_select(wd, apply_wd, question_dict)
        submit_id = apply_wd.find_element(By.CSS_SELECTOR, '[class="submit-state submit-start"]').find_element(By.XPATH,
                                                                                                               '..').get_attribute(
            "id")
        self.click_ele(wd, '[id="' + submit_id + '"]')
        try:
            self.expected_shown_element(wd, '[for="code"]')
            verification_code = self.get_verification_from_gmail("Candidate Verification")
            if verification_code:
                apply_wd.find_element(By.CSS_SELECTOR, 'input[name="code"]').send_keys(verification_code)
                submit_id = apply_wd.find_elements(By.CSS_SELECTOR, '[class="submit-state submit-start"]')[
                    -1].find_element(
                    By.XPATH, '..').get_attribute("id")
                self.click_ele(wd, '[id="' + submit_id + '"]')
        except:
            pass
        try:
            self.wait_x_sec(3)
            self.expected_shown_element(wd, '[class="call-to-action-get"]')
            if any("Thank you" in x.text for x in wd.find_elements(By.CLASS_NAME, "call-to-action-get")):
                return 1
        except:
            return 0
