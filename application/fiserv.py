from abc import ABC

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from module.workdayModule import workdayModule as applyInterface


class fiservApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        check = self.check_condition(wd, url)
        if check != 0:
            return check
        # My info
        self.my_info_page(wd)
        # My Experience
        self.my_exp_page(wd)
        # Application questions
        WebDriverWait(wd, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Application Questions')]")))
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Application Questions'):
            question_dict = {
                "Are you of legal age to work": "Yes",
                "Are you legally authorized": "Yes",
                "Will you now or in the future require sponsorship": self.user_visa_ans,
                "Do you now or have you ever worked": "No",
                "May we contact you via text": "No",
                "Are you a current or former United States Military": "No",
                "Military Branch of Service": "Not",
                "Are you a current or former military spouse": "No",
                "Are you willing to relocate": "Yes",
                "Are you currently working for": "No",
                "Have you ever worked for": "No",
                "Have you ever been employed by": "No",
                "Are you willing to work on-site": "Yes",
                "Are you currently within commuting distance": "Yes"
            }
            self.question_select(wd, question_dict)
            self.question_select(wd, {"I hereby certify that": "Agree",
                                      "Are you a current or former United States Military": "No"})
            self.click_calender(wd)
            self.click_next_page(wd)
        self.wait_x_sec(4)
        # Voluntary disclosure
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Voluntary Disclosures')]")))
        except:
            pass
        try:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
        except:
            title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
        if title.startswith('Voluntary Disclosures'):
            v_d = {
                "Please select the option which best defines your gender": "Male" if self.user_gender_male else "Female",
                "What is your race": "Asian",
                "What is your Veteran": "I AM NOT"
            }
            self.question_select(wd, v_d)
            self.click_agreement(wd)
            self.click_next_page(wd)
        self.self_identify(wd)
        try:
            WebDriverWait(wd, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h2[starts-with(text(), 'Take Assessment')]")))
            try:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1ylcaf3"]').text
            except:
                title = wd.find_element(By.CSS_SELECTOR, '[class="css-1j9bnzb"]').text
            if title.startswith("Take Assessment"):
                self.click_ele(wd, '[data-automation-id="inlineAssessmentButton"]')
                window_before = wd.window_handles[0]
                window_after = wd.window_handles[1]
                wd.switch_to.window(window_after)
                self.expected_shown_element(wd, '[value="Opt Out"]')
                self.click_ele(wd, '[value="Opt Out"]')
                wd.switch_to.window(window_before)
        except:
            pass
        # submit
        self.submit(wd)
        return self.apply_success(wd)
